<?xml version="1.0" encoding="utf-8"?>
<!-- 
  Copyright (c) Microsoft Corporation. All rights reserved.
 Licensed under the MIT License.. 
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <RunAutolinkCheck Condition="'$(RunAutolinkCheck)' == ''">false</RunAutolinkCheck>
    <AutolinkCommand Condition="'$(AutolinkCommand)' == ''">npx react-native autolink-windows</AutolinkCommand>
    <AutolinkCommandWorkingDir Condition="'$(AutolinkCommandWorkingDir)' == ''">$([MSBuild]::GetDirectoryNameOfFileAbove($(ProjectDir), 'package.json'))</AutolinkCommandWorkingDir>
    <AutolinkCommandArgs Condition="'$(AutolinkCommandArgs)' == '' And '$(SolutionPath)' != '' And '$(ProjectPath)' != ''">--check --sln "$([MSBuild]::MakeRelative($(AutolinkCommandWorkingDir), $(SolutionPath)))" --proj "$([MSBuild]::MakeRelative($(AutolinkCommandWorkingDir), $(ProjectPath)))"</AutolinkCommandArgs>
    <AutolinkCommandArgs Condition="'$(AutolinkCommandArgs)' == ''">--check</AutolinkCommandArgs>
  </PropertyGroup>

</Project>
