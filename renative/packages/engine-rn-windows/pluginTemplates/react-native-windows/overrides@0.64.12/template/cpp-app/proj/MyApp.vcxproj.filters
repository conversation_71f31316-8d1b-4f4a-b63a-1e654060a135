﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="App.idl" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="App.cpp" />
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
    <ClCompile Include="ReactPackageProvider.cpp" />
    <ClCompile Include="AutolinkedNativeModules.g.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="App.h" />
    <ClInclude Include="ReactPackageProvider.h" />
    <ClInclude Include="AutolinkedNativeModules.g.h" />
  </ItemGroup>
  <ItemGroup>
   <Image Include="Assets\Wide310x150Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallTile.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallTile.scale-125.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallTile.scale-150.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallTile.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallTile.scale-400.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.scale-125.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.scale-150.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.scale-400.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Wide310x150Logo.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Wide310x150Logo.scale-125.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Wide310x150Logo.scale-150.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Wide310x150Logo.scale-400.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\LargeTile.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\LargeTile.scale-125.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\LargeTile.scale-150.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\LargeTile.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\LargeTile.scale-400.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.scale-125.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.scale-150.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.scale-400.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-16.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-24.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-32.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-48.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-256.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-16.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-32.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-48.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-256.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-16.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-24.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-32.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-48.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-256.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-125.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-150.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-400.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.scale-125.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.scale-150.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.scale-400.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Assets">
      <UniqueIdentifier>{e48dc53e-40b1-40cb-970a-f89935452892}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="PropertySheet.props" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="readme.txt" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainPage.xaml" />
  </ItemGroup>
   <!-- Only add Fonts as dependencies if vector icons package is installed as a plugin -->
    {{#hasAdditionalAssets }}
   <ItemGroup>
    <Font Include="Assets\AntDesign.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\Entypo.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\EvilIcons.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\Feather.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\FontAwesome.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\FontAwesome5_Brands.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\FontAwesome5_Regular.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\FontAwesome5_Solid.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\Fontisto.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\Foundation.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\Ionicons.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\MaterialCommunityIcons.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\MaterialIcons.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\Octicons.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\SimpleLineIcons.ttf">
      <Filter>Assets</Filter>
    </Font>
    <Font Include="Assets\Zocial.ttf">
      <Filter>Assets</Filter>
    </Font>
  </ItemGroup>
   {{/hasAdditionalAssets }}
</Project>