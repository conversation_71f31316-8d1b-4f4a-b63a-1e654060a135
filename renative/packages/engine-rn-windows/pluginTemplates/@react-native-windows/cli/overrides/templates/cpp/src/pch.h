﻿#pragma once

#define NOMINMAX

#include <hstring.h>
#include <restrictederrorinfo.h>
#include <unknwn.h>
#include <windows.h>
#include <winrt/Windows.ApplicationModel.Activation.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Windows.UI.ViewManagement.h>
#include <winrt/Windows.Foundation.h>
#include <winrt/{{ xamlNamespace }}.Controls.Primitives.h>
#include <winrt/{{ xamlNamespace }}.Controls.h>
#include <winrt/{{ xamlNamespace }}.Data.h>
#include <winrt/{{ xamlNamespace }}.Interop.h>
#include <winrt/{{ xamlNamespace }}.Markup.h>
#include <winrt/{{ xamlNamespace }}.Navigation.h>
#include <winrt/{{ xamlNamespace }}.h>

#include <winrt/Microsoft.ReactNative.h>

#include <winrt/Microsoft.UI.Xaml.Automation.Peers.h>
#include <winrt/Microsoft.UI.Xaml.Controls.Primitives.h>
#include <winrt/Microsoft.UI.Xaml.Controls.h>
#include <winrt/Microsoft.UI.Xaml.Media.h>
#include <winrt/Microsoft.UI.Xaml.XamlTypeInfo.h>