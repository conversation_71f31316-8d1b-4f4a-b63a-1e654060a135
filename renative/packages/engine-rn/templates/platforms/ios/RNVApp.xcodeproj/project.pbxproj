// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		1B8FC1667891302063944D4D /* libPods-RNVApp-RNVAppTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96722C6F859B298386E53EEC /* libPods-RNVApp-RNVAppTests.a */; };
		200132FC1F6BF9CF00450340 /* RNVApp.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = 200132FA1F6BF9CF00450340 /* RNVApp.xcdatamodeld */; };
		200132FE1F6BF9CF00450340 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 200132FD1F6BF9CF00450340 /* Assets.xcassets */; };
		207BDA4024AB543F00F32013 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 207BDA3F24AB543F00F32013 /* LaunchScreen.storyboard */; };
		209A4B2729ABDAAA002A9A11 /* RNVAppTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 209A4B2629ABDAAA002A9A11 /* RNVAppTests.swift */; };
		20A3B6EF2252512D0084A50B /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 207888241FCD3A000015651F /* assets */; };
		20C4C37A1FE53E0A00D9721E /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 20C4C3791FE53E0A00D9721E /* GLKit.framework */; };
		20C4C37C1FE53E1A00D9721E /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 20C4C37B1FE53E1A00D9721E /* ImageIO.framework */; };
		20C4C37E1FE53E4A00D9721E /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 20C4C37D1FE53E4A00D9721E /* libc++.tbd */; };
		20CB0E7D21C695460010854F /* images in Resources */ = {isa = PBXBuildFile; fileRef = 20CB0E7B21C695460010854F /* images */; };
		752A9765F8A14465B24BF97C /* libPods-RNVApp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C717EEC7024EA4A970A8C0B /* libPods-RNVApp.a */; };
		9408A38A2A97997800B2E736 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 9408A3872A97997800B2E736 /* AppDelegate.mm */; };
		9408A38B2A97997800B2E736 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 9408A3882A97997800B2E736 /* main.m */; };
		D938F9032AEA8C09006F7BB1 /* main.jsbundle in Resources */ = {isa = PBXBuildFile; fileRef = D938F9022AEA8C09006F7BB1 /* main.jsbundle */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		209A4B2829ABDAAA002A9A11 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 200132E81F6BF9CF00450340 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 200132EF1F6BF9CF00450340;
			remoteInfo = RNVApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		202C7E712190E14100449711 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0782652F658FF043B8F5C682 /* Pods-RNVApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVApp.release.xcconfig"; path = "Pods/Target Support Files/Pods-RNVApp/Pods-RNVApp.release.xcconfig"; sourceTree = "<group>"; };
		1457A110E23AF3761AE396A6 /* Pods-RNVAppUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVAppUITests.debug.xcconfig"; path = "Pods/Target Support Files/Pods-RNVAppUITests/Pods-RNVAppUITests.debug.xcconfig"; sourceTree = "<group>"; };
		1F3AB6FB70CE1E2AF4E8262C /* Pods-RNVApp-RNVAppTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVApp-RNVAppTests.release.xcconfig"; path = "Pods/Target Support Files/Pods-RNVApp-RNVAppTests/Pods-RNVApp-RNVAppTests.release.xcconfig"; sourceTree = "<group>"; };
		200132F01F6BF9CF00450340 /* RNVApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RNVApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		200132FB1F6BF9CF00450340 /* RNVApp.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = RNVApp.xcdatamodel; sourceTree = "<group>"; };
		200132FD1F6BF9CF00450340 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		200133021F6BF9CF00450340 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		2034B5411FB5CB98001A5872 /* RNVApp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RNVApp.entitlements; sourceTree = "<group>"; };
		207888241FCD3A000015651F /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = "<group>"; };
		207BDA3F24AB543F00F32013 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		209A4B2429ABDAAA002A9A11 /* RNVAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RNVAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		209A4B2629ABDAAA002A9A11 /* RNVAppTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNVAppTests.swift; sourceTree = "<group>"; };
		20C4C3791FE53E0A00D9721E /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		20C4C37B1FE53E1A00D9721E /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = System/Library/Frameworks/ImageIO.framework; sourceTree = SDKROOT; };
		20C4C37D1FE53E4A00D9721E /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		20CB0E7B21C695460010854F /* images */ = {isa = PBXFileReference; lastKnownFileType = folder; path = images; sourceTree = "<group>"; };
		3471B97B677CE64891C44686 /* Pods-RNVAppUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVAppUITests.release.xcconfig"; path = "Pods/Target Support Files/Pods-RNVAppUITests/Pods-RNVAppUITests.release.xcconfig"; sourceTree = "<group>"; };
		4EC4EAA4E066BD0502B9DF3F /* Pods-RNVAppTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVAppTests.debug.xcconfig"; path = "Pods/Target Support Files/Pods-RNVAppTests/Pods-RNVAppTests.debug.xcconfig"; sourceTree = "<group>"; };
		5A3295F713AD7DF91D6C2EF3 /* Pods_RNVAppTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RNVAppTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7C717EEC7024EA4A970A8C0B /* libPods-RNVApp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-RNVApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9408A3872A97997800B2E736 /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AppDelegate.mm; sourceTree = "<group>"; };
		9408A3882A97997800B2E736 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		9408A3892A97997800B2E736 /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		96722C6F859B298386E53EEC /* libPods-RNVApp-RNVAppTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-RNVApp-RNVAppTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		AF8EFAC6D34EF718C410C11B /* Pods_RNVAppUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RNVAppUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C7A51880565C9437FC383DF0 /* Pods-RNVApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVApp.debug.xcconfig"; path = "Pods/Target Support Files/Pods-RNVApp/Pods-RNVApp.debug.xcconfig"; sourceTree = "<group>"; };
		D938F9022AEA8C09006F7BB1 /* main.jsbundle */ = {isa = PBXFileReference; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		FA2535CB3354D16A4910CDF1 /* Pods-RNVAppTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVAppTests.release.xcconfig"; path = "Pods/Target Support Files/Pods-RNVAppTests/Pods-RNVAppTests.release.xcconfig"; sourceTree = "<group>"; };
		FB345A890161D745CEEFDE63 /* Pods-RNVApp-RNVAppTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RNVApp-RNVAppTests.debug.xcconfig"; path = "Pods/Target Support Files/Pods-RNVApp-RNVAppTests/Pods-RNVApp-RNVAppTests.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		200132ED1F6BF9CF00450340 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				20C4C37E1FE53E4A00D9721E /* libc++.tbd in Frameworks */,
				20C4C37C1FE53E1A00D9721E /* ImageIO.framework in Frameworks */,
				20C4C37A1FE53E0A00D9721E /* GLKit.framework in Frameworks */,
				752A9765F8A14465B24BF97C /* libPods-RNVApp.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		209A4B2129ABDAAA002A9A11 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1B8FC1667891302063944D4D /* libPods-RNVApp-RNVAppTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		200132E71F6BF9CF00450340 = {
			isa = PBXGroup;
			children = (
				D938F9022AEA8C09006F7BB1 /* main.jsbundle */,
				2030C437224AE567002469E4 /* Resources */,
				207888241FCD3A000015651F /* assets */,
				200132F21F6BF9CF00450340 /* RNVApp */,
				209A4B2529ABDAAA002A9A11 /* RNVAppTests */,
				200132F11F6BF9CF00450340 /* Products */,
				C00B3C9569C8A0FC45A2BE5B /* Pods */,
				217F01A475BAA0089C91D383 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		200132F11F6BF9CF00450340 /* Products */ = {
			isa = PBXGroup;
			children = (
				200132F01F6BF9CF00450340 /* RNVApp.app */,
				209A4B2429ABDAAA002A9A11 /* RNVAppTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		200132F21F6BF9CF00450340 /* RNVApp */ = {
			isa = PBXGroup;
			children = (
				20CB0E7B21C695460010854F /* images */,
				2034B5411FB5CB98001A5872 /* RNVApp.entitlements */,
				9408A3892A97997800B2E736 /* AppDelegate.h */,
				9408A3872A97997800B2E736 /* AppDelegate.mm */,
				9408A3882A97997800B2E736 /* main.m */,
				200132FD1F6BF9CF00450340 /* Assets.xcassets */,
				200133021F6BF9CF00450340 /* Info.plist */,
				200132FA1F6BF9CF00450340 /* RNVApp.xcdatamodeld */,
				207BDA3F24AB543F00F32013 /* LaunchScreen.storyboard */,
			);
			path = RNVApp;
			sourceTree = "<group>";
		};
		2030C437224AE567002469E4 /* Resources */ = {
			isa = PBXGroup;
			children = (
			);
			path = Resources;
			sourceTree = "<group>";
		};
		209A4B2529ABDAAA002A9A11 /* RNVAppTests */ = {
			isa = PBXGroup;
			children = (
				209A4B2629ABDAAA002A9A11 /* RNVAppTests.swift */,
			);
			path = RNVAppTests;
			sourceTree = "<group>";
		};
		217F01A475BAA0089C91D383 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				20C4C37D1FE53E4A00D9721E /* libc++.tbd */,
				20C4C37B1FE53E1A00D9721E /* ImageIO.framework */,
				20C4C3791FE53E0A00D9721E /* GLKit.framework */,
				5A3295F713AD7DF91D6C2EF3 /* Pods_RNVAppTests.framework */,
				AF8EFAC6D34EF718C410C11B /* Pods_RNVAppUITests.framework */,
				7C717EEC7024EA4A970A8C0B /* libPods-RNVApp.a */,
				96722C6F859B298386E53EEC /* libPods-RNVApp-RNVAppTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C00B3C9569C8A0FC45A2BE5B /* Pods */ = {
			isa = PBXGroup;
			children = (
				C7A51880565C9437FC383DF0 /* Pods-RNVApp.debug.xcconfig */,
				0782652F658FF043B8F5C682 /* Pods-RNVApp.release.xcconfig */,
				4EC4EAA4E066BD0502B9DF3F /* Pods-RNVAppTests.debug.xcconfig */,
				FA2535CB3354D16A4910CDF1 /* Pods-RNVAppTests.release.xcconfig */,
				1457A110E23AF3761AE396A6 /* Pods-RNVAppUITests.debug.xcconfig */,
				3471B97B677CE64891C44686 /* Pods-RNVAppUITests.release.xcconfig */,
				FB345A890161D745CEEFDE63 /* Pods-RNVApp-RNVAppTests.debug.xcconfig */,
				1F3AB6FB70CE1E2AF4E8262C /* Pods-RNVApp-RNVAppTests.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		200132EF1F6BF9CF00450340 /* RNVApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2001331B1F6BF9D000450340 /* Build configuration list for PBXNativeTarget "RNVApp" */;
			buildPhases = (
				46D6CEE2E773CE5689ECE6E4 /* [CP] Check Pods Manifest.lock */,
				200132EC1F6BF9CF00450340 /* Sources */,
				200132ED1F6BF9CF00450340 /* Frameworks */,
				200132EE1F6BF9CF00450340 /* Resources */,
				9408A38F2AA0DF7700B2E736 /* Bundle React Native code and images */,
				47C90D4AD2F08223E16AFD26 /* [CP] Embed Pods Frameworks */,
				202C7E712190E14100449711 /* Embed Frameworks */,
				125A5CF0151CF06FDA0E1041 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNVApp;
			productName = RNVApp;
			productReference = 200132F01F6BF9CF00450340 /* RNVApp.app */;
			productType = "com.apple.product-type.application";
		};
		209A4B2329ABDAAA002A9A11 /* RNVAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 209A4B2C29ABDAAA002A9A11 /* Build configuration list for PBXNativeTarget "RNVAppTests" */;
			buildPhases = (
				6935A94A772CA1D10B5A6F0B /* [CP] Check Pods Manifest.lock */,
				209A4B2029ABDAAA002A9A11 /* Sources */,
				209A4B2129ABDAAA002A9A11 /* Frameworks */,
				209A4B2229ABDAAA002A9A11 /* Resources */,
				F19A7C8D65044764F14721CF /* [CP] Embed Pods Frameworks */,
				DF530FE9BA2EFAF0A1689966 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				209A4B2929ABDAAA002A9A11 /* PBXTargetDependency */,
			);
			name = RNVAppTests;
			productName = RNVAppTests;
			productReference = 209A4B2429ABDAAA002A9A11 /* RNVAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		200132E81F6BF9CF00450340 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1420;
				LastUpgradeCheck = 900;
				TargetAttributes = {
					200132EF1F6BF9CF00450340 = {
						CreatedOnToolsVersion = 9.0;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.AccessWiFi = {
								enabled = 0;
							};
							com.apple.ApplePay = {
								enabled = 0;
							};
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.AutoFillCredentialProvider = {
								enabled = 0;
							};
							com.apple.BackgroundModes = {
								enabled = 0;
							};
							com.apple.ClassKit = {
								enabled = 0;
							};
							com.apple.DataProtection = {
								enabled = 0;
							};
							com.apple.GameCenter.iOS = {
								enabled = 0;
							};
							com.apple.HealthKit = {
								enabled = 0;
							};
							com.apple.HomeKit = {
								enabled = 0;
							};
							com.apple.HotspotConfiguration = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Keychain = {
								enabled = 0;
							};
							com.apple.Maps.iOS = {
								enabled = 0;
							};
							com.apple.Multipath = {
								enabled = 0;
							};
							com.apple.NearFieldCommunicationTagReading = {
								enabled = 0;
							};
							com.apple.NetworkExtensions.iOS = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.SafariKeychain = {
								enabled = 0;
							};
							com.apple.Siri = {
								enabled = 0;
							};
							com.apple.VPNLite = {
								enabled = 0;
							};
							com.apple.WAC = {
								enabled = 0;
							};
							com.apple.Wallet = {
								enabled = 0;
							};
							com.apple.iCloud = {
								enabled = 0;
							};
						};
					};
					209A4B2329ABDAAA002A9A11 = {
						CreatedOnToolsVersion = 14.2;
						ProvisioningStyle = Automatic;
						TestTargetID = 200132EF1F6BF9CF00450340;
					};
				};
			};
			buildConfigurationList = 200132EB1F6BF9CF00450340 /* Build configuration list for PBXProject "RNVApp" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 200132E71F6BF9CF00450340;
			productRefGroup = 200132F11F6BF9CF00450340 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				200132EF1F6BF9CF00450340 /* RNVApp */,
				209A4B2329ABDAAA002A9A11 /* RNVAppTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		200132EE1F6BF9CF00450340 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D938F9032AEA8C09006F7BB1 /* main.jsbundle in Resources */,
				20A3B6EF2252512D0084A50B /* assets in Resources */,
				200132FE1F6BF9CF00450340 /* Assets.xcassets in Resources */,
				207BDA4024AB543F00F32013 /* LaunchScreen.storyboard in Resources */,
				20CB0E7D21C695460010854F /* images in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		209A4B2229ABDAAA002A9A11 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		125A5CF0151CF06FDA0E1041 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RNVApp/Pods-RNVApp-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RNVApp/Pods-RNVApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		46D6CEE2E773CE5689ECE6E4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RNVApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		47C90D4AD2F08223E16AFD26 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RNVApp/Pods-RNVApp-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Flipper-DoubleConversion/double-conversion.framework/double-conversion",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Flipper-Glog/glog.framework/glog",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/OpenSSL-Universal/OpenSSL.framework/OpenSSL",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/double-conversion.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/glog.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/OpenSSL.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RNVApp/Pods-RNVApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6935A94A772CA1D10B5A6F0B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RNVApp-RNVAppTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9408A38F2AA0DF7700B2E736 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\nWITH_ENVIRONMENT=\"{{PATH_REACT_NATIVE}}/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"{{PATH_REACT_NATIVE}}/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		DF530FE9BA2EFAF0A1689966 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RNVApp-RNVAppTests/Pods-RNVApp-RNVAppTests-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RNVApp-RNVAppTests/Pods-RNVApp-RNVAppTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F19A7C8D65044764F14721CF /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RNVApp-RNVAppTests/Pods-RNVApp-RNVAppTests-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Flipper-DoubleConversion/double-conversion.framework/double-conversion",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Flipper-Glog/glog.framework/glog",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/OpenSSL-Universal/OpenSSL.framework/OpenSSL",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/double-conversion.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/glog.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/OpenSSL.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RNVApp-RNVAppTests/Pods-RNVApp-RNVAppTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		200132EC1F6BF9CF00450340 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9408A38A2A97997800B2E736 /* AppDelegate.mm in Sources */,
				9408A38B2A97997800B2E736 /* main.m in Sources */,
				200132FC1F6BF9CF00450340 /* RNVApp.xcdatamodeld in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		209A4B2029ABDAAA002A9A11 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				209A4B2729ABDAAA002A9A11 /* RNVAppTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		209A4B2929ABDAAA002A9A11 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 200132EF1F6BF9CF00450340 /* RNVApp */;
			targetProxy = 209A4B2829ABDAAA002A9A11 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		200133191F6BF9D000450340 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-DRN_FABRIC_ENABLED",
				);
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"-DRN_FABRIC_ENABLED",
				);
				REACT_NATIVE_PATH = "{{PATH_REACT_NATIVE}}";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				DEVELOPMENT_TEAM = "";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_BUNDLE_IDENTIFIER = com.rnv.myapp;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
			};
			name = Debug;
		};
		2001331A1F6BF9D000450340 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-DRN_FABRIC_ENABLED",
				);
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"-DRN_FABRIC_ENABLED",
				);
				REACT_NATIVE_PATH = "{{PATH_REACT_NATIVE}}";
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				VALIDATE_PRODUCT = YES;
				DEVELOPMENT_TEAM = "";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_BUNDLE_IDENTIFIER = com.rnv.myapp;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
			};
			name = Release;
		};
		2001331C1F6BF9D000450340 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C7A51880565C9437FC383DF0 /* Pods-RNVApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = RNVApp/RNVApp.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = RNVApp/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				OTHER_CFLAGS = (
					"$(inherited)",
					"-isystem",
					"\"${PODS_ROOT}/Headers/Public\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rnv.myapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				CODE_SIGN_IDENTITY = "iPhone Developer";
			};
			name = Debug;
		};
		2001331D1F6BF9D000450340 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0782652F658FF043B8F5C682 /* Pods-RNVApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = RNVApp/RNVApp.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = RNVApp/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rnv.myapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				CODE_SIGN_IDENTITY = "iPhone Developer";
			};
			name = Release;
		};
		209A4B2A29ABDAAA002A9A11 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FB345A890161D745CEEFDE63 /* Pods-RNVApp-RNVAppTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.rnv.myapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RNVApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/RNVApp";
				DEVELOPMENT_TEAM = "";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
			};
			name = Debug;
		};
		209A4B2B29ABDAAA002A9A11 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1F3AB6FB70CE1E2AF4E8262C /* Pods-RNVApp-RNVAppTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.rnv.myapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RNVApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/RNVApp";
				DEVELOPMENT_TEAM = "";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		200132EB1F6BF9CF00450340 /* Build configuration list for PBXProject "RNVApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				200133191F6BF9D000450340 /* Debug */,
				2001331A1F6BF9D000450340 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2001331B1F6BF9D000450340 /* Build configuration list for PBXNativeTarget "RNVApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2001331C1F6BF9D000450340 /* Debug */,
				2001331D1F6BF9D000450340 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		209A4B2C29ABDAAA002A9A11 /* Build configuration list for PBXNativeTarget "RNVAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				209A4B2A29ABDAAA002A9A11 /* Debug */,
				209A4B2B29ABDAAA002A9A11 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		200132FA1F6BF9CF00450340 /* RNVApp.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				200132FB1F6BF9CF00450340 /* RNVApp.xcdatamodel */,
			);
			currentVersion = 200132FB1F6BF9CF00450340 /* RNVApp.xcdatamodel */;
			path = RNVApp.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = 200132E81F6BF9CF00450340 /* Project object */;
}
