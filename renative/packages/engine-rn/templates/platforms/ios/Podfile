{{INJECT_PLUGIN_PODFILE_SOURCES}}


# Resolve react_native_pods.rb with node to allow for hoisting
def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

{{INJECT_PODFILE_HEADER}}
node_require('react-native/scripts/react_native_pods.rb')

platform :ios, '{{INJECT_PLUGIN_DEPLOYMENT_TARGET}}'
prepare_react_native_project!

# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
#flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'RNVApp' do
  config = use_native_modules!

  {{INJECT_PLUGIN_PATHS}}
  
  use_react_native!(
    :path => config[:reactNativePath],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
   # :flipper_configuration => flipper_config,
    # Hermes is now enabled by default. Disable by setting this flag to false.
    :hermes_enabled => {{INJECT_HERMES_ENABLED}},
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/../.."
  )

  target 'RNVAppTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
    {{INJECT_POST_INSTALL}}

    installer.pods_project.targets.each do |target|
      if target.name == 'Flipper'
        file_path = 'Pods/Flipper/xplat/Flipper/FlipperTransportTypes.h'
        contents = File.read(file_path)
        unless contents.include?('#include <functional>')
          File.open(file_path, 'w') do |file|
            file.puts('#include <functional>')
            file.puts(contents)
          end
        end
      end
    end
  end
end

{{INJECT_PLUGIN_PODFILE_INJECT}}