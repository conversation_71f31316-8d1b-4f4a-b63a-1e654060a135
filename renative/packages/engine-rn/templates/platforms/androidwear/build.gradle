// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = '{{BUILD_TOOLS_VERSION}}'
        minSdkVersion = {{MIN_SDK_VERSION}}
        compileSdkVersion = {{COMPILE_SDK_VERSION}}
        targetSdkVersion = {{TARGET_SDK_VERSION}}

        // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
        ndkVersion = '{{NDK_VERSION}}'
        kotlinVersion = '{{INJECT_KOTLIN_VERSION}}'
    }
    
    subprojects { subproject ->
    afterEvaluate{
        if((subproject.plugins.hasPlugin('android') || subproject.plugins.hasPlugin('android-library'))) {
            android {
                compileSdkVersion rootProject.ext.compileSdkVersion
                buildToolsVersion rootProject.ext.buildToolsVersion
            }
        }
    }
}
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

apply plugin: "com.facebook.react.rootproject"
