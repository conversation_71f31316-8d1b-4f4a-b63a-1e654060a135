//
//  AppDelegate.swift
//  RNVApp
//
//  Generated by ReNative (https://renative.org)
//
//

import UIKit
import CoreData
import React
import UserNotifications
{{APPDELEGATE_IMPORTS}}

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate, UNUserNotificationCenterDelegate {{APPDELEGATE_EXTENSIONS}} {

    var window: UIWindow?
    let moduleName = "RNVApp"

    var uiView: RCTRootView!
    let bundleUrl = {{BUNDLE}}

    {{APPDELEGATE_METHODS}}
}
