{{PLUGIN_PODFILE_SOURCES}}
platform :ios, '{{INJECT_PLUGIN_DEPLOYMENT_TARGET}}'
use_frameworks!
{{PLUGIN_WARNINGS}}

# ReNative: To allow dynamic plugins via flavours autolinking has to be disabled
# require_relative '../../node_modules/@react-native-community/cli-platform-ios/native_modules'

target 'RNVApp' do
  pod 'FBLazyVector', :path => "{{PATH_REACT_NATIVE}}/Libraries/FBLazyVector"
  pod 'FBReactNativeSpec', :path => "{{PATH_REACT_NATIVE}}/React/FBReactNativeSpec"
  pod 'RCTRequired', :path => "{{PATH_REACT_NATIVE}}/Libraries/RCTRequired"
  pod 'RCTTypeSafety', :path => "{{PATH_REACT_NATIVE}}/Libraries/TypeSafety"
  pod 'React', :path => '{{PATH_REACT_NATIVE}}/'
  pod 'React-Core', :path => '{{PATH_REACT_NATIVE}}/'
  pod 'React-CoreModules', :path => '{{PATH_REACT_NATIVE}}/React/CoreModules'
  pod 'React-Core/DevSupport', :path => '{{PATH_REACT_NATIVE}}/'
  pod 'React-RCTActionSheet', :path => '{{PATH_REACT_NATIVE}}/Libraries/ActionSheetIOS'
  pod 'React-RCTAnimation', :path => '{{PATH_REACT_NATIVE}}/Libraries/NativeAnimation'
  pod 'React-RCTBlob', :path => '{{PATH_REACT_NATIVE}}/Libraries/Blob'
  pod 'React-RCTImage', :path => '{{PATH_REACT_NATIVE}}/Libraries/Image'
  pod 'React-RCTLinking', :path => '{{PATH_REACT_NATIVE}}/Libraries/LinkingIOS'
  pod 'React-RCTNetwork', :path => '{{PATH_REACT_NATIVE}}/Libraries/Network'
  pod 'React-RCTSettings', :path => '{{PATH_REACT_NATIVE}}/Libraries/Settings'
  pod 'React-RCTText', :path => '{{PATH_REACT_NATIVE}}/Libraries/Text'
  pod 'React-RCTVibration', :path => '{{PATH_REACT_NATIVE}}/Libraries/Vibration'
  pod 'React-Core/RCTWebSocket', :path => '{{PATH_REACT_NATIVE}}/'

  pod 'React-cxxreact', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/cxxreact'
  pod 'React-jsi', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/jsi'
  pod 'React-jsiexecutor', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/jsiexecutor'
  pod 'React-jsinspector', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/jsinspector'
  pod 'React-callinvoker', :path => "{{PATH_REACT_NATIVE}}/ReactCommon/callinvoker"
  pod 'ReactCommon/turbomodule/core', :path => "{{PATH_REACT_NATIVE}}/ReactCommon"
  pod 'Yoga', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/yoga'

  pod 'DoubleConversion', :podspec => '{{PATH_REACT_NATIVE}}/third-party-podspecs/DoubleConversion.podspec'
  pod 'glog', :podspec => '{{PATH_REACT_NATIVE}}/third-party-podspecs/glog.podspec'
  pod 'boost', :podspec => '{{PATH_REACT_NATIVE}}/third-party-podspecs/boost.podspec'
  pod 'RCT-Folly', :podspec => '{{PATH_REACT_NATIVE}}/third-party-podspecs/RCT-Folly.podspec'

  pod 'React-perflogger', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/reactperflogger'
  pod 'React-logger', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/logger'
  pod 'React-runtimeexecutor', :path => '{{PATH_REACT_NATIVE}}/ReactCommon/runtimeexecutor'

{{PLUGIN_PATHS}}

  # ReNative: To allow dynamic plugins via flavours autolinking has to be disabled
  # use_native_modules!('../..')
end

pre_install do |installer|
  # workaround for https://github.com/CocoaPods/CocoaPods/issues/3289
  Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
end

static_frameworks = [{{PLUGIN_STATIC_FRAMEWORKS}}]

pre_install do |installer|
  installer.pod_targets.each do |pod|
    if static_frameworks.include?(pod.name){{PLUGIN_STATIC_POD_EXTRA_CONDITIONS}}
      def pod.static_framework?;
        true
      end
      def pod.build_type;
        {{PLUGIN_STATIC_POD_DEFINITION}}
      end
    end
  end
end

post_install do |installer|
  {{INJECT_POST_INSTALL}}
  installer.pods_project.targets.each do |target|
        # Fix bundle targets' 'Signing Certificate' to 'Sign to Run Locally'
        if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
            target.build_configurations.each do |config|
                config.build_settings['CODE_SIGN_IDENTITY[sdk=macosx*]'] = '-'
            end
        end
    end

    # Apple Silicon builds require a library path tweak for Swift library discovery or "symbol not found" for swift things
    installer.aggregate_targets.each do |aggregate_target| 
      aggregate_target.user_project.native_targets.each do |target|
        target.build_configurations.each do |config|
          config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(SDKROOT)/usr/lib/swift', '$(inherited)']
        end
      end
      aggregate_target.user_project.save
    end

    # https://github.com/reactwg/react-native-releases/discussions/1
    `sed -i -e 's/defined(TARGET_OS_OSX) || TARGET_OS_OSX/& || TARGET_OS_MACCATALYST/g' Pods/RCT-Folly/folly/portability/Time.h`
end

{{PLUGIN_PODFILE_INJECT}}



