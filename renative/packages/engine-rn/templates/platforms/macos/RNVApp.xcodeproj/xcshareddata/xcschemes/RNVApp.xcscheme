<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "0920"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "200132EF1F6BF9CF00450340"
               BuildableName = "RNVApp.app"
               BlueprintName = "RNVApp"
               ReferencedContainer = "container:RNVApp.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "200133061F6BF9D000450340"
               BuildableName = "RNVAppTests.xctest"
               BlueprintName = "RNVAppTests"
               ReferencedContainer = "container:RNVApp.xcodeproj">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "200133111F6BF9D000450340"
               BuildableName = "RNVAppUITests.xctest"
               BlueprintName = "RNVAppUITests"
               ReferencedContainer = "container:RNVApp.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "200132EF1F6BF9CF00450340"
            BuildableName = "RNVApp.app"
            BlueprintName = "RNVApp"
            ReferencedContainer = "container:RNVApp.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
      </AdditionalOptions>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "{{PLUGIN_DEBUGGER_ID}}"
      selectedLauncherIdentifier = "{{PLUGIN_LAUNCHER_ID}}"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "200132EF1F6BF9CF00450340"
            BuildableName = "RNVApp.app"
            BlueprintName = "RNVApp"
            ReferencedContainer = "container:RNVApp.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <AdditionalOptions>
      </AdditionalOptions>
      <CommandLineArguments>
         {{INJECT_COMMAND_LINE_ARGUMENTS}}
      </CommandLineArguments>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "200132EF1F6BF9CF00450340"
            BuildableName = "RNVApp.app"
            BlueprintName = "RNVApp"
            ReferencedContainer = "container:RNVApp.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
