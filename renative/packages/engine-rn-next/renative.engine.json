{"$schema": "../../.rnv/schema/rnv.engine.json", "name": "@rnv/engine-rn-next", "engineExtension": "rnx", "overview": "React native based engine with web transpiler provided by react-native-web and render by nextjs", "plugins": {"react": "source:rnv", "react-art": "source:rnv", "react-dom": "source:rnv", "react-native": "source:rnv", "react-native-web": "source:rnv", "next": "source:rnv"}, "npm": {"devDependencies": {}}, "platforms": {"web": {}}}