{"name": "@rnv/engine-rn-next", "version": "1.10.0-rc.1", "description": "ReNative Engine to build next based platforms with react native support.", "keywords": ["nextjs", "react-native"], "homepage": "https://github.com/flexn-io/renative#readme", "bugs": {"url": "https://github.com/flexn-io/renative/issues"}, "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://github.com/pavjacko)", "contributors": [], "files": ["LICENSE", "lib", "renative.engine.json", "templates"], "main": "lib/index.js", "repository": {"type": "git", "url": "git://github.com/flexn-io/renative.git"}, "scripts": {"build": "yarn clean && yarn compile", "clean": "rimraf -I ./lib && rimraf -I tsconfig.tsbuildinfo", "compile": "tsc -b tsconfig.json", "compile:prod": "tsc -b tsconfig.json", "prepublishOnly": "yarn clean && yarn compile:prod", "watch": "tsc --watch --preserveWatchOutput"}, "dependencies": {"@rnv/adapter": "1.10.0-rc.1", "@rnv/sdk-utils": "1.10.0-rc.1", "babel-preset-expo": "9.5.2", "next-fonts": "1.5.1", "next-images": "1.8.4", "webpack": "^5.94.0"}, "peerDependencies": {"@rnv/core": "^1.10.0-rc.1", "next": "^14", "react": "^18", "react-native-web": "~0.18.0"}, "private": "false", "publishConfig": {"access": "public"}}