{"name": "@rnv/engine-rn-electron", "version": "1.10.0-rc.1", "description": "ReNative Engine to build electron based platforms with react native support.", "keywords": ["electron", "react-native"], "homepage": "https://github.com/flexn-io/renative#readme", "bugs": {"url": "https://github.com/flexn-io/renative/issues"}, "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://github.com/pavjacko)", "contributors": [], "files": ["LICENSE", "lib", "renative.engine.json", "templates"], "main": "lib/index.js", "repository": {"type": "git", "url": "git://github.com/flexn-io/renative.git"}, "scripts": {"build": "yarn clean && yarn compile", "clean": "rimraf -I ./lib && rimraf -I tsconfig.tsbuildinfo", "compile": "tsc -b tsconfig.json", "compile:prod": "tsc -b tsconfig.json", "prepublishOnly": "yarn clean && yarn compile:prod", "watch": "tsc --watch --preserveWatchOutput"}, "dependencies": {"@react-native/babel-preset": "0.76.5", "@rnv/adapter": "1.10.0-rc.1", "@rnv/sdk-react-native": "1.10.0-rc.1", "@rnv/sdk-utils": "1.10.0-rc.1", "@rnv/sdk-webpack": "1.10.0-rc.1", "electron": "26.3.0", "electron-builder": "24.13.3", "electron-notarize": "1.2.2", "metro-react-native-babel-preset": "0.76.8"}, "peerDependencies": {"@rnv/core": "^1.10.0-rc.1"}, "private": "false", "publishConfig": {"access": "public"}, "gitHead": "48ef244c6ec2e206cbfd72fe8770d8dc03387591"}