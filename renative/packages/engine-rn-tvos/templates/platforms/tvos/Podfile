source 'https://github.com/react-native-tvos/react-native-tvos-podspecs.git'
source 'https://cdn.cocoapods.org/'
{{INJECT_PLUGIN_PODFILE_SOURCES}}


cmake_path = `command -v cmake`
if cmake_path == ""
  brew_path = `command -v brew`
  if brew_path != ""
    Pod::UI.puts "Installing CMake using brew. This is required to build project.".red
    `brew install cmake`
  else
    Pod::UI.puts "In order to build project locally, you need cmake installed, please install it and try again".red
    return
  end
else
  Pod::UI.puts "Cmake found at: #{cmake_path}".green
end


# Resolve react_native_pods.rb with node to allow for hoisting
def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

{{INJECT_PODFILE_HEADER}}
node_require('react-native-tvos/scripts/react_native_pods.rb')

prepare_react_native_project!

# flipper_config = FlipperConfiguration.disabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

####### For now, only one target at a time can be used in a React Native podfile ########
####### https://github.com/react-native-tvos/react-native-tvos/issues/619        ########

# target 'RNVApp' do
#   config = use_native_modules!
#   platform :ios, '{{INJECT_PLUGIN_DEPLOYMENT_TARGET}}'
#
#   use_react_native!(
#     :path => config[:reactNativePath],
#     # Enables Flipper.
#     #
#     # Note that if you have use_frameworks! enabled, Flipper will not work and
#     # you should disable the next line.
#     :flipper_configuration => flipper_config,
#     # Hermes is now enabled by default. Disable by setting this flag to false.
#     :hermes_enabled => {{INJECT_HERMES_ENABLED}},
#     # An absolute path to your application root.
#     :app_path => "#{Pod::Config.instance.installation_root}/../.."
#   )
#
#   target 'RNVAppTests' do
#     inherit! :complete
#     # Pods for testing
#   end
# end

target 'RNVApp-tvOS' do
  config = use_native_modules!
  platform :tvos, '{{INJECT_PLUGIN_DEPLOYMENT_TARGET}}'

  {{INJECT_PLUGIN_PATHS}}

  use_react_native!(
    :path => config[:reactNativePath],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    # :flipper_configuration => flipper_config,
    # Hermes is now enabled by default. Disable by setting this flag to false.
    :hermes_enabled => {{INJECT_HERMES_ENABLED}},
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/../.."
  )

  target 'RNVApp-tvOSTests' do
    inherit! :complete
    # Pods for testing
  end
end

post_install do |installer|
  config = use_native_modules!
  # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
  react_native_post_install(
    installer,
    config[:reactNativePath],
    :mac_catalyst_enabled => false,
    # :ccache_enabled => true
  )
  {{INJECT_POST_INSTALL}}

  installer.pods_project.targets.each do |target|
    if target.name == 'Flipper'
      file_path = 'Pods/Flipper/xplat/Flipper/FlipperTransportTypes.h'
      contents = File.read(file_path)
      unless contents.include?('#include <functional>')
        File.open(file_path, 'w') do |file|
          file.puts('#include <functional>')
          file.puts(contents)
        end
      end
    end
  end
end

{{INJECT_PLUGIN_PODFILE_INJECT}}
