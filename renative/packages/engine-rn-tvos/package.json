{"name": "@rnv/engine-rn-tvos", "version": "1.10.0-rc.1", "description": "ReNative Engine to build react-native tvos platform.", "keywords": ["react-native"], "homepage": "https://github.com/flexn-io/renative#readme", "bugs": {"url": "https://github.com/flexn-io/renative/issues"}, "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://github.com/pavjacko)", "contributors": [], "files": ["LICENSE", "lib", "renative.engine.json", "templates"], "main": "lib/index.js", "repository": {"type": "git", "url": "git://github.com/flexn-io/renative.git"}, "scripts": {"build": "yarn clean && yarn compile", "clean": "rimraf -I ./lib && rimraf -I tsconfig.tsbuildinfo", "compile": "tsc -b tsconfig.json", "compile:prod": "tsc -b tsconfig.json", "prepublishOnly": "yarn clean && yarn compile:prod", "watch": "tsc --watch --preserveWatchOutput"}, "dependencies": {"@react-native/babel-preset": "0.76.5", "@rnv/adapter": "1.10.0-rc.1", "@rnv/sdk-android": "1.10.0-rc.1", "@rnv/sdk-apple": "1.10.0-rc.1", "@rnv/sdk-react-native": "1.10.0-rc.1"}, "peerDependencies": {"@rnv/core": "^1.10.0-rc.1", "metro-cache": "^0.81.0"}, "private": "false", "publishConfig": {"access": "public"}, "gitHead": "48ef244c6ec2e206cbfd72fe8770d8dc03387591"}