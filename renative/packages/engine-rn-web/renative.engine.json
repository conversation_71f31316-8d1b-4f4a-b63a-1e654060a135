{"$schema": "../../.rnv/schema/rnv.engine.json", "id": "engine-rn-web", "engineExtension": "rnw", "name": "@rnv/engine-rn-web", "overview": "React native based engine with web transpiler provided by react-native-web", "plugins": {"react": "source:rnv", "react-art": "source:rnv", "react-dom": "source:rnv", "react-native": "source:rnv", "react-native-web": "source:rnv"}, "npm": {"devDependencies": {}}, "platforms": {"tizen": {"npm": {"dependencies": {"raf": "3.4.1"}}}, "web": {}, "webtv": {}, "webos": {}, "tizenwatch": {}, "tizenmobile": {}, "chromecast": {}, "kaios": {}}}