{"name": "@rnv/engine-rn-web", "version": "1.10.0-rc.1", "description": "ReNative Engine to build web based platforms with react native support.", "keywords": ["react-native"], "homepage": "https://github.com/flexn-io/renative#readme", "bugs": {"url": "https://github.com/flexn-io/renative/issues"}, "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://github.com/pavjacko)", "contributors": [], "files": ["LICENSE", "lib", "renative.engine.json", "templates"], "main": "lib/index.js", "repository": {"type": "git", "url": "git://github.com/flexn-io/renative.git"}, "scripts": {"build": "yarn clean && yarn compile", "clean": "rimraf -I ./lib && rimraf -I tsconfig.tsbuildinfo", "compile": "tsc -b tsconfig.json", "compile:prod": "tsc -b tsconfig.json", "prepublishOnly": "yarn clean && yarn compile:prod", "watch": "tsc --watch --preserveWatchOutput"}, "dependencies": {"@react-native/babel-preset": "0.76.5", "@rnv/adapter": "1.10.0-rc.1", "@rnv/sdk-kaios": "1.10.0-rc.1", "@rnv/sdk-tizen": "1.10.0-rc.1", "@rnv/sdk-utils": "1.10.0-rc.1", "@rnv/sdk-webos": "1.10.0-rc.1", "@rnv/sdk-webpack": "1.10.0-rc.1", "metro-react-native-babel-preset": "0.76.8"}, "peerDependencies": {"@rnv/core": "^1.10.0-rc.1"}, "private": "false", "publishConfig": {"access": "public"}}