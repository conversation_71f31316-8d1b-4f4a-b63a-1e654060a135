<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  {{#cppNugetPackages}}
  {{#propsTopOfFile}}
  <Import Project="..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.props" Condition="Exists('..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.props')" />
  {{/propsTopOfFile}}
  {{/cppNugetPackages}}
  <Import Project="$(SolutionDir)\ExperimentalFeatures.props" Condition="Exists('$(SolutionDir)\ExperimentalFeatures.props')" />
  <PropertyGroup Label="Globals">
    <CppWinRTOptimized>true</CppWinRTOptimized>
    <CppWinRTRootNamespaceAutoMerge>true</CppWinRTRootNamespaceAutoMerge>
    <MinimalCoreWin>true</MinimalCoreWin>
    <ProjectGuid>{{ projectGuidLower }}</ProjectGuid>
    <ProjectName>{{ name }}</ProjectName>
    <RootNamespace>{{ namespace }}</RootNamespace>
    <DefaultLanguage>en-US</DefaultLanguage>
    <MinimumVisualStudioVersion>16.0</MinimumVisualStudioVersion>
    <AppContainerApplication>true</AppContainerApplication>
    <ApplicationType>Windows Store</ApplicationType>
    <ApplicationTypeRevision>10.0</ApplicationTypeRevision>
    <WindowsTargetPlatformVersion Condition=" '$(WindowsTargetPlatformVersion)' == '' ">10.0.18362.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.16299.0</WindowsTargetPlatformMinVersion>
    <PackageCertificateKeyFile>{{ name }}_TemporaryKey.pfx</PackageCertificateKeyFile>
    {{#certificateThumbprint}}
    <PackageCertificateThumbprint>{{certificateThumbprint}}</PackageCertificateThumbprint>
    {{/certificateThumbprint}}
    <PackageCertificatePassword>password</PackageCertificatePassword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="ReactNativeWindowsProps">
    <ReactNativeWindowsDir Condition="'$(ReactNativeWindowsDir)' == ''">$([MSBuild]::GetDirectoryNameOfFileAbove($(MSBuildThisFileDirectory), 'node_modules\react-native-windows\package.json'))\node_modules\react-native-windows\</ReactNativeWindowsDir>
    {{#useExperimentalNuget}}
    <UseExperimentalNuget>true</UseExperimentalNuget>    <!-- This will eventually default to true and this property should be removed -->
    {{/useExperimentalNuget}}
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Debug'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  {{#cppNugetPackages}}
  {{#propsMiddleOfFile}}
  <Import Project="..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.props" Condition="Exists('..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.props')" />
  {{/propsMiddleOfFile}}
  {{/cppNugetPackages}}
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings"></ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="PropertySheet.props" />
  </ImportGroup>
  <ImportGroup Label="ReactNativeWindowsPropertySheets">
    <Import Project="$(ReactNativeWindowsDir)\PropertySheets\external\Microsoft.ReactNative.Uwp.CppApp.props" Condition="Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppApp.props')" />
    <Import Project="..\packages\$(WinUIPackageProps)" Condition="'$(WinUIPackageProps)'!='' And Exists('..\packages\$(WinUIPackageProps)')" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)pch.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level4</WarningLevel>
      <AdditionalOptions>%(AdditionalOptions) /bigobj</AdditionalOptions>
      <DisableSpecificWarnings>4453;28204</DisableSpecificWarnings>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="MainPage.h">
      <DependentUpon>MainPage.xaml</DependentUpon>
      <SubType>Code</SubType>
    </ClInclude>
    <ClInclude Include="ReactPackageProvider.h" />
    <ClInclude Include="AutolinkedNativeModules.g.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="App.h">
      <DependentUpon>App.xaml</DependentUpon>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <SubType>Designer</SubType>
    </ApplicationDefinition>
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest">
      <SubType>Designer</SubType>
    </AppxManifest>
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\LargeTile.scale-100.png" />
    <Image Include="Assets\LargeTile.scale-125.png" />
    <Image Include="Assets\LargeTile.scale-150.png" />
    <Image Include="Assets\LargeTile.scale-200.png" />
    <Image Include="Assets\LargeTile.scale-400.png" />
    <Image Include="Assets\SmallTile.scale-100.png" />
    <Image Include="Assets\SmallTile.scale-125.png" />
    <Image Include="Assets\SmallTile.scale-150.png" />
    <Image Include="Assets\SmallTile.scale-200.png" />
    <Image Include="Assets\SmallTile.scale-400.png" />
    <Image Include="Assets\SplashScreen.scale-100.png" />
    <Image Include="Assets\SplashScreen.scale-125.png" />
    <Image Include="Assets\SplashScreen.scale-150.png" />
    <Image Include="Assets\SplashScreen.scale-200.png" />
    <Image Include="Assets\SplashScreen.scale-400.png" />
    <Image Include="Assets\Square150x150Logo.scale-100.png" />
    <Image Include="Assets\Square150x150Logo.scale-125.png" />
    <Image Include="Assets\Square150x150Logo.scale-150.png" />
    <Image Include="Assets\Square150x150Logo.scale-200.png" />
    <Image Include="Assets\Square150x150Logo.scale-400.png" />
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-16.png" />
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-24.png" />
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-256.png" />
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-32.png" />
    <Image Include="Assets\Square44x44Logo.altform-lightunplated_targetsize-48.png" />
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-16.png" />
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-256.png" />
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-32.png" />
    <Image Include="Assets\Square44x44Logo.altform-unplated_targetsize-48.png" />
    <Image Include="Assets\Square44x44Logo.scale-100.png" />
    <Image Include="Assets\Square44x44Logo.scale-125.png" />
    <Image Include="Assets\Square44x44Logo.scale-150.png" />
    <Image Include="Assets\Square44x44Logo.scale-200.png" />
    <Image Include="Assets\Square44x44Logo.scale-400.png" />
    <Image Include="Assets\Square44x44Logo.targetsize-16.png" />
    <Image Include="Assets\Square44x44Logo.targetsize-24.png" />
    <Image Include="Assets\Square44x44Logo.targetsize-24_altform-unplated.png" />
    <Image Include="Assets\Square44x44Logo.targetsize-256.png" />
    <Image Include="Assets\Square44x44Logo.targetsize-32.png" />
    <Image Include="Assets\Square44x44Logo.targetsize-48.png" />
    <Image Include="Assets\StoreLogo.scale-100.png" />
    <Image Include="Assets\StoreLogo.scale-125.png" />
    <Image Include="Assets\StoreLogo.scale-150.png" />
    <Image Include="Assets\StoreLogo.scale-200.png" />
    <Image Include="Assets\StoreLogo.scale-400.png" />
    <Image Include="Assets\Wide310x150Logo.scale-100.png" />
    <Image Include="Assets\Wide310x150Logo.scale-125.png" />
    <Image Include="Assets\Wide310x150Logo.scale-150.png" />
    <Image Include="Assets\Wide310x150Logo.scale-200.png" />
    <Image Include="Assets\Wide310x150Logo.scale-400.png" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="MainPage.cpp">
      <DependentUpon>MainPage.xaml</DependentUpon>
      <SubType>Code</SubType>
    </ClCompile>
    <ClCompile Include="ReactPackageProvider.cpp" />
    <ClCompile Include="AutolinkedNativeModules.g.cpp" />
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader>Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="App.cpp">
      <DependentUpon>App.xaml</DependentUpon>
    </ClCompile>
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="App.idl">
      <DependentUpon>App.xaml</DependentUpon>
    </Midl>
    <Midl Include="MainPage.idl">
      <DependentUpon>MainPage.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Midl>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="PropertySheet.props" />
    <Text Include="readme.txt">
      <DeploymentContent>false</DeploymentContent>
    </Text>
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainPage.xaml">
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
   <!-- Only add Fonts as dependencies if vector icons package is installed as a plugin -->
  {{#hasAdditionalAssets }}
  <ItemGroup>
    <Font Include="Assets\AntDesign.ttf" />
    <Font Include="Assets\Entypo.ttf" />
    <Font Include="Assets\EvilIcons.ttf" />
    <Font Include="Assets\Feather.ttf" />
    <Font Include="Assets\FontAwesome.ttf" />
    <Font Include="Assets\FontAwesome5_Brands.ttf" />
    <Font Include="Assets\FontAwesome5_Regular.ttf" />
    <Font Include="Assets\FontAwesome5_Solid.ttf" />
    <Font Include="Assets\Fontisto.ttf" />
    <Font Include="Assets\Foundation.ttf" />
    <Font Include="Assets\Ionicons.ttf" />
    <Font Include="Assets\MaterialCommunityIcons.ttf" />
    <Font Include="Assets\MaterialIcons.ttf" />
    <Font Include="Assets\Octicons.ttf" />
    <Font Include="Assets\SimpleLineIcons.ttf" />
    <Font Include="Assets\Zocial.ttf" />
  </ItemGroup>
  {{/hasAdditionalAssets }}
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ReactNativeWindowsTargets">
    <Import Project="$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppApp.targets" Condition="Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppApp.targets')" />
  </ImportGroup>
  <Target Name="EnsureReactNativeWindowsTargets" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references targets in your node_modules\react-native-windows folder that are missing. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppApp.props')" Text="$([System.String]::Format('$(ErrorText)', '$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppApp.props'))" />
    <Error Condition="!Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppApp.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppApp.targets'))" />
  </Target>
  <ImportGroup Label="ExtensionTargets">
    {{#cppNugetPackages}}
    {{#hasTargets}}
    <Import Project="..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.targets" Condition="Exists('..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.targets')" />
    {{/hasTargets}}
    {{/cppNugetPackages}}
    <Import Project="..\packages\$(WinUIPackageName).$(WinUIPackageVersion)\build\native\$(WinUIPackageName).targets" Condition="Exists('..\packages\$(WinUIPackageName).$(WinUIPackageVersion)\build\native\$(WinUIPackageName).targets')" />
  </ImportGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    {{#cppNugetPackages}}
    {{#hasProps}}
    <Error Condition="!Exists('..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.props'))" />
    {{/hasProps}}
    {{#hasTargets}}
    <Error Condition="!Exists('..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\{{ id }}.{{ version }}\build\native\{{ id }}.targets'))" />
    {{/hasTargets}}
    {{/cppNugetPackages}}
    <Error Condition="!Exists('..\packages\$(WinUIPackageName).$(WinUIPackageVersion)\build\native\$(WinUIPackageName).targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\$(WinUIPackageName).$(WinUIPackageVersion)\build\native\$(WinUIPackageName).targets'))" />
  </Target>
</Project>
