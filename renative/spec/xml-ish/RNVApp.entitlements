<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<!--
#################################################################################################################
Generated by ReNative https://renative.org
You can manually override this file by adding it to ./appCongifs/YOUR_APP_ID/builds/ios/RNVApp/RNVApp.entitlements
#################################################################################################################
-->
<plist version="1.0">
<dict>
  <!-- <key>aps-environment</key>
  <string>development</string>
  <key>com.apple.developer.ClassKit-environment</key>
  <string>development</string>
  <key>com.apple.developer.associated-domains</key>
  <array/>
  <key>com.apple.developer.authentication-services.autofill-credential-provider</key>
  <true/>
  <key>com.apple.developer.default-data-protection</key>
  <string>NSFileProtectionComplete</string>
  <key>com.apple.developer.healthkit</key>
  <true/>
  <key>com.apple.developer.healthkit.access</key>
  <array/>
  <key>com.apple.developer.homekit</key>
  <true/>
  <key>com.apple.developer.icloud-container-identifiers</key>
  <array>
    <string>iCloud.$(CFBundleIdentifier)</string>
  </array>
  <key>com.apple.developer.icloud-services</key>
  <array>
    <string>CloudKit</string>
    <string>CloudDocuments</string>
  </array>
  <key>com.apple.developer.in-app-payments</key>
  <array/>
  <key>com.apple.developer.networking.HotspotConfiguration</key>
  <true/>
  <key>com.apple.developer.networking.multipath</key>
  <true/>
  <key>com.apple.developer.networking.networkextension</key>
  <array>
    <string>app-proxy-provider</string>
    <string>content-filter-provider</string>
    <string>dns-proxy</string>
    <string>packet-tunnel-provider</string>
  </array>
  <key>com.apple.developer.networking.vpn.api</key>
  <array>
    <string>allow-vpn</string>
  </array>
  <key>com.apple.developer.networking.wifi-info</key>
  <true/>
  <key>com.apple.developer.nfc.readersession.formats</key>
  <array>
    <string>NDEF</string>
  </array>
  <key>com.apple.developer.pass-type-identifiers</key>
  <array>
    <string>$(TeamIdentifierPrefix)*</string>
  </array>
  <key>com.apple.developer.siri</key>
  <true/>
  <key>com.apple.developer.ubiquity-container-identifiers</key>
  <array>
    <string>iCloud.$(CFBundleIdentifier)</string>
  </array>
  <key>com.apple.developer.ubiquity-kvstore-identifier</key>
  <string>$(TeamIdentifierPrefix)$(CFBundleIdentifier)</string>
  <key>com.apple.external-accessory.wireless-configuration</key>
  <true/>
  <key>com.apple.security.application-groups</key>
  <array/>
  <key>inter-app-audio</key>
  <true/>
  <key>keychain-access-groups</key>
  <array>
    <string>$(AppIdentifierPrefix)renative.helloworld</string>
  </array> -->
</dict>
</plist>
