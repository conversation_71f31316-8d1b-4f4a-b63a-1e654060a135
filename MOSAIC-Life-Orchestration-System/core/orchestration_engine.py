"""
MOSAIC Core Orchestration Engine
The brain that coordinates all aspects of life and business
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from enum import Enum
import asyncio
from dataclasses import dataclass

class LifeDomain(Enum):
    PERSONAL = "personal"
    BUSINESS = "business" 
    HYBRID = "hybrid"

class LifeMode(Enum):
    DEEP_FOCUS = "deep_focus"
    FAMILY_FIRST = "family_first"
    CREATIVE_BURST = "creative_burst"
    ADMIN_TASKS = "admin_tasks"
    RECOVERY = "recovery"
    SOCIAL = "social"
    FLEXIBLE = "flexible"

class EnergyLevel(Enum):
    PEAK = "peak"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    DEPLETED = "depleted"

@dataclass
class LifeContext:
    """Current state across all life dimensions"""
    timestamp: datetime
    location: str
    energy_level: EnergyLevel
    current_mode: LifeMode
    active_projects: List[str]
    upcoming_commitments: List[Dict]
    health_metrics: Dict[str, float]
    relationship_status: Dict[str, Any]
    financial_snapshot: Dict[str, float]
    
@dataclass
class OrchestrationDecision:
    """A decision made by MOSAIC"""
    action_id: str
    action_type: str
    description: str
    domain: LifeDomain
    priority: int
    energy_required: str
    estimated_duration: int
    rationale: str
    confidence: float
    alternatives: List[Dict]
    human_validation_required: bool

class MosaicOrchestrator:
    """Main orchestration engine for MOSAIC"""
    
    def __init__(self):
        self.context_analyzer = ContextAnalyzer()
        self.priority_engine = PriorityEngine()
        self.agent_coordinator = AgentCoordinator()
        self.decision_validator = DecisionValidator()
        self.learning_system = LearningSystem()
        
    async def orchestrate(self, current_state: Dict) -> List[OrchestrationDecision]:
        """Main orchestration loop"""
        # Analyze current context
        context = self.context_analyzer.analyze(current_state)
        
        # Determine optimal mode
        optimal_mode = self._determine_optimal_mode(context)
        
        # Generate priority-sorted action list
        priorities = self.priority_engine.calculate(context, optimal_mode)
        
        # Delegate to specialized agents
        agent_recommendations = await self.agent_coordinator.get_recommendations(
            context, priorities
        )
        
        # Validate and rank decisions
        decisions = self.decision_validator.validate(
            agent_recommendations, context
        )
        
        # Learn from any previous decisions
        self.learning_system.update(context, decisions)
        
        return decisions
    
    def _determine_optimal_mode(self, context: LifeContext) -> LifeMode:
        """Determine the best life mode based on context"""
        hour = context.timestamp.hour
        energy = context.energy_level
        
        # Morning deep work optimization
        if 5 <= hour <= 9 and energy in [EnergyLevel.PEAK, EnergyLevel.HIGH]:
            return LifeMode.DEEP_FOCUS
            
        # Family time protection
        if hour >= 18 and "family_dinner" in context.upcoming_commitments:
            return LifeMode.FAMILY_FIRST
            
        # Low energy admin work
        if energy == EnergyLevel.LOW and hour < 17:
            return LifeMode.ADMIN_TASKS
            
        # Creative bursts
        if energy == EnergyLevel.PEAK and not context.upcoming_commitments:
            return LifeMode.CREATIVE_BURST
            
        return LifeMode.FLEXIBLE