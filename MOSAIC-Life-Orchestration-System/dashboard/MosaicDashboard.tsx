import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Activity, Brain, Heart, DollarSign, Users, Target,
  TrendingUp, Calendar, Battery, Zap
} from 'lucide-react';

interface MosaicMetrics {
  energy: number;
  focus: number;
  health: number;
  revenue: number;
  relationships: number;
  leverage: number;
}

interface LifeMode {
  current: string;
  optimal: string;
  efficiency: number;
}

export const MosaicDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<MosaicMetrics>({
    energy: 82,
    focus: 90,
    health: 88,
    revenue: 180000,
    relationships: 95,
    leverage: 847
  });
  
  const [lifeMode, setLifeMode] = useState<LifeMode>({
    current: 'Deep Focus',
    optimal: 'Deep Focus',
    efficiency: 94
  });
  
  const [activeAgents, setActiveAgents] = useState([
    { name: 'Strategic Advisor', status: 'analyzing', task: 'Q4 opportunities' },
    { name: 'Wellness Guardian', status: 'monitoring', task: 'Energy optimization' },
    { name: 'Revenue Optimizer', status: 'active', task: 'Deal progression' }
  ]);
  
  const [decisions, setDecisions] = useState([
    { 
      time: '2 min ago',
      action: 'Rescheduled low-priority meeting',
      impact: '+45 min deep work',
      confidence: 92
    },
    {
      time: '15 min ago', 
      action: 'Triggered break reminder',
      impact: 'Preventing energy dip',
      confidence: 88
    }
  ]);

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            MOSAIC Command Center
          </h1>
          <p className="text-gray-600 mt-1">Orchestrating your path to unicorn status</p>
        </div>
        <div className="flex items-center space-x-4">
          <Badge variant="outline" className="px-4 py-2">
            <Battery className="w-4 h-4 mr-2" />
            Energy: {metrics.energy}%
          </Badge>
          <Badge className="px-4 py-2 bg-green-500">
            <Zap className="w-4 h-4 mr-2" />
            {lifeMode.current}
          </Badge>
        </div>
      </div>

      {/* Core Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <MetricCard
          title="Energy Level"
          value={`${metrics.energy}%`}
          icon={<Activity className="w-5 h-5" />}
          color="text-green-600"
          trend="+5%"
        />
        <MetricCard
          title="Focus Score"
          value={`${metrics.focus}%`}
          icon={<Brain className="w-5 h-5" />}
          color="text-purple-600"
          trend="+12%"
        />
        <MetricCard
          title="Health Index"
          value={`${metrics.health}%`}
          icon={<Heart className="w-5 h-5" />}
          color="text-red-600"
          trend="+3%"
        />
        <MetricCard
          title="Monthly Revenue"
          value={`$${(metrics.revenue / 1000).toFixed(0)}k`}
          icon={<DollarSign className="w-5 h-5" />}
          color="text-blue-600"
          trend="+22%"
        />
        <MetricCard
          title="Relationships"
          value={`${metrics.relationships}%`}
          icon={<Users className="w-5 h-5" />}
          color="text-orange-600"
          trend="+8%"
        />
        <MetricCard
          title="Leverage"
          value={`${metrics.leverage}x`}
          icon={<Target className="w-5 h-5" />}
          color="text-indigo-600"
          trend="+89x"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Active Agents */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Active AI Agents</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {activeAgents.map((agent, idx) => (
              <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{agent.name}</p>
                  <p className="text-sm text-gray-600">{agent.task}</p>
                </div>
                <Badge variant={agent.status === 'active' ? 'default' : 'secondary'}>
                  {agent.status}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Recent Decisions */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg">Orchestration Decisions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {decisions.map((decision, idx) => (
              <div key={idx} className="border-l-4 border-blue-500 pl-4 py-2">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">{decision.action}</p>
                    <p className="text-sm text-green-600">{decision.impact}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">{decision.time}</p>
                    <p className="text-xs">Confidence: {decision.confidence}%</p>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Unicorn Progress */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-xl">Unicorn Journey Progress</CardTitle>
          <p className="text-sm text-gray-600">Building a $1B company, solo</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium">ARR Progress</span>
                <span className="text-sm">$180k / $100M</span>
              </div>
              <Progress value={0.18} className="h-3" />
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium">Automation Level</span>
                <span className="text-sm">73%</span>
              </div>
              <Progress value={73} className="h-3" />
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium">Life-Work Harmony</span>
                <span className="text-sm">91%</span>
              </div>
              <Progress value={91} className="h-3" />
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
            <p className="text-sm font-medium mb-2">Next Milestone</p>
            <p className="text-2xl font-bold">$500k ARR</p>
            <p className="text-sm text-gray-600">Estimated: 4.2 months</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: string;
  trend: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color, trend }) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <span className={color}>{icon}</span>
          <span className="text-xs text-green-600 font-medium">{trend}</span>
        </div>
        <p className="text-2xl font-bold">{value}</p>
        <p className="text-xs text-gray-600 mt-1">{title}</p>
      </CardContent>
    </Card>
  );
};

export default MosaicDashboard;