{"name": "@midday/api", "scripts": {"dev": "TZ=UTC PORT=3003 bun run --hot src/index.ts", "db:pull": "drizzle-kit pull", "lint": "biome check .", "format": "biome format --write .", "typecheck": "tsc --noEmit", "test": "bun test", "test:watch": "bun test --watch"}, "exports": {"./trpc/routers/_app": "./src/trpc/routers/_app.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@date-fns/utc": "^2.1.0", "@hono/trpc-server": "^0.3.4", "@hono/zod-openapi": "^0.19.8", "@hono/zod-validator": "^0.7.0", "@midday/documents": "workspace:*", "@midday/encryption": "workspace:*", "@midday/engine": "workspace:*", "@midday/engine-client": "workspace:*", "@midday/inbox": "workspace:*", "@midday/invoice": "workspace:*", "@midday/jobs": "workspace:*", "@midday/location": "workspace:*", "@midday/supabase": "workspace:*", "@scalar/hono-api-reference": "^0.9.4", "@sindresorhus/slugify": "^2.2.1", "@trigger.dev/sdk": "3.3.17", "@trpc/server": "^11.4.1", "ai": "^4.3.16", "camelcase-keys": "^9.1.3", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "hono": "^4.7.11", "hono-rate-limiter": "^0.4.2", "jose": "^6.0.11", "lru-cache": "^11.1.0", "nanoid": "^5.1.5", "pino": "^9.7.0", "postgres": "^3.4.7", "resend": "^4.6.0", "snakecase-keys": "^8.0.1", "superjson": "^2.2.2", "uuid": "^11.1.0", "zod": "^3.25.64", "zod-openapi": "^4.2.4"}, "devDependencies": {"@types/bun": "^1.2.16", "drizzle-kit": "^0.31.1"}}