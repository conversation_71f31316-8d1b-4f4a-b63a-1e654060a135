{"editor.formatOnSave": true, "typescript.tsdk": "node_modules\\typescript\\lib", "typescript.enablePromptUseWorkspaceTsdk": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}