# MOSAIC Marketing Site Refactor Plan

## Goals
1. Rebrand existing BaseHub-powered marketing site to ALIAS MOSAIC.
2. Keep BaseHub CMS – all copy, lifecycles, pricing, personas editable in studio.
3. Integrate premium **mvpblocks** UI blocks for modern look & feel.
4. Maintain AI-driven workflow via **Task Master** from DotAI template.
5. Preserve accessibility, SEO, performance budgets.

---

## Phase 1 – Project Scaffolding *(Day 0)*
| Task | Owner | Details |
| ---- | ----- | ------- |
| Clone `alias-mosaic`, `mvpblocks`, `dotai` | done | ✓ |
| Copy `.taskmaster` scaffold → marketing repo | done | ✓ |
| Create 4 tags (`hero-upgrade`, `lifecycle-sections`, `pricing-section`, `mvpblocks-ui-swap`) | done | ✓ |
| Add `plan.md` | 🟢 | this file |

---

## Phase 2 – CMS Schema Extension *(Day 1)*
1. **Hero** – single-entry (`Hero`): title, subtitle, ctaLabel, ctaHref, stats[]
2. **Lifecycle** – collection: id, title, subtitle, order, icon, body (rich-text)
3. **PricingTier** – collection: id, name, priceMonthly, features[]
4. **Stat** – collection referenced by Hero.
5. **Settings** – global: metaTitle, metaDescription, openGraphImage

_Update BaseHub schema via Studio & commit generated GraphQL fragments._

---

## Phase 3 – Data Layer *(Day 1)*
- Add queries in `src/lib/basehub/fragments.ts`:
  ```ts
  export const HERO_QUERY = gql`{ hero { title subtitle ctaLabel ctaHref } }`;
  export const LIFECYCLES_QUERY = gql`{ lifecycles(orderBy: order_ASC) { ... } }`;
  ```
- Generate types with `graphql-codegen`.

---

## Phase 4 – UI Integration *(Days 2–3)*
### 4.1 Header / Footer  
Replace existing header & footer with mvpblocks `header-2` & `footer-glow`:
- Copy components → `src/components/mvpblocks/...`
- Create wrapper to inject navigation links from BaseHub (`menus` collection).

### 4.2 Hero  
Tag **hero-upgrade**:
- Import `minimal-hero` component at `src/app/_sections/hero/index.tsx`.
- Map BaseHub fields to props.
- Remove old animation libs if unused.

### 4.3 Lifecycles Grid  
Tag **lifecycle-sections**:
- New section `src/app/_sections/lifecycles-grid` using mvpblocks `side-features` cards.
- Fetch lifecycles via GraphQL.
- `/lifecycles` index page + dynamic `[id]` page with rich body.

### 4.4 Pricing  
Tag **pricing-section**:
- Swap to `designer-pricing` block.
- Bind tiers from `PricingTier` CMS.

### 4.5 Testimonials, FAQ, Newsletter  
Reuse existing alias-mosaic blocks; only update copy.

---

## Phase 5 – Styling & Theme *(Day 3)*
- Centralise Tailwind theme tokens (colors, font) to match MOSAIC palette.
- Ensure dark/light mode works with `next-themes`.

---

## Phase 6 – Task Master Automation *(Continuous)*
- For each phase create subtasks under relevant tag.
- Use `/task:next` and `/task:done` to drive implementation.

---

## Phase 7 – QA & Deployment *(Day 4)*
- Lighthouse ≥ 90 on PWA, Perf, SEO.
- Responsive checks (mobile, tablet, desktop).
- Vercel preview.
- Stakeholder review.

---

## Phase 8 – Stretch
- Add interactive Home-Assistant demo widget (Convex websocket → mock data).
- Integrate DotAI animated backgrounds in Hero.

---

## Environment Setup
Add `.env.local` (git-ignored):
```
BASEHUB_TOKEN=bshb_pk_uj8wq04g2i9gswggt0qrq9vhz97ygk7aa3eudslcz5q97yf8biennrmf3b4g3lha
```

---

## Timeline
| Day | Deliverable |
| --- | ----------- |
| 0   | Repo scaffolding, plan.md |
| 1   | CMS schema + GraphQL layer |
| 2   | Header, Footer, Hero integrated |
| 3   | Lifecycles grid, Pricing, theme |
| 4   | QA, content entry, deploy preview |

---

© 2025 ALIAS – Proprietary