.code-snippet {
  display: flex;
  flex-direction: column;
  background-color: theme("colors.surface.secondary");
  border-radius: theme("borderRadius.xl");
  margin: theme("spacing.4") 0px;
  border: 1px solid theme("colors.border.DEFAULT");
  width: 100%;
  overflow: hidden;

  .header {
    padding: theme("spacing.2") theme("spacing.4");
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid theme("colors.border.DEFAULT");
  }
  .content {
    overflow-x: auto;
    background-color: theme("colors.surface.secondary");
    position: relative;

    :global(pre) {
      margin: 0 !important;
      font-size: theme("fontSize.sm") !important;
      // white-space: pre-wrap;
      word-wrap: break-word;
      background-color: theme("colors.surface.secondary") !important;
      outline-color: theme("colors.accent.500");

      :global(span.line-indicator) {
        display: inline-block;
        pointer-events: none;
        position: sticky;
        left: 0;
        background-color: theme("colors.surface.secondary");
        color: theme("colors.text.tertiary");
        user-select: none;
        width: 44px !important;
        text-align: end;
        padding-right: 12px !important;
      }
    }

    --shiki-color-text: theme("colors.grayscale.950");
    --shiki-background: theme("colors.surface.secondary");
    --shiki-token-constant: theme("colors.accent.700");
    --shiki-token-function: theme("colors.grayscale.600");
    --shiki-token-string-expression: theme("colors.grayscale.500");
    --shiki-token-string: theme("colors.accent.400");
    --shiki-token-comment: theme("colors.grayscale.400");
    --shiki-token-keyword: theme("colors.accent.500");
    --shiki-token-parameter: #d6deeb;
    --shiki-token-punctuation: #c792e9;
    --shiki-token-link: #79b8ff;
  }
}

:global(.dark) {
  .code-snippet {
    background-color: theme("colors.dark.surface.secondary");
    border: 1px solid theme("colors.dark.border.DEFAULT");
    .header {
      border-bottom: 1px solid theme("colors.dark.border.DEFAULT");
    }
    .content {
      background-color: theme("colors.dark.surface.secondary");
      :global(pre) {
        background-color: theme("colors.dark.surface.secondary") !important;

        :global(span.line-indicator) {
          color: theme("colors.dark.text.tertiary");
          background-color: theme("colors.dark.surface.secondary");
        }
      }

      --shiki-color-text: theme("colors.grayscale.950");
      --shiki-background: theme("colors.dark.surface.secondary");
      --shiki-token-constant: theme("colors.accent.200");
      --shiki-token-function: theme("colors.grayscale.400");
      --shiki-token-string-expression: theme("colors.grayscale.500");
      --shiki-token-string: theme("colors.accent.400");
      --shiki-token-comment: theme("colors.grayscale.600");
      --shiki-token-keyword: theme("colors.accent.500");
      --shiki-token-parameter: #d6deeb;
      --shiki-token-punctuation: #c792e9;
    }
  }
}
