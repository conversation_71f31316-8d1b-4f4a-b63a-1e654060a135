// @ts-nocheck
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/*=============================================================================
 * This file was automatically generated by the BaseHub SDK and contains type
 * definitions based on your repository schema. Credits to https://genql.dev/
 * for the type generation.
 *
 * You can safely commit this to version control.
 *============================================================================*/

declare module "basehub" {
  export interface Query extends _Query {}
  export interface QueryGenqlSelection extends _QueryGenqlSelection {}
  export interface Mutation extends _Mutation {}
  export interface MutationGenqlSelection extends _MutationGenqlSelection {}
  export interface FragmentsMap extends _FragmentsMap {}
  export interface Scalars extends _Scalars {}
}

import type { Transaction } from 'basehub/api-transaction'

interface _Query extends Query {}
interface _QueryGenqlSelection extends QueryGenqlSelection {}
interface _Mutation extends Mutation {}
interface _MutationGenqlSelection extends MutationGenqlSelection {}
interface _FragmentsMap extends FragmentsMap {}
interface _Scalars extends Scalars {}

export interface Scalars {
    BSHBEventSchema: ({
  name: string;
  required: boolean;
  placeholder?: string;
  defaultValue?: string;
  helpText?: string
} & {
  id: string;
  label: string
} & ({
  type: "text" | "textarea" | "number" | "date" | "datetime" | "email" | "checkbox" | "hidden"
} | {
  type: "select" | "radio";
  options: string[];
  multiple: boolean
} | {
  type: "file";
  private: boolean
}))[],
    BSHBRichTextContentSchema: RichTextNode[],
    BSHBRichTextTOCSchema: RichTextTocNode[],
    BSHBSelect_1837708130: 'Efficiency' | 'Guide' | 'Productivity' | 'Success',
    BSHBSelect_1867466044: 'primary' | 'secondary' | 'tertiary',
    BSHBSelect_1885808492: 'full image' | 'gradient' | 'Image bottom' | 'Image Right',
    BSHBSelect_1960434833: 'large',
    BSHBSelect_262811106: 'amber' | 'blue' | 'cyan' | 'emerald' | 'fuchsia' | 'gray' | 'green' | 'indigo' | 'lime' | 'neutral' | 'orange' | 'pink' | 'purple' | 'red' | 'rose' | 'sky' | 'slate' | 'stone' | 'teal' | 'violet' | 'yellow' | 'zinc',
    BSHBSelect_604783081: 'accordion' | 'list',
    BSHBSelect__108045351: 'info' | 'warning',
    BSHBSelect__1086199789: 'right' | 'center' | 'left' | 'none',
    BSHBSelect__1721217313: 'gray' | 'neutral' | 'slate' | 'stone' | 'zinc',
    Boolean: boolean,
    CodeSnippetLanguage: B_Language,
    DateTime: any,
    Float: number,
    ID: string,
    Int: number,
    JSON: any,
    String: string,
    bshb_event_199695012: `bshb_event_199695012:${string}`,
    schema_bshb_event_199695012: {email: string;},
    bshb_event_514328939: `bshb_event_514328939:${string}`,
    schema_bshb_event_514328939: never,
    bshb_event__272409232: `bshb_event__272409232:${string}`,
    schema_bshb_event__272409232: {eventType: string;},
}

export type AnalyticsKeyScope = 'query' | 'send'

export interface AuthorComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    company: CompanyComponent
    image: BlockImage
    role: Scalars['String']
    x: (Scalars['String'] | null)
    __typename: 'AuthorComponent'
}

export type AuthorComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'company__ASC' | 'company__DESC' | 'image__ASC' | 'image__DESC' | 'role__ASC' | 'role__DESC' | 'x__ASC' | 'x__DESC'

export interface Authors {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (AuthorComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: AuthorComponent[]
    __typename: 'Authors'
}

export interface AvatarComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    avatar: BlockImage
    __typename: 'AvatarComponent'
}

export type AvatarComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'avatar__ASC' | 'avatar__DESC'

export interface Avatars {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (AvatarComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: AvatarComponent[]
    __typename: 'Avatars'
}

export interface BaseRichTextJson {
    blocks: Scalars['String']
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'BaseRichTextJson'
}

export interface BlockAudio {
    /** The duration of the audio in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration: Scalars['Float']
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    url: Scalars['String']
    __typename: 'BlockAudio'
}

export interface BlockCodeSnippet {
    allowedLanguages: Scalars['CodeSnippetLanguage'][]
    code: Scalars['String']
    /** @deprecated Figuring out the correct api. */
    html: Scalars['String']
    language: Scalars['CodeSnippetLanguage']
    __typename: 'BlockCodeSnippet'
}

export interface BlockColor {
    b: Scalars['Int']
    g: Scalars['Int']
    hex: Scalars['String']
    hsl: Scalars['String']
    r: Scalars['Int']
    rgb: Scalars['String']
    __typename: 'BlockColor'
}

export type BlockDocument = (AuthorComponent | Authors | AvatarComponent | Avatars | Blog | BlogPostComponent | BooleanComponent | ButtonComponent | CalloutComponent | CalloutV2Component | Categories | CategoryComponent | Changelog | ChangelogPostComponent | Characteristics | CharacteristicsItem | CodeSnippetComponent | Collections | CompaniesComponent | CompanyComponent | ComparisonOptions | Components | CustomTextComponent | CustomerSatisfactionBannerComponent | DarkLightImageComponent | FaqComponent | FaqItemComponent | FeatureComponent | FeatureHeroComponent | FeatureWithIconComponent | Features | FeaturesBigImageComponent | FeaturesBigImageList | FeaturesCardsComponent | FeaturesCardsList | FeaturesCardsListItem | FeaturesGridComponent | FeaturesGridList | FeaturesSideBySideComponent | FeaturesSideBySideList | FeaturesSideBySideListItem | Footer | FormComponent | FormWrapperComponent | FreeformTextComponent | Header | HeaderNavbarLinkComponent | HeadingComponent | HeroComponent | List | ListItem | Metadata | MetadataOverridesComponent | MoreCompanies | Navbar | NavbarItem | Navbar_1 | Newsletter | PageReferenceComponent | Pages | PagesItem | PlanComponent | Plans | Posts | Posts_1 | PricingComponent | PricingPlanComponent | PricingPlans | PricingTableComponent | Questions | QuoteComponent | Quotes | RichTextCalloutComponent | RightCtas | Sections | Settings | Site | SocialLinkComponent | SocialLinks | Sublinks | SublinksItem | TestimonialSliderComponent | TestimonialsGridComponent | Theme | ValueComponent | Values | authorComponent_AsList | avatarComponent_AsList | blogPostComponent_AsList | booleanComponent_AsList | buttonComponent_AsList | calloutComponent_AsList | calloutV2Component_AsList | categoryComponent_AsList | changelogPostComponent_AsList | characteristicsItem_AsList | codeSnippetComponent_AsList | companiesComponent_AsList | companyComponent_AsList | customTextComponent_AsList | customerSatisfactionBannerComponent_AsList | darkLightImageComponent_AsList | faqComponent_AsList | faqItemComponent_AsList | featureComponent_AsList | featureHeroComponent_AsList | featureWithIconComponent_AsList | featuresBigImageComponent_AsList | featuresCardsComponent_AsList | featuresCardsListItem_AsList | featuresGridComponent_AsList | featuresSideBySideComponent_AsList | featuresSideBySideListItem_AsList | formComponent_AsList | formWrapperComponent_AsList | freeformTextComponent_AsList | headerNavbarLinkComponent_AsList | headingComponent_AsList | heroComponent_AsList | listItem_AsList | metadataOverridesComponent_AsList | navbarItem_AsList | pageReferenceComponent_AsList | pagesItem_AsList | planComponent_AsList | pricingComponent_AsList | pricingPlanComponent_AsList | pricingTableComponent_AsList | quoteComponent_AsList | richTextCalloutComponent_AsList | socialLinkComponent_AsList | sublinksItem_AsList | testimonialSliderComponent_AsList | testimonialsGridComponent_AsList | valueComponent_AsList) & { __isUnion?: true }

export interface BlockDocumentSys {
    apiNamePath: Scalars['String']
    createdAt: Scalars['String']
    hash: Scalars['String']
    id: Scalars['ID']
    idPath: Scalars['String']
    lastModifiedAt: Scalars['String']
    slug: Scalars['String']
    slugPath: Scalars['String']
    title: Scalars['String']
    __typename: 'BlockDocumentSys'
}

export interface BlockFile {
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    url: Scalars['String']
    __typename: 'BlockFile'
}

export interface BlockImage {
    alt: (Scalars['String'] | null)
    aspectRatio: Scalars['String']
    blurDataURL: Scalars['String']
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    height: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    /** @deprecated Renamed to `blurDataURL` to match Next.js Image's naming convention. */
    placeholderURL: Scalars['String']
    /** @deprecated Use `url` instead. */
    rawUrl: Scalars['String']
    thumbhash: Scalars['String']
    /**
     * This field is used to generate the image URL with the provided options. The options are passed as arguments. For example, if you want to resize the image to 200x200 pixels, you can use the following query:
     * 
     * ```graphql
     * {
     *   imageBlock {
     *     url(width: 200, height: 200)
     *   }
     * }
     * ```
     * 
     * This will return the URL with the width and height set to 200 pixels.
     * 
     * BaseHub uses Cloudflare for image resizing. Check out [all available options in their docs](https://developers.cloudflare.com/images/transform-images/transform-via-workers/#fetch-options).
     * 
     */
    url: Scalars['String']
    width: Scalars['Int']
    __typename: 'BlockImage'
}

export type BlockList = (Authors | Avatars | Categories | Characteristics | Features | FeaturesBigImageList | FeaturesCardsList | FeaturesGridList | FeaturesSideBySideList | List | MoreCompanies | Navbar | Navbar_1 | Pages | Plans | Posts | Posts_1 | PricingPlans | Questions | Quotes | RightCtas | SocialLinks | Sublinks | Values | authorComponent_AsList | avatarComponent_AsList | blogPostComponent_AsList | booleanComponent_AsList | buttonComponent_AsList | calloutComponent_AsList | calloutV2Component_AsList | categoryComponent_AsList | changelogPostComponent_AsList | characteristicsItem_AsList | codeSnippetComponent_AsList | companiesComponent_AsList | companyComponent_AsList | customTextComponent_AsList | customerSatisfactionBannerComponent_AsList | darkLightImageComponent_AsList | faqComponent_AsList | faqItemComponent_AsList | featureComponent_AsList | featureHeroComponent_AsList | featureWithIconComponent_AsList | featuresBigImageComponent_AsList | featuresCardsComponent_AsList | featuresCardsListItem_AsList | featuresGridComponent_AsList | featuresSideBySideComponent_AsList | featuresSideBySideListItem_AsList | formComponent_AsList | formWrapperComponent_AsList | freeformTextComponent_AsList | headerNavbarLinkComponent_AsList | headingComponent_AsList | heroComponent_AsList | listItem_AsList | metadataOverridesComponent_AsList | navbarItem_AsList | pageReferenceComponent_AsList | pagesItem_AsList | planComponent_AsList | pricingComponent_AsList | pricingPlanComponent_AsList | pricingTableComponent_AsList | quoteComponent_AsList | richTextCalloutComponent_AsList | socialLinkComponent_AsList | sublinksItem_AsList | testimonialSliderComponent_AsList | testimonialsGridComponent_AsList | valueComponent_AsList) & { __isUnion?: true }

export interface BlockOgImage {
    height: Scalars['Int']
    url: Scalars['String']
    width: Scalars['Int']
    __typename: 'BlockOgImage'
}


/** Rich text block */
export type BlockRichText = (Body | Body_1 | Body_2 | Content | Subtitle | Subtitle_1) & { __isUnion?: true }

export interface BlockVideo {
    aspectRatio: Scalars['String']
    /** The duration of the video in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration: Scalars['Float']
    fileName: Scalars['String']
    fileSize: Scalars['Int']
    height: Scalars['Int']
    lastModified: Scalars['Float']
    mimeType: Scalars['String']
    url: Scalars['String']
    width: Scalars['Int']
    __typename: 'BlockVideo'
}

export interface Blog {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    featuredPosts: (BlogPostComponent[] | null)
    listTitle: Scalars['String']
    mainTitle: Scalars['String']
    metadata: MetadataOverridesComponent
    posts: Posts
    __typename: 'Blog'
}

export interface BlogPostComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    authors: AuthorComponent[]
    body: Body_1
    categories: Scalars['BSHBSelect_1837708130'][]
    description: Scalars['String']
    image: DarkLightImageComponent
    ogImage: BlockOgImage
    /** ISO 8601 date string. */
    publishedAt: Scalars['String']
    __typename: 'BlogPostComponent'
}

export type BlogPostComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'authors__ASC' | 'authors__DESC' | 'body__ASC' | 'body__DESC' | 'categories__ASC' | 'categories__DESC' | 'description__ASC' | 'description__DESC' | 'image__ASC' | 'image__DESC' | 'ogImage__ASC' | 'ogImage__DESC' | 'publishedAt__ASC' | 'publishedAt__DESC'

export interface Body {
    html: Scalars['String']
    json: BodyRichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Body'
}

export interface BodyRichText {
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'BodyRichText'
}

export interface Body_1 {
    html: Scalars['String']
    json: Body_1RichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Body_1'
}

export interface Body_1RichText {
    blocks: UnionFaqItemComponentCodeSnippetComponentRichTextCalloutComponent[]
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'Body_1RichText'
}

export interface Body_2 {
    html: Scalars['String']
    json: Body_2RichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Body_2'
}

export interface Body_2RichText {
    blocks: UnionCodeSnippetComponent[]
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'Body_2RichText'
}

export interface BooleanComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    boolean: Scalars['Boolean']
    __typename: 'BooleanComponent'
}

export type BooleanComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'boolean__ASC' | 'boolean__DESC'

export interface ButtonComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    href: Scalars['String']
    icon: (Scalars['String'] | null)
    label: Scalars['String']
    type: Scalars['BSHBSelect_1867466044']
    __typename: 'ButtonComponent'
}

export type ButtonComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'href__ASC' | 'href__DESC' | 'icon__ASC' | 'icon__DESC' | 'label__ASC' | 'label__DESC' | 'type__ASC' | 'type__DESC'

export interface CalloutComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    actions: (ButtonComponent[] | null)
    subtitle: (Scalars['String'] | null)
    title: (Scalars['String'] | null)
    __typename: 'CalloutComponent'
}

export type CalloutComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'actions__ASC' | 'actions__DESC' | 'subtitle__ASC' | 'subtitle__DESC' | 'title__ASC' | 'title__DESC'

export interface CalloutV2Component {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    actions: (ButtonComponent[] | null)
    subtitle: (Scalars['String'] | null)
    title: (Scalars['String'] | null)
    __typename: 'CalloutV2Component'
}

export type CalloutV2ComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'actions__ASC' | 'actions__DESC' | 'subtitle__ASC' | 'subtitle__DESC' | 'title__ASC' | 'title__DESC'

export interface Categories {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CategoryComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CategoryComponent[]
    __typename: 'Categories'
}

export interface CategoryComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    features: Features
    tooltip: (Scalars['String'] | null)
    __typename: 'CategoryComponent'
}

export type CategoryComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'features__ASC' | 'features__DESC' | 'tooltip__ASC' | 'tooltip__DESC'

export interface Changelog {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    goBackText: Scalars['String']
    metadata: MetadataOverridesComponent
    posts: Posts_1
    socialLinks: SocialLinkComponent[]
    socialLinksTitle: Scalars['String']
    subtitle: Scalars['String']
    title: Scalars['String']
    __typename: 'Changelog'
}

export interface ChangelogPostComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    authors: AuthorComponent[]
    body: Body_2
    excerpt: Scalars['String']
    image: BlockImage
    ogImage: BlockOgImage
    /** ISO 8601 date string. */
    publishedAt: Scalars['String']
    __typename: 'ChangelogPostComponent'
}

export type ChangelogPostComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'authors__ASC' | 'authors__DESC' | 'body__ASC' | 'body__DESC' | 'excerpt__ASC' | 'excerpt__DESC' | 'image__ASC' | 'image__DESC' | 'ogImage__ASC' | 'ogImage__DESC' | 'publishedAt__ASC' | 'publishedAt__DESC'

export interface Characteristics {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CharacteristicsItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CharacteristicsItem[]
    __typename: 'Characteristics'
}

export interface CharacteristicsItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    __typename: 'CharacteristicsItem'
}

export type CharacteristicsItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC'

export interface CodeSnippetComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    code: BlockCodeSnippet
    __typename: 'CodeSnippetComponent'
}

export type CodeSnippetComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'code__ASC' | 'code__DESC'

export interface Collections {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    authors: Authors
    moreCompanies: MoreCompanies
    pricingPlans: PricingPlans
    quotes: Quotes
    socialLinks: SocialLinks
    __typename: 'Collections'
}

export interface CompaniesComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    companies: CompanyComponent[]
    subtitle: (Scalars['String'] | null)
    __typename: 'CompaniesComponent'
}

export type CompaniesComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'companies__ASC' | 'companies__DESC' | 'subtitle__ASC' | 'subtitle__DESC'

export interface CompanyComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    image: (BlockImage | null)
    url: (Scalars['String'] | null)
    __typename: 'CompanyComponent'
}

export type CompanyComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'image__ASC' | 'image__DESC' | 'url__ASC' | 'url__DESC'

export interface ComparisonOptions {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    __typename: 'ComparisonOptions'
}

export interface Components {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    __typename: 'Components'
}

export interface Content {
    html: Scalars['String']
    json: ContentRichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Content'
}

export interface ContentRichText {
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'ContentRichText'
}

export interface CustomTextComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    text: Scalars['String']
    __typename: 'CustomTextComponent'
}

export type CustomTextComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'text__ASC' | 'text__DESC'

export interface CustomerSatisfactionBannerComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    avatars: Avatars
    text: Scalars['String']
    __typename: 'CustomerSatisfactionBannerComponent'
}

export type CustomerSatisfactionBannerComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'avatars__ASC' | 'avatars__DESC' | 'text__ASC' | 'text__DESC'

export interface DarkLightImageComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    dark: (BlockImage | null)
    light: BlockImage
    __typename: 'DarkLightImageComponent'
}

export type DarkLightImageComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'dark__ASC' | 'dark__DESC' | 'light__ASC' | 'light__DESC'

export interface FaqComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    heading: HeadingComponent
    layout: (Scalars['BSHBSelect_604783081'] | null)
    questions: Questions
    __typename: 'FaqComponent'
}

export type FaqComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'heading__ASC' | 'heading__DESC' | 'layout__ASC' | 'layout__DESC' | 'questions__ASC' | 'questions__DESC'


/** Item of Frequent Answered Question */
export interface FaqItemComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    answer: Scalars['String']
    __typename: 'FaqItemComponent'
}

export type FaqItemComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'answer__ASC' | 'answer__DESC'

export interface FeatureComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    tooltip: (Scalars['String'] | null)
    values: Values
    __typename: 'FeatureComponent'
}

export type FeatureComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'tooltip__ASC' | 'tooltip__DESC' | 'values__ASC' | 'values__DESC'

export interface FeatureHeroComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    actions: (ButtonComponent[] | null)
    heading: HeadingComponent
    heroLayout: Scalars['BSHBSelect_1885808492']
    image: DarkLightImageComponent
    __typename: 'FeatureHeroComponent'
}

export type FeatureHeroComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'actions__ASC' | 'actions__DESC' | 'heading__ASC' | 'heading__DESC' | 'heroLayout__ASC' | 'heroLayout__DESC' | 'image__ASC' | 'image__DESC'

export interface FeatureWithIconComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    description: Scalars['String']
    icon: BlockImage
    __typename: 'FeatureWithIconComponent'
}

export type FeatureWithIconComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'description__ASC' | 'description__DESC' | 'icon__ASC' | 'icon__DESC'

export interface Features {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeatureComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeatureComponent[]
    __typename: 'Features'
}

export interface FeaturesBigImageComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    featuresBigImageList: FeaturesBigImageList
    heading: HeadingComponent
    image: DarkLightImageComponent
    __typename: 'FeaturesBigImageComponent'
}

export type FeaturesBigImageComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'featuresBigImageList__ASC' | 'featuresBigImageList__DESC' | 'heading__ASC' | 'heading__DESC' | 'image__ASC' | 'image__DESC'

export interface FeaturesBigImageList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeatureWithIconComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeatureWithIconComponent[]
    __typename: 'FeaturesBigImageList'
}

export interface FeaturesCardsComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    featuresCardsList: FeaturesCardsList
    heading: HeadingComponent
    __typename: 'FeaturesCardsComponent'
}

export type FeaturesCardsComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'featuresCardsList__ASC' | 'featuresCardsList__DESC' | 'heading__ASC' | 'heading__DESC'

export interface FeaturesCardsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesCardsListItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesCardsListItem[]
    __typename: 'FeaturesCardsList'
}

export interface FeaturesCardsListItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    characteristics: Characteristics
    description: Scalars['String']
    image: DarkLightImageComponent
    __typename: 'FeaturesCardsListItem'
}

export type FeaturesCardsListItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'characteristics__ASC' | 'characteristics__DESC' | 'description__ASC' | 'description__DESC' | 'image__ASC' | 'image__DESC'

export interface FeaturesGridComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    actions: (ButtonComponent[] | null)
    featuresGridList: FeaturesGridList
    heading: HeadingComponent
    __typename: 'FeaturesGridComponent'
}

export type FeaturesGridComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'actions__ASC' | 'actions__DESC' | 'featuresGridList__ASC' | 'featuresGridList__DESC' | 'heading__ASC' | 'heading__DESC'

export interface FeaturesGridList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeatureWithIconComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeatureWithIconComponent[]
    __typename: 'FeaturesGridList'
}

export interface FeaturesSideBySideComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    actions: (ButtonComponent[] | null)
    featuresSideBySideList: FeaturesSideBySideList
    heading: HeadingComponent
    __typename: 'FeaturesSideBySideComponent'
}

export type FeaturesSideBySideComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'actions__ASC' | 'actions__DESC' | 'featuresSideBySideList__ASC' | 'featuresSideBySideList__DESC' | 'heading__ASC' | 'heading__DESC'

export interface FeaturesSideBySideList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesSideBySideListItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesSideBySideListItem[]
    __typename: 'FeaturesSideBySideList'
}

export interface FeaturesSideBySideListItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    icon: BlockImage
    subtitle: Scalars['String']
    __typename: 'FeaturesSideBySideListItem'
}

export type FeaturesSideBySideListItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'icon__ASC' | 'icon__DESC' | 'subtitle__ASC' | 'subtitle__DESC'

export interface Footer {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    copyright: (Scalars['String'] | null)
    navbar: Navbar_1
    newsletter: Newsletter
    socialLinks: SocialLinkComponent[]
    __typename: 'Footer'
}

export interface FormComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    cta: ButtonComponent
    submissions: Submissions
    subtitle: (Subtitle | null)
    title: Scalars['String']
    __typename: 'FormComponent'
}

export type FormComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'cta__ASC' | 'cta__DESC' | 'submissions__ASC' | 'submissions__DESC' | 'subtitle__ASC' | 'subtitle__DESC' | 'title__ASC' | 'title__DESC'

export interface FormWrapperComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    cta: ButtonComponent
    subtitle: (Subtitle_1 | null)
    title: Scalars['String']
    __typename: 'FormWrapperComponent'
}

export type FormWrapperComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'cta__ASC' | 'cta__DESC' | 'subtitle__ASC' | 'subtitle__DESC' | 'title__ASC' | 'title__DESC'

export interface FreeformTextComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    body: Body
    __typename: 'FreeformTextComponent'
}

export type FreeformTextComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'body__ASC' | 'body__DESC'

export interface GeneralEvents {
    /** The `adminKey` gives clients the ability to query, delete and update this block's data. **It's not meant to be exposed to the public.** */
    adminKey: Scalars['bshb_event__272409232']
    /** The `ingestKey` gives clients the ability to send new events to this block. Generally, it's safe to expose it to the public. */
    ingestKey: Scalars['bshb_event__272409232']
    schema: Scalars['BSHBEventSchema']
    __typename: 'GeneralEvents'
}

export interface GetUploadSignedURL {
    signedURL: Scalars['String']
    uploadURL: Scalars['String']
    __typename: 'GetUploadSignedURL'
}

export interface Header {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    navbar: Navbar
    rightCtas: RightCtas
    __typename: 'Header'
}

export interface HeaderNavbarLinkComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    href: (Scalars['String'] | null)
    sublinks: Sublinks
    __typename: 'HeaderNavbarLinkComponent'
}

export type HeaderNavbarLinkComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'href__ASC' | 'href__DESC' | 'sublinks__ASC' | 'sublinks__DESC'

export interface HeadingComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    align: (Scalars['BSHBSelect__1086199789'] | null)
    subtitle: (Scalars['String'] | null)
    tag: (Scalars['String'] | null)
    title: Scalars['String']
    __typename: 'HeadingComponent'
}

export type HeadingComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'align__ASC' | 'align__DESC' | 'subtitle__ASC' | 'subtitle__DESC' | 'tag__ASC' | 'tag__DESC' | 'title__ASC' | 'title__DESC'

export interface HeroComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    actions: (ButtonComponent[] | null)
    customerSatisfactionBanner: CustomerSatisfactionBannerComponent
    subtitle: (Scalars['String'] | null)
    title: (Scalars['String'] | null)
    __typename: 'HeroComponent'
}

export type HeroComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'actions__ASC' | 'actions__DESC' | 'customerSatisfactionBanner__ASC' | 'customerSatisfactionBanner__DESC' | 'subtitle__ASC' | 'subtitle__DESC' | 'title__ASC' | 'title__DESC'

export interface List {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ListItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ListItem[]
    __typename: 'List'
}

export interface ListItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    __typename: 'ListItem'
}

export type ListItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC'

export interface ListMeta {
    /** Number of items after applying filters but before pagination */
    filteredCount: Scalars['Int']
    /** Total number of items in collection before any filtering/pagination */
    totalCount: Scalars['Int']
    __typename: 'ListMeta'
}

export type MediaBlock = (BlockAudio | BlockFile | BlockImage | BlockVideo) & { __isUnion?: true }

export type MediaBlockUnion = (BlockAudio | BlockFile | BlockImage | BlockVideo) & { __isUnion?: true }

export interface Metadata {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    defaultDescription: Scalars['String']
    defaultTitle: Scalars['String']
    favicon: BlockImage
    ogImage: BlockOgImage
    sitename: Scalars['String']
    titleTemplate: Scalars['String']
    xAccount: (SocialLinkComponent | null)
    __typename: 'Metadata'
}

export interface MetadataOverridesComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    description: (Scalars['String'] | null)
    title: (Scalars['String'] | null)
    __typename: 'MetadataOverridesComponent'
}

export type MetadataOverridesComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'description__ASC' | 'description__DESC' | 'title__ASC' | 'title__DESC'

export interface MoreCompanies {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CompanyComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CompanyComponent[]
    __typename: 'MoreCompanies'
}

export interface Mutation {
    /**
     * Returns a signed url and an upload url so that you can upload files into your repository.
     * 
     * Example usage with JavaScript:
     * ```js
     * async function handleUpload(file: File) {
     *   const { getUploadSignedURL } = await basehub().mutation({
     *     getUploadSignedURL: {
     *       __args: { fileName: file.name },
     *       signedURL: true,
     *       uploadURL: true,
     *     }
     *   })
     * 
     *   const { signedURL, uploadURL } = getUploadSignedURL
     * 
     *   await fetch(signedURL, { method: 'PUT', body: file })
     * 
     *   // done! do something with the uploadURL now
     * }
     * ```
     * 
     */
    getUploadSignedURL: GetUploadSignedURL
    /** Start a job that can be awaited and the result given directly. Under the hood, it runs `transactionAsync` and polls for the result until it is available. You can pass a `timeout` argument, the default being 30_000ms. */
    transaction: TransactionStatus
    /** Start an asynchronous job to mutate BaseHub data. Returns a transaction ID which you can use to get the result of the job. */
    transactionAsync: Scalars['String']
    transactionStatus: TransactionStatus
    __typename: 'Mutation'
}

export interface Navbar {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (HeaderNavbarLinkComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: HeaderNavbarLinkComponent[]
    __typename: 'Navbar'
}

export interface NavbarItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    url: (Scalars['String'] | null)
    __typename: 'NavbarItem'
}

export type NavbarItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'url__ASC' | 'url__DESC'

export interface Navbar_1 {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (NavbarItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: NavbarItem[]
    __typename: 'Navbar_1'
}

export interface Newsletter {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    description: (Scalars['String'] | null)
    submissions: Submissions_1
    title: (Scalars['String'] | null)
    __typename: 'Newsletter'
}

export interface PageReferenceComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    page: PagesItem
    __typename: 'PageReferenceComponent'
}

export type PageReferenceComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'page__ASC' | 'page__DESC'

export interface Pages {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PagesItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PagesItem[]
    __typename: 'Pages'
}

export interface PagesItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    metadataOverrides: MetadataOverridesComponent
    pathname: Scalars['String']
    sections: (UnionFreeformTextComponentFeaturesCardsComponentCalloutComponentCompaniesComponentFaqComponentFeaturesBigImageComponentFeaturesGridComponentFeatureHeroComponentFeaturesSideBySideComponentCalloutV2ComponentFormComponentHeroComponentPricingTableComponentPricingComponentTestimonialSliderComponentTestimonialsGridComponent[] | null)
    __typename: 'PagesItem'
}

export type PagesItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'metadataOverrides__ASC' | 'metadataOverrides__DESC' | 'pathname__ASC' | 'pathname__DESC' | 'sections__ASC' | 'sections__DESC'

export interface PlanComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    plan: PricingPlanComponent
    __typename: 'PlanComponent'
}

export type PlanComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'plan__ASC' | 'plan__DESC'

export interface Plans {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PlanComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PlanComponent[]
    __typename: 'Plans'
}

export interface Posts {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (BlogPostComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: BlogPostComponent[]
    __typename: 'Posts'
}

export interface Posts_1 {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ChangelogPostComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ChangelogPostComponent[]
    __typename: 'Posts_1'
}

export interface PricingComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    heading: HeadingComponent
    plans: Plans
    __typename: 'PricingComponent'
}

export type PricingComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'heading__ASC' | 'heading__DESC' | 'plans__ASC' | 'plans__DESC'

export interface PricingPlanComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    billed: (Scalars['String'] | null)
    isMostPopular: Scalars['Boolean']
    list: List
    price: (Scalars['String'] | null)
    __typename: 'PricingPlanComponent'
}

export type PricingPlanComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'billed__ASC' | 'billed__DESC' | 'isMostPopular__ASC' | 'isMostPopular__DESC' | 'list__ASC' | 'list__DESC' | 'price__ASC' | 'price__DESC'

export interface PricingPlans {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PricingPlanComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PricingPlanComponent[]
    __typename: 'PricingPlans'
}

export interface PricingTableComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    categories: Categories
    heading: HeadingComponent
    __typename: 'PricingTableComponent'
}

export type PricingTableComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'categories__ASC' | 'categories__DESC' | 'heading__ASC' | 'heading__DESC'

export interface Query {
    /** Query across all of the instances of a component. Pass in filters and sorts if you want, and get each instance via the `items` key. */
    _componentInstances: _components
    /** The structure of the repository. Used by START. */
    _structure: Scalars['JSON']
    _sys: RepoSys
    collections: Collections
    components: Components
    sections: Sections
    site: Site
    __typename: 'Query'
}

export interface Questions {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FaqItemComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FaqItemComponent[]
    __typename: 'Questions'
}

export interface QuoteComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    author: AuthorComponent
    quote: Scalars['String']
    __typename: 'QuoteComponent'
}

export type QuoteComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'author__ASC' | 'author__DESC' | 'quote__ASC' | 'quote__DESC'

export interface Quotes {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (QuoteComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: QuoteComponent[]
    __typename: 'Quotes'
}

export interface RepoSys {
    dashboardUrl: Scalars['String']
    forkUrl: Scalars['String']
    hash: Scalars['String']
    id: Scalars['ID']
    playgroundInfo: (_PlaygroundInfo | null)
    slug: Scalars['String']
    title: Scalars['String']
    __typename: 'RepoSys'
}

export interface RichTextCalloutComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    content: (Content | null)
    size: (Scalars['BSHBSelect_1960434833'] | null)
    type: Scalars['BSHBSelect__108045351']
    __typename: 'RichTextCalloutComponent'
}

export type RichTextCalloutComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'content__ASC' | 'content__DESC' | 'size__ASC' | 'size__DESC' | 'type__ASC' | 'type__DESC'

export type RichTextJson = (BaseRichTextJson | BodyRichText | Body_1RichText | Body_2RichText | ContentRichText | SubtitleRichText | Subtitle_1RichText) & { __isUnion?: true }

export interface RightCtas {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ButtonComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ButtonComponent[]
    __typename: 'RightCtas'
}

export interface Sections {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    __typename: 'Sections'
}

export interface Settings {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    logo: DarkLightImageComponent
    logoLite: BlockImage
    metadata: Metadata
    showUseTemplate: Scalars['Boolean']
    theme: Theme
    __typename: 'Settings'
}

export interface Site {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    blog: Blog
    changelog: Changelog
    footer: Footer
    generalEvents: GeneralEvents
    header: Header
    pages: Pages
    settings: Settings
    __typename: 'Site'
}

export interface SocialLinkComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    icon: (BlockImage | null)
    url: Scalars['String']
    __typename: 'SocialLinkComponent'
}

export type SocialLinkComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'icon__ASC' | 'icon__DESC' | 'url__ASC' | 'url__DESC'

export interface SocialLinks {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (SocialLinkComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: SocialLinkComponent[]
    __typename: 'SocialLinks'
}

export interface Sublinks {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (SublinksItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: SublinksItem[]
    __typename: 'Sublinks'
}

export interface SublinksItem {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    link: UnionPageReferenceComponentCustomTextComponent
    __typename: 'SublinksItem'
}

export type SublinksItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'link__ASC' | 'link__DESC'

export interface Submissions {
    /** The `adminKey` gives clients the ability to query, delete and update this block's data. **It's not meant to be exposed to the public.** */
    adminKey: Scalars['bshb_event_514328939']
    /** The `ingestKey` gives clients the ability to send new events to this block. Generally, it's safe to expose it to the public. */
    ingestKey: Scalars['bshb_event_514328939']
    schema: Scalars['BSHBEventSchema']
    __typename: 'Submissions'
}

export interface Submissions_1 {
    /** The `adminKey` gives clients the ability to query, delete and update this block's data. **It's not meant to be exposed to the public.** */
    adminKey: Scalars['bshb_event_199695012']
    /** The `ingestKey` gives clients the ability to send new events to this block. Generally, it's safe to expose it to the public. */
    ingestKey: Scalars['bshb_event_199695012']
    schema: Scalars['BSHBEventSchema']
    __typename: 'Submissions_1'
}

export interface Subtitle {
    html: Scalars['String']
    json: SubtitleRichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Subtitle'
}

export interface SubtitleRichText {
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'SubtitleRichText'
}

export interface Subtitle_1 {
    html: Scalars['String']
    json: Subtitle_1RichText
    markdown: Scalars['String']
    plainText: Scalars['String']
    readingTime: Scalars['Int']
    __typename: 'Subtitle_1'
}

export interface Subtitle_1RichText {
    content: Scalars['BSHBRichTextContentSchema']
    toc: Scalars['BSHBRichTextTOCSchema']
    __typename: 'Subtitle_1RichText'
}

export interface TestimonialSliderComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    heading: HeadingComponent
    quotes: QuoteComponent[]
    __typename: 'TestimonialSliderComponent'
}

export type TestimonialSliderComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'heading__ASC' | 'heading__DESC' | 'quotes__ASC' | 'quotes__DESC'

export interface TestimonialsGridComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    heading: HeadingComponent
    quotes: QuoteComponent[]
    __typename: 'TestimonialsGridComponent'
}

export type TestimonialsGridComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'heading__ASC' | 'heading__DESC' | 'quotes__ASC' | 'quotes__DESC'

export interface Theme {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    accent: Scalars['BSHBSelect_262811106']
    grayScale: Scalars['BSHBSelect__1721217313']
    __typename: 'Theme'
}

export interface TransactionStatus {
    /** Duration in milliseconds. */
    duration: (Scalars['Int'] | null)
    endedAt: (Scalars['String'] | null)
    id: Scalars['String']
    message: (Scalars['String'] | null)
    startedAt: Scalars['String']
    status: TransactionStatusEnum
    __typename: 'TransactionStatus'
}

export type TransactionStatusEnum = 'Cancelled' | 'Completed' | 'Failed' | 'Running' | 'Scheduled'

export type UnionBooleanComponentCustomTextComponent = (BooleanComponent | CustomTextComponent) & { __isUnion?: true }

export type UnionCodeSnippetComponent = (CodeSnippetComponent) & { __isUnion?: true }

export type UnionFaqItemComponentCodeSnippetComponentRichTextCalloutComponent = (CodeSnippetComponent | FaqItemComponent | RichTextCalloutComponent) & { __isUnion?: true }

export type UnionFreeformTextComponentFeaturesCardsComponentCalloutComponentCompaniesComponentFaqComponentFeaturesBigImageComponentFeaturesGridComponentFeatureHeroComponentFeaturesSideBySideComponentCalloutV2ComponentFormComponentHeroComponentPricingTableComponentPricingComponentTestimonialSliderComponentTestimonialsGridComponent = (CalloutComponent | CalloutV2Component | CompaniesComponent | FaqComponent | FeatureHeroComponent | FeaturesBigImageComponent | FeaturesCardsComponent | FeaturesGridComponent | FeaturesSideBySideComponent | FormComponent | FreeformTextComponent | HeroComponent | PricingComponent | PricingTableComponent | TestimonialSliderComponent | TestimonialsGridComponent) & { __isUnion?: true }

export type UnionPageReferenceComponentCustomTextComponent = (CustomTextComponent | PageReferenceComponent) & { __isUnion?: true }

export interface ValueComponent {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    plan: PricingPlanComponent
    value: (UnionBooleanComponentCustomTextComponent | null)
    __typename: 'ValueComponent'
}

export type ValueComponentOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'plan__ASC' | 'plan__DESC' | 'value__ASC' | 'value__DESC'

export interface Values {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ValueComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ValueComponent[]
    __typename: 'Values'
}

export interface Variant {
    apiName: Scalars['String']
    color: Scalars['String']
    id: Scalars['String']
    isDefault: Scalars['Boolean']
    label: Scalars['String']
    __typename: 'Variant'
}

export interface _PlaygroundInfo {
    claimUrl: (Scalars['String'] | null)
    editUrl: Scalars['String']
    expiresAt: (Scalars['String'] | null)
    id: (Scalars['String'] | null)
    __typename: '_PlaygroundInfo'
}

export type _ResolveTargetsWithEnum = 'id' | 'objectName'

export type _StructureFormatEnum = 'json' | 'xml'

export interface _components {
    author: authorComponent_AsList
    avatar: avatarComponent_AsList
    blogPost: blogPostComponent_AsList
    boolean: booleanComponent_AsList
    button: buttonComponent_AsList
    callout: calloutComponent_AsList
    calloutV2: calloutV2Component_AsList
    category: categoryComponent_AsList
    changelogPost: changelogPostComponent_AsList
    characteristicsItem: characteristicsItem_AsList
    codeSnippet: codeSnippetComponent_AsList
    companies: companiesComponent_AsList
    company: companyComponent_AsList
    customText: customTextComponent_AsList
    customerSatisfactionBanner: customerSatisfactionBannerComponent_AsList
    darkLightImage: darkLightImageComponent_AsList
    faq: faqComponent_AsList
    faqItem: faqItemComponent_AsList
    feature: featureComponent_AsList
    featureHero: featureHeroComponent_AsList
    featureWithIcon: featureWithIconComponent_AsList
    featuresBigImage: featuresBigImageComponent_AsList
    featuresCards: featuresCardsComponent_AsList
    featuresCardsListItem: featuresCardsListItem_AsList
    featuresGrid: featuresGridComponent_AsList
    featuresSideBySide: featuresSideBySideComponent_AsList
    featuresSideBySideListItem: featuresSideBySideListItem_AsList
    form: formComponent_AsList
    formWrapper: formWrapperComponent_AsList
    freeformText: freeformTextComponent_AsList
    headerNavbarLink: headerNavbarLinkComponent_AsList
    heading: headingComponent_AsList
    hero: heroComponent_AsList
    listItem: listItem_AsList
    metadataOverrides: metadataOverridesComponent_AsList
    navbarItem: navbarItem_AsList
    pageReference: pageReferenceComponent_AsList
    pagesItem: pagesItem_AsList
    plan: planComponent_AsList
    pricing: pricingComponent_AsList
    pricingPlan: pricingPlanComponent_AsList
    pricingTable: pricingTableComponent_AsList
    quote: quoteComponent_AsList
    richTextCallout: richTextCalloutComponent_AsList
    socialLink: socialLinkComponent_AsList
    sublinksItem: sublinksItem_AsList
    testimonialSlider: testimonialSliderComponent_AsList
    testimonialsGrid: testimonialsGridComponent_AsList
    value: valueComponent_AsList
    __typename: '_components'
}

export interface authorComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (AuthorComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: AuthorComponent[]
    __typename: 'authorComponent_AsList'
}

export interface avatarComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (AvatarComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: AvatarComponent[]
    __typename: 'avatarComponent_AsList'
}

export interface blogPostComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (BlogPostComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: BlogPostComponent[]
    __typename: 'blogPostComponent_AsList'
}

export interface booleanComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (BooleanComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: BooleanComponent[]
    __typename: 'booleanComponent_AsList'
}

export interface buttonComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ButtonComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ButtonComponent[]
    __typename: 'buttonComponent_AsList'
}

export interface calloutComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CalloutComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CalloutComponent[]
    __typename: 'calloutComponent_AsList'
}

export interface calloutV2Component_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CalloutV2Component | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CalloutV2Component[]
    __typename: 'calloutV2Component_AsList'
}

export interface categoryComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CategoryComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CategoryComponent[]
    __typename: 'categoryComponent_AsList'
}

export interface changelogPostComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ChangelogPostComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ChangelogPostComponent[]
    __typename: 'changelogPostComponent_AsList'
}

export interface characteristicsItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CharacteristicsItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CharacteristicsItem[]
    __typename: 'characteristicsItem_AsList'
}

export interface codeSnippetComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CodeSnippetComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CodeSnippetComponent[]
    __typename: 'codeSnippetComponent_AsList'
}

export interface companiesComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CompaniesComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CompaniesComponent[]
    __typename: 'companiesComponent_AsList'
}

export interface companyComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CompanyComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CompanyComponent[]
    __typename: 'companyComponent_AsList'
}

export interface customTextComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CustomTextComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CustomTextComponent[]
    __typename: 'customTextComponent_AsList'
}

export interface customerSatisfactionBannerComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (CustomerSatisfactionBannerComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: CustomerSatisfactionBannerComponent[]
    __typename: 'customerSatisfactionBannerComponent_AsList'
}

export interface darkLightImageComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (DarkLightImageComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: DarkLightImageComponent[]
    __typename: 'darkLightImageComponent_AsList'
}

export interface faqComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FaqComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FaqComponent[]
    __typename: 'faqComponent_AsList'
}

export interface faqItemComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FaqItemComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FaqItemComponent[]
    __typename: 'faqItemComponent_AsList'
}

export interface featureComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeatureComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeatureComponent[]
    __typename: 'featureComponent_AsList'
}

export interface featureHeroComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeatureHeroComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeatureHeroComponent[]
    __typename: 'featureHeroComponent_AsList'
}

export interface featureWithIconComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeatureWithIconComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeatureWithIconComponent[]
    __typename: 'featureWithIconComponent_AsList'
}

export interface featuresBigImageComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesBigImageComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesBigImageComponent[]
    __typename: 'featuresBigImageComponent_AsList'
}

export interface featuresCardsComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesCardsComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesCardsComponent[]
    __typename: 'featuresCardsComponent_AsList'
}

export interface featuresCardsListItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesCardsListItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesCardsListItem[]
    __typename: 'featuresCardsListItem_AsList'
}

export interface featuresGridComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesGridComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesGridComponent[]
    __typename: 'featuresGridComponent_AsList'
}

export interface featuresSideBySideComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesSideBySideComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesSideBySideComponent[]
    __typename: 'featuresSideBySideComponent_AsList'
}

export interface featuresSideBySideListItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FeaturesSideBySideListItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FeaturesSideBySideListItem[]
    __typename: 'featuresSideBySideListItem_AsList'
}

export interface formComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FormComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FormComponent[]
    __typename: 'formComponent_AsList'
}

export interface formWrapperComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FormWrapperComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FormWrapperComponent[]
    __typename: 'formWrapperComponent_AsList'
}

export interface freeformTextComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (FreeformTextComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: FreeformTextComponent[]
    __typename: 'freeformTextComponent_AsList'
}

export interface headerNavbarLinkComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (HeaderNavbarLinkComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: HeaderNavbarLinkComponent[]
    __typename: 'headerNavbarLinkComponent_AsList'
}

export interface headingComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (HeadingComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: HeadingComponent[]
    __typename: 'headingComponent_AsList'
}

export interface heroComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (HeroComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: HeroComponent[]
    __typename: 'heroComponent_AsList'
}

export interface listItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ListItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ListItem[]
    __typename: 'listItem_AsList'
}

export interface metadataOverridesComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (MetadataOverridesComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: MetadataOverridesComponent[]
    __typename: 'metadataOverridesComponent_AsList'
}

export interface navbarItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (NavbarItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: NavbarItem[]
    __typename: 'navbarItem_AsList'
}

export interface pageReferenceComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PageReferenceComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PageReferenceComponent[]
    __typename: 'pageReferenceComponent_AsList'
}

export interface pagesItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PagesItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PagesItem[]
    __typename: 'pagesItem_AsList'
}

export interface planComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PlanComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PlanComponent[]
    __typename: 'planComponent_AsList'
}

export interface pricingComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PricingComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PricingComponent[]
    __typename: 'pricingComponent_AsList'
}

export interface pricingPlanComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PricingPlanComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PricingPlanComponent[]
    __typename: 'pricingPlanComponent_AsList'
}

export interface pricingTableComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (PricingTableComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: PricingTableComponent[]
    __typename: 'pricingTableComponent_AsList'
}

export interface quoteComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (QuoteComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: QuoteComponent[]
    __typename: 'quoteComponent_AsList'
}

export interface richTextCalloutComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (RichTextCalloutComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: RichTextCalloutComponent[]
    __typename: 'richTextCalloutComponent_AsList'
}

export interface socialLinkComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (SocialLinkComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: SocialLinkComponent[]
    __typename: 'socialLinkComponent_AsList'
}

export interface sublinksItem_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (SublinksItem | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: SublinksItem[]
    __typename: 'sublinksItem_AsList'
}

export interface testimonialSliderComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (TestimonialSliderComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: TestimonialSliderComponent[]
    __typename: 'testimonialSliderComponent_AsList'
}

export interface testimonialsGridComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (TestimonialsGridComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: TestimonialsGridComponent[]
    __typename: 'testimonialsGridComponent_AsList'
}

export interface valueComponent_AsList {
    _analyticsKey: Scalars['String']
    _dashboardUrl: Scalars['String']
    _id: Scalars['String']
    _idPath: Scalars['String']
    _meta: ListMeta
    /** The key used to search from the frontend. */
    _searchKey: Scalars['String']
    _slug: Scalars['String']
    _slugPath: Scalars['String']
    _sys: BlockDocumentSys
    _title: Scalars['String']
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item: (ValueComponent | null)
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items: ValueComponent[]
    __typename: 'valueComponent_AsList'
}

export interface AuthorComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    company?: CompanyComponentGenqlSelection
    image?: BlockImageGenqlSelection
    role?: boolean | number
    x?: boolean | number
    __typename?: boolean | number
}

export interface AuthorComponentFilterInput {AND?: (AuthorComponentFilterInput | null),OR?: (AuthorComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),company?: (AuthorComponentFilterInput__company_0___company | null),role?: (StringFilter | null),x?: (StringFilter | null)}

export interface AuthorComponentFilterInput__company_0___company {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),url?: (StringFilter | null)}

export interface AuthorsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: AuthorComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: AuthorComponentGenqlSelection
    __typename?: boolean | number
}

export interface AvatarComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    avatar?: BlockImageGenqlSelection
    __typename?: boolean | number
}

export interface AvatarComponentFilterInput {AND?: (AvatarComponentFilterInput | null),OR?: (AvatarComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface AvatarsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: AvatarComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: AvatarComponentGenqlSelection
    __typename?: boolean | number
}

export interface BaseRichTextJsonGenqlSelection{
    blocks?: boolean | number
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
}

export interface BlockAudioGenqlSelection{
    /** The duration of the audio in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration?: boolean | number
    fileName?: boolean | number
    fileSize?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    __typename?: boolean | number
}

export interface BlockCodeSnippetGenqlSelection{
    allowedLanguages?: boolean | number
    code?: boolean | number
    /** @deprecated Figuring out the correct api. */
    html?: { __args: {
    /** Theme for the code snippet */
    theme?: (Scalars['String'] | null)} } | boolean | number
    language?: boolean | number
    __typename?: boolean | number
}

export interface BlockColorGenqlSelection{
    b?: boolean | number
    g?: boolean | number
    hex?: boolean | number
    hsl?: boolean | number
    r?: boolean | number
    rgb?: boolean | number
    __typename?: boolean | number
}

export interface BlockDocumentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    on_AuthorComponent?: AuthorComponentGenqlSelection
    on_Authors?: AuthorsGenqlSelection
    on_AvatarComponent?: AvatarComponentGenqlSelection
    on_Avatars?: AvatarsGenqlSelection
    on_Blog?: BlogGenqlSelection
    on_BlogPostComponent?: BlogPostComponentGenqlSelection
    on_BooleanComponent?: BooleanComponentGenqlSelection
    on_ButtonComponent?: ButtonComponentGenqlSelection
    on_CalloutComponent?: CalloutComponentGenqlSelection
    on_CalloutV2Component?: CalloutV2ComponentGenqlSelection
    on_Categories?: CategoriesGenqlSelection
    on_CategoryComponent?: CategoryComponentGenqlSelection
    on_Changelog?: ChangelogGenqlSelection
    on_ChangelogPostComponent?: ChangelogPostComponentGenqlSelection
    on_Characteristics?: CharacteristicsGenqlSelection
    on_CharacteristicsItem?: CharacteristicsItemGenqlSelection
    on_CodeSnippetComponent?: CodeSnippetComponentGenqlSelection
    on_Collections?: CollectionsGenqlSelection
    on_CompaniesComponent?: CompaniesComponentGenqlSelection
    on_CompanyComponent?: CompanyComponentGenqlSelection
    on_ComparisonOptions?: ComparisonOptionsGenqlSelection
    on_Components?: ComponentsGenqlSelection
    on_CustomTextComponent?: CustomTextComponentGenqlSelection
    on_CustomerSatisfactionBannerComponent?: CustomerSatisfactionBannerComponentGenqlSelection
    on_DarkLightImageComponent?: DarkLightImageComponentGenqlSelection
    on_FaqComponent?: FaqComponentGenqlSelection
    on_FaqItemComponent?: FaqItemComponentGenqlSelection
    on_FeatureComponent?: FeatureComponentGenqlSelection
    on_FeatureHeroComponent?: FeatureHeroComponentGenqlSelection
    on_FeatureWithIconComponent?: FeatureWithIconComponentGenqlSelection
    on_Features?: FeaturesGenqlSelection
    on_FeaturesBigImageComponent?: FeaturesBigImageComponentGenqlSelection
    on_FeaturesBigImageList?: FeaturesBigImageListGenqlSelection
    on_FeaturesCardsComponent?: FeaturesCardsComponentGenqlSelection
    on_FeaturesCardsList?: FeaturesCardsListGenqlSelection
    on_FeaturesCardsListItem?: FeaturesCardsListItemGenqlSelection
    on_FeaturesGridComponent?: FeaturesGridComponentGenqlSelection
    on_FeaturesGridList?: FeaturesGridListGenqlSelection
    on_FeaturesSideBySideComponent?: FeaturesSideBySideComponentGenqlSelection
    on_FeaturesSideBySideList?: FeaturesSideBySideListGenqlSelection
    on_FeaturesSideBySideListItem?: FeaturesSideBySideListItemGenqlSelection
    on_Footer?: FooterGenqlSelection
    on_FormComponent?: FormComponentGenqlSelection
    on_FormWrapperComponent?: FormWrapperComponentGenqlSelection
    on_FreeformTextComponent?: FreeformTextComponentGenqlSelection
    on_Header?: HeaderGenqlSelection
    on_HeaderNavbarLinkComponent?: HeaderNavbarLinkComponentGenqlSelection
    on_HeadingComponent?: HeadingComponentGenqlSelection
    on_HeroComponent?: HeroComponentGenqlSelection
    on_List?: ListGenqlSelection
    on_ListItem?: ListItemGenqlSelection
    on_Metadata?: MetadataGenqlSelection
    on_MetadataOverridesComponent?: MetadataOverridesComponentGenqlSelection
    on_MoreCompanies?: MoreCompaniesGenqlSelection
    on_Navbar?: NavbarGenqlSelection
    on_NavbarItem?: NavbarItemGenqlSelection
    on_Navbar_1?: Navbar_1GenqlSelection
    on_Newsletter?: NewsletterGenqlSelection
    on_PageReferenceComponent?: PageReferenceComponentGenqlSelection
    on_Pages?: PagesGenqlSelection
    on_PagesItem?: PagesItemGenqlSelection
    on_PlanComponent?: PlanComponentGenqlSelection
    on_Plans?: PlansGenqlSelection
    on_Posts?: PostsGenqlSelection
    on_Posts_1?: Posts_1GenqlSelection
    on_PricingComponent?: PricingComponentGenqlSelection
    on_PricingPlanComponent?: PricingPlanComponentGenqlSelection
    on_PricingPlans?: PricingPlansGenqlSelection
    on_PricingTableComponent?: PricingTableComponentGenqlSelection
    on_Questions?: QuestionsGenqlSelection
    on_QuoteComponent?: QuoteComponentGenqlSelection
    on_Quotes?: QuotesGenqlSelection
    on_RichTextCalloutComponent?: RichTextCalloutComponentGenqlSelection
    on_RightCtas?: RightCtasGenqlSelection
    on_Sections?: SectionsGenqlSelection
    on_Settings?: SettingsGenqlSelection
    on_Site?: SiteGenqlSelection
    on_SocialLinkComponent?: SocialLinkComponentGenqlSelection
    on_SocialLinks?: SocialLinksGenqlSelection
    on_Sublinks?: SublinksGenqlSelection
    on_SublinksItem?: SublinksItemGenqlSelection
    on_TestimonialSliderComponent?: TestimonialSliderComponentGenqlSelection
    on_TestimonialsGridComponent?: TestimonialsGridComponentGenqlSelection
    on_Theme?: ThemeGenqlSelection
    on_ValueComponent?: ValueComponentGenqlSelection
    on_Values?: ValuesGenqlSelection
    on_authorComponent_AsList?: authorComponent_AsListGenqlSelection
    on_avatarComponent_AsList?: avatarComponent_AsListGenqlSelection
    on_blogPostComponent_AsList?: blogPostComponent_AsListGenqlSelection
    on_booleanComponent_AsList?: booleanComponent_AsListGenqlSelection
    on_buttonComponent_AsList?: buttonComponent_AsListGenqlSelection
    on_calloutComponent_AsList?: calloutComponent_AsListGenqlSelection
    on_calloutV2Component_AsList?: calloutV2Component_AsListGenqlSelection
    on_categoryComponent_AsList?: categoryComponent_AsListGenqlSelection
    on_changelogPostComponent_AsList?: changelogPostComponent_AsListGenqlSelection
    on_characteristicsItem_AsList?: characteristicsItem_AsListGenqlSelection
    on_codeSnippetComponent_AsList?: codeSnippetComponent_AsListGenqlSelection
    on_companiesComponent_AsList?: companiesComponent_AsListGenqlSelection
    on_companyComponent_AsList?: companyComponent_AsListGenqlSelection
    on_customTextComponent_AsList?: customTextComponent_AsListGenqlSelection
    on_customerSatisfactionBannerComponent_AsList?: customerSatisfactionBannerComponent_AsListGenqlSelection
    on_darkLightImageComponent_AsList?: darkLightImageComponent_AsListGenqlSelection
    on_faqComponent_AsList?: faqComponent_AsListGenqlSelection
    on_faqItemComponent_AsList?: faqItemComponent_AsListGenqlSelection
    on_featureComponent_AsList?: featureComponent_AsListGenqlSelection
    on_featureHeroComponent_AsList?: featureHeroComponent_AsListGenqlSelection
    on_featureWithIconComponent_AsList?: featureWithIconComponent_AsListGenqlSelection
    on_featuresBigImageComponent_AsList?: featuresBigImageComponent_AsListGenqlSelection
    on_featuresCardsComponent_AsList?: featuresCardsComponent_AsListGenqlSelection
    on_featuresCardsListItem_AsList?: featuresCardsListItem_AsListGenqlSelection
    on_featuresGridComponent_AsList?: featuresGridComponent_AsListGenqlSelection
    on_featuresSideBySideComponent_AsList?: featuresSideBySideComponent_AsListGenqlSelection
    on_featuresSideBySideListItem_AsList?: featuresSideBySideListItem_AsListGenqlSelection
    on_formComponent_AsList?: formComponent_AsListGenqlSelection
    on_formWrapperComponent_AsList?: formWrapperComponent_AsListGenqlSelection
    on_freeformTextComponent_AsList?: freeformTextComponent_AsListGenqlSelection
    on_headerNavbarLinkComponent_AsList?: headerNavbarLinkComponent_AsListGenqlSelection
    on_headingComponent_AsList?: headingComponent_AsListGenqlSelection
    on_heroComponent_AsList?: heroComponent_AsListGenqlSelection
    on_listItem_AsList?: listItem_AsListGenqlSelection
    on_metadataOverridesComponent_AsList?: metadataOverridesComponent_AsListGenqlSelection
    on_navbarItem_AsList?: navbarItem_AsListGenqlSelection
    on_pageReferenceComponent_AsList?: pageReferenceComponent_AsListGenqlSelection
    on_pagesItem_AsList?: pagesItem_AsListGenqlSelection
    on_planComponent_AsList?: planComponent_AsListGenqlSelection
    on_pricingComponent_AsList?: pricingComponent_AsListGenqlSelection
    on_pricingPlanComponent_AsList?: pricingPlanComponent_AsListGenqlSelection
    on_pricingTableComponent_AsList?: pricingTableComponent_AsListGenqlSelection
    on_quoteComponent_AsList?: quoteComponent_AsListGenqlSelection
    on_richTextCalloutComponent_AsList?: richTextCalloutComponent_AsListGenqlSelection
    on_socialLinkComponent_AsList?: socialLinkComponent_AsListGenqlSelection
    on_sublinksItem_AsList?: sublinksItem_AsListGenqlSelection
    on_testimonialSliderComponent_AsList?: testimonialSliderComponent_AsListGenqlSelection
    on_testimonialsGridComponent_AsList?: testimonialsGridComponent_AsListGenqlSelection
    on_valueComponent_AsList?: valueComponent_AsListGenqlSelection
    __typename?: boolean | number
}

export interface BlockDocumentSysGenqlSelection{
    apiNamePath?: boolean | number
    createdAt?: boolean | number
    hash?: boolean | number
    id?: boolean | number
    idPath?: boolean | number
    lastModifiedAt?: boolean | number
    slug?: boolean | number
    slugPath?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface BlockFileGenqlSelection{
    fileName?: boolean | number
    fileSize?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    __typename?: boolean | number
}

export interface BlockImageGenqlSelection{
    alt?: boolean | number
    aspectRatio?: boolean | number
    blurDataURL?: boolean | number
    fileName?: boolean | number
    fileSize?: boolean | number
    height?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    /** @deprecated Renamed to `blurDataURL` to match Next.js Image's naming convention. */
    placeholderURL?: boolean | number
    /** @deprecated Use `url` instead. */
    rawUrl?: boolean | number
    thumbhash?: boolean | number
    /**
     * This field is used to generate the image URL with the provided options. The options are passed as arguments. For example, if you want to resize the image to 200x200 pixels, you can use the following query:
     * 
     * ```graphql
     * {
     *   imageBlock {
     *     url(width: 200, height: 200)
     *   }
     * }
     * ```
     * 
     * This will return the URL with the width and height set to 200 pixels.
     * 
     * BaseHub uses Cloudflare for image resizing. Check out [all available options in their docs](https://developers.cloudflare.com/images/transform-images/transform-via-workers/#fetch-options).
     * 
     */
    url?: { __args: {anim?: (Scalars['String'] | null), background?: (Scalars['String'] | null), blur?: (Scalars['Int'] | null), border?: (Scalars['String'] | null), brightness?: (Scalars['Int'] | null), compression?: (Scalars['String'] | null), contrast?: (Scalars['Int'] | null), dpr?: (Scalars['Int'] | null), fit?: (Scalars['String'] | null), format?: (Scalars['String'] | null), gamma?: (Scalars['String'] | null), gravity?: (Scalars['String'] | null), height?: (Scalars['Int'] | null), metadata?: (Scalars['String'] | null), quality?: (Scalars['Int'] | null), rotate?: (Scalars['String'] | null), sharpen?: (Scalars['String'] | null), trim?: (Scalars['String'] | null), width?: (Scalars['Int'] | null)} } | boolean | number
    width?: boolean | number
    __typename?: boolean | number
}

export interface BlockListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    on_Authors?: AuthorsGenqlSelection
    on_Avatars?: AvatarsGenqlSelection
    on_Categories?: CategoriesGenqlSelection
    on_Characteristics?: CharacteristicsGenqlSelection
    on_Features?: FeaturesGenqlSelection
    on_FeaturesBigImageList?: FeaturesBigImageListGenqlSelection
    on_FeaturesCardsList?: FeaturesCardsListGenqlSelection
    on_FeaturesGridList?: FeaturesGridListGenqlSelection
    on_FeaturesSideBySideList?: FeaturesSideBySideListGenqlSelection
    on_List?: ListGenqlSelection
    on_MoreCompanies?: MoreCompaniesGenqlSelection
    on_Navbar?: NavbarGenqlSelection
    on_Navbar_1?: Navbar_1GenqlSelection
    on_Pages?: PagesGenqlSelection
    on_Plans?: PlansGenqlSelection
    on_Posts?: PostsGenqlSelection
    on_Posts_1?: Posts_1GenqlSelection
    on_PricingPlans?: PricingPlansGenqlSelection
    on_Questions?: QuestionsGenqlSelection
    on_Quotes?: QuotesGenqlSelection
    on_RightCtas?: RightCtasGenqlSelection
    on_SocialLinks?: SocialLinksGenqlSelection
    on_Sublinks?: SublinksGenqlSelection
    on_Values?: ValuesGenqlSelection
    on_authorComponent_AsList?: authorComponent_AsListGenqlSelection
    on_avatarComponent_AsList?: avatarComponent_AsListGenqlSelection
    on_blogPostComponent_AsList?: blogPostComponent_AsListGenqlSelection
    on_booleanComponent_AsList?: booleanComponent_AsListGenqlSelection
    on_buttonComponent_AsList?: buttonComponent_AsListGenqlSelection
    on_calloutComponent_AsList?: calloutComponent_AsListGenqlSelection
    on_calloutV2Component_AsList?: calloutV2Component_AsListGenqlSelection
    on_categoryComponent_AsList?: categoryComponent_AsListGenqlSelection
    on_changelogPostComponent_AsList?: changelogPostComponent_AsListGenqlSelection
    on_characteristicsItem_AsList?: characteristicsItem_AsListGenqlSelection
    on_codeSnippetComponent_AsList?: codeSnippetComponent_AsListGenqlSelection
    on_companiesComponent_AsList?: companiesComponent_AsListGenqlSelection
    on_companyComponent_AsList?: companyComponent_AsListGenqlSelection
    on_customTextComponent_AsList?: customTextComponent_AsListGenqlSelection
    on_customerSatisfactionBannerComponent_AsList?: customerSatisfactionBannerComponent_AsListGenqlSelection
    on_darkLightImageComponent_AsList?: darkLightImageComponent_AsListGenqlSelection
    on_faqComponent_AsList?: faqComponent_AsListGenqlSelection
    on_faqItemComponent_AsList?: faqItemComponent_AsListGenqlSelection
    on_featureComponent_AsList?: featureComponent_AsListGenqlSelection
    on_featureHeroComponent_AsList?: featureHeroComponent_AsListGenqlSelection
    on_featureWithIconComponent_AsList?: featureWithIconComponent_AsListGenqlSelection
    on_featuresBigImageComponent_AsList?: featuresBigImageComponent_AsListGenqlSelection
    on_featuresCardsComponent_AsList?: featuresCardsComponent_AsListGenqlSelection
    on_featuresCardsListItem_AsList?: featuresCardsListItem_AsListGenqlSelection
    on_featuresGridComponent_AsList?: featuresGridComponent_AsListGenqlSelection
    on_featuresSideBySideComponent_AsList?: featuresSideBySideComponent_AsListGenqlSelection
    on_featuresSideBySideListItem_AsList?: featuresSideBySideListItem_AsListGenqlSelection
    on_formComponent_AsList?: formComponent_AsListGenqlSelection
    on_formWrapperComponent_AsList?: formWrapperComponent_AsListGenqlSelection
    on_freeformTextComponent_AsList?: freeformTextComponent_AsListGenqlSelection
    on_headerNavbarLinkComponent_AsList?: headerNavbarLinkComponent_AsListGenqlSelection
    on_headingComponent_AsList?: headingComponent_AsListGenqlSelection
    on_heroComponent_AsList?: heroComponent_AsListGenqlSelection
    on_listItem_AsList?: listItem_AsListGenqlSelection
    on_metadataOverridesComponent_AsList?: metadataOverridesComponent_AsListGenqlSelection
    on_navbarItem_AsList?: navbarItem_AsListGenqlSelection
    on_pageReferenceComponent_AsList?: pageReferenceComponent_AsListGenqlSelection
    on_pagesItem_AsList?: pagesItem_AsListGenqlSelection
    on_planComponent_AsList?: planComponent_AsListGenqlSelection
    on_pricingComponent_AsList?: pricingComponent_AsListGenqlSelection
    on_pricingPlanComponent_AsList?: pricingPlanComponent_AsListGenqlSelection
    on_pricingTableComponent_AsList?: pricingTableComponent_AsListGenqlSelection
    on_quoteComponent_AsList?: quoteComponent_AsListGenqlSelection
    on_richTextCalloutComponent_AsList?: richTextCalloutComponent_AsListGenqlSelection
    on_socialLinkComponent_AsList?: socialLinkComponent_AsListGenqlSelection
    on_sublinksItem_AsList?: sublinksItem_AsListGenqlSelection
    on_testimonialSliderComponent_AsList?: testimonialSliderComponent_AsListGenqlSelection
    on_testimonialsGridComponent_AsList?: testimonialsGridComponent_AsListGenqlSelection
    on_valueComponent_AsList?: valueComponent_AsListGenqlSelection
    __typename?: boolean | number
}

export interface BlockOgImageGenqlSelection{
    height?: boolean | number
    url?: boolean | number
    width?: boolean | number
    __typename?: boolean | number
}


/** Rich text block */
export interface BlockRichTextGenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: RichTextJsonGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    on_Body?: BodyGenqlSelection
    on_Body_1?: Body_1GenqlSelection
    on_Body_2?: Body_2GenqlSelection
    on_Content?: ContentGenqlSelection
    on_Subtitle?: SubtitleGenqlSelection
    on_Subtitle_1?: Subtitle_1GenqlSelection
    __typename?: boolean | number
}

export interface BlockVideoGenqlSelection{
    aspectRatio?: boolean | number
    /** The duration of the video in seconds. If the duration is not available, it will be estimated based on the file size. */
    duration?: boolean | number
    fileName?: boolean | number
    fileSize?: boolean | number
    height?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    width?: boolean | number
    __typename?: boolean | number
}

export interface BlogGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    featuredPosts?: BlogPostComponentGenqlSelection
    listTitle?: boolean | number
    mainTitle?: boolean | number
    metadata?: MetadataOverridesComponentGenqlSelection
    posts?: (PostsGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (BlogPostComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (BlogPostComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface BlogPostComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    authors?: AuthorComponentGenqlSelection
    body?: Body_1GenqlSelection
    categories?: boolean | number
    description?: boolean | number
    image?: DarkLightImageComponentGenqlSelection
    ogImage?: BlockOgImageGenqlSelection
    /** ISO 8601 date string. */
    publishedAt?: boolean | number
    __typename?: boolean | number
}

export interface BlogPostComponentFilterInput {AND?: (BlogPostComponentFilterInput | null),OR?: (BlogPostComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),authors?: (BlogPostComponentFilterInput__authors_0___author | null),categories?: (SelectFilter | null),description?: (StringFilter | null),image?: (BlogPostComponentFilterInput__image | null),publishedAt?: (DateFilter | null)}

export interface BlogPostComponentFilterInput__authors_0___author {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),role?: (StringFilter | null),x?: (StringFilter | null)}

export interface BlogPostComponentFilterInput__image {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface BodyGenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: BodyRichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
}

export interface BodyRichTextGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
}

export interface Body_1GenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: Body_1RichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
}

export interface Body_1RichTextGenqlSelection{
    blocks?: UnionFaqItemComponentCodeSnippetComponentRichTextCalloutComponentGenqlSelection
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
}

export interface Body_2GenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: Body_2RichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
}

export interface Body_2RichTextGenqlSelection{
    blocks?: UnionCodeSnippetComponentGenqlSelection
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
}

export interface BooleanComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    boolean?: boolean | number
    __typename?: boolean | number
}

export interface BooleanComponentFilterInput {AND?: (BooleanComponentFilterInput | null),OR?: (BooleanComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),boolean?: (Scalars['Boolean'] | null)}

export interface ButtonComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    href?: boolean | number
    icon?: boolean | number
    label?: boolean | number
    type?: boolean | number
    __typename?: boolean | number
}

export interface ButtonComponentFilterInput {AND?: (ButtonComponentFilterInput | null),OR?: (ButtonComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface CalloutComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    actions?: ButtonComponentGenqlSelection
    subtitle?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface CalloutComponentFilterInput {AND?: (CalloutComponentFilterInput | null),OR?: (CalloutComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),actions?: (CalloutComponentFilterInput__actions_0___button | null),subtitle?: (StringFilter | null),title?: (StringFilter | null)}

export interface CalloutComponentFilterInput__actions_0___button {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface CalloutV2ComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    actions?: ButtonComponentGenqlSelection
    subtitle?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface CalloutV2ComponentFilterInput {AND?: (CalloutV2ComponentFilterInput | null),OR?: (CalloutV2ComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),actions?: (CalloutV2ComponentFilterInput__actions_0___button | null),subtitle?: (StringFilter | null),title?: (StringFilter | null)}

export interface CalloutV2ComponentFilterInput__actions_0___button {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface CategoriesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CategoryComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CategoryComponentGenqlSelection
    __typename?: boolean | number
}

export interface CategoryComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    features?: (FeaturesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeatureComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeatureComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    tooltip?: boolean | number
    __typename?: boolean | number
}

export interface CategoryComponentFilterInput {AND?: (CategoryComponentFilterInput | null),OR?: (CategoryComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),features?: (ListFilter | null),tooltip?: (StringFilter | null)}

export interface ChangelogGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    goBackText?: boolean | number
    metadata?: MetadataOverridesComponentGenqlSelection
    posts?: (Posts_1GenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ChangelogPostComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ChangelogPostComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    socialLinks?: SocialLinkComponentGenqlSelection
    socialLinksTitle?: boolean | number
    subtitle?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface ChangelogPostComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    authors?: AuthorComponentGenqlSelection
    body?: Body_2GenqlSelection
    excerpt?: boolean | number
    image?: BlockImageGenqlSelection
    ogImage?: BlockOgImageGenqlSelection
    /** ISO 8601 date string. */
    publishedAt?: boolean | number
    __typename?: boolean | number
}

export interface ChangelogPostComponentFilterInput {AND?: (ChangelogPostComponentFilterInput | null),OR?: (ChangelogPostComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),authors?: (ChangelogPostComponentFilterInput__authors_0___author | null),excerpt?: (StringFilter | null),publishedAt?: (DateFilter | null)}

export interface ChangelogPostComponentFilterInput__authors_0___author {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),role?: (StringFilter | null),x?: (StringFilter | null)}

export interface CharacteristicsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CharacteristicsItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CharacteristicsItemGenqlSelection
    __typename?: boolean | number
}

export interface CharacteristicsItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    __typename?: boolean | number
}

export interface CharacteristicsItemFilterInput {AND?: (CharacteristicsItemFilterInput | null),OR?: (CharacteristicsItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface CodeSnippetComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    code?: BlockCodeSnippetGenqlSelection
    __typename?: boolean | number
}

export interface CodeSnippetComponentFilterInput {AND?: (CodeSnippetComponentFilterInput | null),OR?: (CodeSnippetComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface CollectionsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    authors?: (AuthorsGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (AuthorComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (AuthorComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    moreCompanies?: (MoreCompaniesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CompanyComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CompanyComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    pricingPlans?: (PricingPlansGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PricingPlanComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PricingPlanComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    quotes?: (QuotesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (QuoteComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (QuoteComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    socialLinks?: (SocialLinksGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (SocialLinkComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (SocialLinkComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface CompaniesComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    companies?: CompanyComponentGenqlSelection
    subtitle?: boolean | number
    __typename?: boolean | number
}

export interface CompaniesComponentFilterInput {AND?: (CompaniesComponentFilterInput | null),OR?: (CompaniesComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),companies?: (CompaniesComponentFilterInput__companies_0___company | null),subtitle?: (StringFilter | null)}

export interface CompaniesComponentFilterInput__companies_0___company {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),url?: (StringFilter | null)}

export interface CompanyComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    image?: BlockImageGenqlSelection
    url?: boolean | number
    __typename?: boolean | number
}

export interface CompanyComponentFilterInput {AND?: (CompanyComponentFilterInput | null),OR?: (CompanyComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),url?: (StringFilter | null)}

export interface ComparisonOptionsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    __typename?: boolean | number
}

export interface ComponentsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    __typename?: boolean | number
}

export interface ContentGenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: ContentRichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
}

export interface ContentRichTextGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
}

export interface CustomTextComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    text?: boolean | number
    __typename?: boolean | number
}

export interface CustomTextComponentFilterInput {AND?: (CustomTextComponentFilterInput | null),OR?: (CustomTextComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),text?: (StringFilter | null)}

export interface CustomerSatisfactionBannerComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    avatars?: (AvatarsGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (AvatarComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (AvatarComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    text?: boolean | number
    __typename?: boolean | number
}

export interface CustomerSatisfactionBannerComponentFilterInput {AND?: (CustomerSatisfactionBannerComponentFilterInput | null),OR?: (CustomerSatisfactionBannerComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),avatars?: (ListFilter | null),text?: (StringFilter | null)}

export interface DarkLightImageComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    dark?: BlockImageGenqlSelection
    light?: BlockImageGenqlSelection
    __typename?: boolean | number
}

export interface DarkLightImageComponentFilterInput {AND?: (DarkLightImageComponentFilterInput | null),OR?: (DarkLightImageComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface DateFilter {eq?: (Scalars['DateTime'] | null),isAfter?: (Scalars['DateTime'] | null),isBefore?: (Scalars['DateTime'] | null),isNull?: (Scalars['Boolean'] | null),neq?: (Scalars['DateTime'] | null),onOrAfter?: (Scalars['DateTime'] | null),onOrBefore?: (Scalars['DateTime'] | null)}

export interface FaqComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    heading?: HeadingComponentGenqlSelection
    layout?: boolean | number
    questions?: (QuestionsGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FaqItemComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FaqItemComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface FaqComponentFilterInput {AND?: (FaqComponentFilterInput | null),OR?: (FaqComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (FaqComponentFilterInput__heading | null),layout?: (SelectFilter | null),questions?: (ListFilter | null)}

export interface FaqComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}


/** Item of Frequent Answered Question */
export interface FaqItemComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    answer?: boolean | number
    __typename?: boolean | number
}

export interface FaqItemComponentFilterInput {AND?: (FaqItemComponentFilterInput | null),OR?: (FaqItemComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),answer?: (StringFilter | null)}

export interface FeatureComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    tooltip?: boolean | number
    values?: (ValuesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ValueComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ValueComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface FeatureComponentFilterInput {AND?: (FeatureComponentFilterInput | null),OR?: (FeatureComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),tooltip?: (StringFilter | null),values?: (ListFilter | null)}

export interface FeatureHeroComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    actions?: ButtonComponentGenqlSelection
    heading?: HeadingComponentGenqlSelection
    heroLayout?: boolean | number
    image?: DarkLightImageComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeatureHeroComponentFilterInput {AND?: (FeatureHeroComponentFilterInput | null),OR?: (FeatureHeroComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),actions?: (FeatureHeroComponentFilterInput__actions_0___button | null),heading?: (FeatureHeroComponentFilterInput__heading | null),heroLayout?: (SelectFilter | null),image?: (FeatureHeroComponentFilterInput__image | null)}

export interface FeatureHeroComponentFilterInput__actions_0___button {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface FeatureHeroComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface FeatureHeroComponentFilterInput__image {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface FeatureWithIconComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    description?: boolean | number
    icon?: BlockImageGenqlSelection
    __typename?: boolean | number
}

export interface FeatureWithIconComponentFilterInput {AND?: (FeatureWithIconComponentFilterInput | null),OR?: (FeatureWithIconComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),description?: (StringFilter | null)}

export interface FeaturesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeatureComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeatureComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesBigImageComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    featuresBigImageList?: (FeaturesBigImageListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeatureWithIconComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeatureWithIconComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    heading?: HeadingComponentGenqlSelection
    image?: DarkLightImageComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesBigImageComponentFilterInput {AND?: (FeaturesBigImageComponentFilterInput | null),OR?: (FeaturesBigImageComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),featuresBigImageList?: (ListFilter | null),heading?: (FeaturesBigImageComponentFilterInput__heading | null),image?: (FeaturesBigImageComponentFilterInput__image | null)}

export interface FeaturesBigImageComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface FeaturesBigImageComponentFilterInput__image {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface FeaturesBigImageListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeatureWithIconComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeatureWithIconComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesCardsComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    featuresCardsList?: (FeaturesCardsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesCardsListItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesCardsListItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    heading?: HeadingComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesCardsComponentFilterInput {AND?: (FeaturesCardsComponentFilterInput | null),OR?: (FeaturesCardsComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),featuresCardsList?: (ListFilter | null),heading?: (FeaturesCardsComponentFilterInput__heading | null)}

export interface FeaturesCardsComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface FeaturesCardsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesCardsListItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesCardsListItemGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesCardsListItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    characteristics?: (CharacteristicsGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CharacteristicsItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CharacteristicsItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    description?: boolean | number
    image?: DarkLightImageComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesCardsListItemFilterInput {AND?: (FeaturesCardsListItemFilterInput | null),OR?: (FeaturesCardsListItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),characteristics?: (ListFilter | null),description?: (StringFilter | null),image?: (FeaturesCardsListItemFilterInput__image | null)}

export interface FeaturesCardsListItemFilterInput__image {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface FeaturesGridComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    actions?: ButtonComponentGenqlSelection
    featuresGridList?: (FeaturesGridListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeatureWithIconComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeatureWithIconComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    heading?: HeadingComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesGridComponentFilterInput {AND?: (FeaturesGridComponentFilterInput | null),OR?: (FeaturesGridComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),actions?: (FeaturesGridComponentFilterInput__actions_0___button | null),featuresGridList?: (ListFilter | null),heading?: (FeaturesGridComponentFilterInput__heading | null)}

export interface FeaturesGridComponentFilterInput__actions_0___button {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface FeaturesGridComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface FeaturesGridListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeatureWithIconComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeatureWithIconComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesSideBySideComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    actions?: ButtonComponentGenqlSelection
    featuresSideBySideList?: (FeaturesSideBySideListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesSideBySideListItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesSideBySideListItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    heading?: HeadingComponentGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesSideBySideComponentFilterInput {AND?: (FeaturesSideBySideComponentFilterInput | null),OR?: (FeaturesSideBySideComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),actions?: (FeaturesSideBySideComponentFilterInput__actions_0___button | null),featuresSideBySideList?: (ListFilter | null),heading?: (FeaturesSideBySideComponentFilterInput__heading | null)}

export interface FeaturesSideBySideComponentFilterInput__actions_0___button {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface FeaturesSideBySideComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface FeaturesSideBySideListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesSideBySideListItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesSideBySideListItemGenqlSelection
    __typename?: boolean | number
}

export interface FeaturesSideBySideListItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    icon?: BlockImageGenqlSelection
    subtitle?: boolean | number
    __typename?: boolean | number
}

export interface FeaturesSideBySideListItemFilterInput {AND?: (FeaturesSideBySideListItemFilterInput | null),OR?: (FeaturesSideBySideListItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),subtitle?: (StringFilter | null)}

export interface FooterGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    copyright?: boolean | number
    navbar?: (Navbar_1GenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (NavbarItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (NavbarItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    newsletter?: NewsletterGenqlSelection
    socialLinks?: SocialLinkComponentGenqlSelection
    __typename?: boolean | number
}

export interface FormComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    cta?: ButtonComponentGenqlSelection
    submissions?: SubmissionsGenqlSelection
    subtitle?: SubtitleGenqlSelection
    title?: boolean | number
    __typename?: boolean | number
}

export interface FormComponentFilterInput {AND?: (FormComponentFilterInput | null),OR?: (FormComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),cta?: (FormComponentFilterInput__cta | null),title?: (StringFilter | null)}

export interface FormComponentFilterInput__cta {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface FormWrapperComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    cta?: ButtonComponentGenqlSelection
    subtitle?: Subtitle_1GenqlSelection
    title?: boolean | number
    __typename?: boolean | number
}

export interface FormWrapperComponentFilterInput {AND?: (FormWrapperComponentFilterInput | null),OR?: (FormWrapperComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),cta?: (FormWrapperComponentFilterInput__cta | null),title?: (StringFilter | null)}

export interface FormWrapperComponentFilterInput__cta {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface FreeformTextComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    body?: BodyGenqlSelection
    __typename?: boolean | number
}

export interface FreeformTextComponentFilterInput {AND?: (FreeformTextComponentFilterInput | null),OR?: (FreeformTextComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface GeneralEventsGenqlSelection{
    /** The `adminKey` gives clients the ability to query, delete and update this block's data. **It's not meant to be exposed to the public.** */
    adminKey?: boolean | number
    /** The `ingestKey` gives clients the ability to send new events to this block. Generally, it's safe to expose it to the public. */
    ingestKey?: boolean | number
    schema?: boolean | number
    __typename?: boolean | number
}

export interface GetUploadSignedURLGenqlSelection{
    signedURL?: boolean | number
    uploadURL?: boolean | number
    __typename?: boolean | number
}

export interface HeaderGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    navbar?: (NavbarGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (HeaderNavbarLinkComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (HeaderNavbarLinkComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    rightCtas?: (RightCtasGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ButtonComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ButtonComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface HeaderNavbarLinkComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    href?: boolean | number
    sublinks?: (SublinksGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (SublinksItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (SublinksItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface HeaderNavbarLinkComponentFilterInput {AND?: (HeaderNavbarLinkComponentFilterInput | null),OR?: (HeaderNavbarLinkComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),sublinks?: (ListFilter | null)}

export interface HeadingComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    align?: boolean | number
    subtitle?: boolean | number
    tag?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface HeadingComponentFilterInput {AND?: (HeadingComponentFilterInput | null),OR?: (HeadingComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface HeroComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    actions?: ButtonComponentGenqlSelection
    customerSatisfactionBanner?: CustomerSatisfactionBannerComponentGenqlSelection
    subtitle?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface HeroComponentFilterInput {AND?: (HeroComponentFilterInput | null),OR?: (HeroComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),actions?: (HeroComponentFilterInput__actions_0___button | null),customerSatisfactionBanner?: (HeroComponentFilterInput__customerSatisfactionBanner | null),subtitle?: (StringFilter | null),title?: (StringFilter | null)}

export interface HeroComponentFilterInput__actions_0___button {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface HeroComponentFilterInput__customerSatisfactionBanner {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),avatars?: (ListFilter | null),text?: (StringFilter | null)}

export interface ListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ListItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ListItemGenqlSelection
    __typename?: boolean | number
}

export interface ListFilter {isEmpty?: (Scalars['Boolean'] | null),length?: (Scalars['Int'] | null)}

export interface ListItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    __typename?: boolean | number
}

export interface ListItemFilterInput {AND?: (ListItemFilterInput | null),OR?: (ListItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface ListMetaGenqlSelection{
    /** Number of items after applying filters but before pagination */
    filteredCount?: boolean | number
    /** Total number of items in collection before any filtering/pagination */
    totalCount?: boolean | number
    __typename?: boolean | number
}

export interface MediaBlockGenqlSelection{
    fileName?: boolean | number
    fileSize?: boolean | number
    lastModified?: boolean | number
    mimeType?: boolean | number
    url?: boolean | number
    on_BlockAudio?: BlockAudioGenqlSelection
    on_BlockFile?: BlockFileGenqlSelection
    on_BlockImage?: BlockImageGenqlSelection
    on_BlockVideo?: BlockVideoGenqlSelection
    __typename?: boolean | number
}

export interface MediaBlockUnionGenqlSelection{
    on_BlockAudio?:BlockAudioGenqlSelection,
    on_BlockFile?:BlockFileGenqlSelection,
    on_BlockImage?:BlockImageGenqlSelection,
    on_BlockVideo?:BlockVideoGenqlSelection,
    on_MediaBlock?: MediaBlockGenqlSelection,
    __typename?: boolean | number
}

export interface MetadataGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    defaultDescription?: boolean | number
    defaultTitle?: boolean | number
    favicon?: BlockImageGenqlSelection
    ogImage?: BlockOgImageGenqlSelection
    sitename?: boolean | number
    titleTemplate?: boolean | number
    xAccount?: SocialLinkComponentGenqlSelection
    __typename?: boolean | number
}

export interface MetadataOverridesComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    description?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface MetadataOverridesComponentFilterInput {AND?: (MetadataOverridesComponentFilterInput | null),OR?: (MetadataOverridesComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),description?: (StringFilter | null),title?: (StringFilter | null)}

export interface MoreCompaniesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CompanyComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CompanyComponentGenqlSelection
    __typename?: boolean | number
}

export interface MutationGenqlSelection{
    /**
     * Returns a signed url and an upload url so that you can upload files into your repository.
     * 
     * Example usage with JavaScript:
     * ```js
     * async function handleUpload(file: File) {
     *   const { getUploadSignedURL } = await basehub().mutation({
     *     getUploadSignedURL: {
     *       __args: { fileName: file.name },
     *       signedURL: true,
     *       uploadURL: true,
     *     }
     *   })
     * 
     *   const { signedURL, uploadURL } = getUploadSignedURL
     * 
     *   await fetch(signedURL, { method: 'PUT', body: file })
     * 
     *   // done! do something with the uploadURL now
     * }
     * ```
     * 
     */
    getUploadSignedURL?: (GetUploadSignedURLGenqlSelection & { __args: {
    /** SHA256 hash of the file. Used for reusing existing files. */
    fileHash?: (Scalars['String'] | null), 
    /** The file name */
    fileName: Scalars['String']} })
    /** Start a job that can be awaited and the result given directly. Under the hood, it runs `transactionAsync` and polls for the result until it is available. You can pass a `timeout` argument, the default being 30_000ms. */
    transaction?: (TransactionStatusGenqlSelection & { __args: {
    /** Auto make a commit in your Repo with the specified message. */
    autoCommit?: (Scalars['String'] | null), 
    /** Transaction data. */
    data: Scalars['String'], 
    /** Skip running workflows and event subscribers. Defaults to false. */
    skipWorkflows?: (Scalars['Boolean'] | null), 
    /** Timeout in milliseconds. */
    timeout?: (Scalars['Int'] | null)} })
    /** Start an asynchronous job to mutate BaseHub data. Returns a transaction ID which you can use to get the result of the job. */
    transactionAsync?: { __args: {
    /** Auto make a commit in your Repo with the specified message. */
    autoCommit?: (Scalars['String'] | null), 
    /** Transaction data. */
    data: Scalars['String'], 
    /** Skip running workflows and event subscribers. Defaults to false. */
    skipWorkflows?: (Scalars['Boolean'] | null)} }
    transactionStatus?: (TransactionStatusGenqlSelection & { __args: {
    /** Transaction ID */
    id: Scalars['String']} })
    __typename?: boolean | number
}

export interface NavbarGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: HeaderNavbarLinkComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: HeaderNavbarLinkComponentGenqlSelection
    __typename?: boolean | number
}

export interface NavbarItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    url?: boolean | number
    __typename?: boolean | number
}

export interface NavbarItemFilterInput {AND?: (NavbarItemFilterInput | null),OR?: (NavbarItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),url?: (StringFilter | null)}

export interface Navbar_1GenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: NavbarItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: NavbarItemGenqlSelection
    __typename?: boolean | number
}

export interface NewsletterGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    description?: boolean | number
    submissions?: Submissions_1GenqlSelection
    title?: boolean | number
    __typename?: boolean | number
}

export interface NumberFilter {eq?: (Scalars['Float'] | null),gt?: (Scalars['Float'] | null),gte?: (Scalars['Float'] | null),isNull?: (Scalars['Boolean'] | null),lt?: (Scalars['Float'] | null),lte?: (Scalars['Float'] | null),neq?: (Scalars['Float'] | null)}

export interface PageReferenceComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    page?: PagesItemGenqlSelection
    __typename?: boolean | number
}

export interface PageReferenceComponentFilterInput {AND?: (PageReferenceComponentFilterInput | null),OR?: (PageReferenceComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),page?: (PageReferenceComponentFilterInput__page_0___untitled | null)}

export interface PageReferenceComponentFilterInput__page_0___untitled {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),metadataOverrides?: (PageReferenceComponentFilterInput__page_0___untitled__metadataOverrides | null),pathname?: (StringFilter | null)}

export interface PageReferenceComponentFilterInput__page_0___untitled__metadataOverrides {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),description?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PagesItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PagesItemGenqlSelection
    __typename?: boolean | number
}

export interface PagesItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    metadataOverrides?: MetadataOverridesComponentGenqlSelection
    pathname?: boolean | number
    sections?: UnionFreeformTextComponentFeaturesCardsComponentCalloutComponentCompaniesComponentFaqComponentFeaturesBigImageComponentFeaturesGridComponentFeatureHeroComponentFeaturesSideBySideComponentCalloutV2ComponentFormComponentHeroComponentPricingTableComponentPricingComponentTestimonialSliderComponentTestimonialsGridComponentGenqlSelection
    __typename?: boolean | number
}

export interface PagesItemFilterInput {AND?: (PagesItemFilterInput | null),OR?: (PagesItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),metadataOverrides?: (PagesItemFilterInput__metadataOverrides | null),pathname?: (StringFilter | null),sections?: (PagesItemFilterInput__sections | null)}

export interface PagesItemFilterInput__metadataOverrides {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),description?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections {callout?: (PagesItemFilterInput__sections_11___callout | null),calloutV2?: (PagesItemFilterInput__sections_0___calloutV2 | null),companies?: (PagesItemFilterInput__sections_9___companies | null),faq?: (PagesItemFilterInput__sections_4___faq | null),featureHero?: (PagesItemFilterInput__sections_13___featureHero | null),featuresBigImage?: (PagesItemFilterInput__sections_7___featuresBigImage | null),featuresCards?: (PagesItemFilterInput__sections_8___featuresCards | null),featuresGrid?: (PagesItemFilterInput__sections_6___featuresGrid | null),featuresSideBySide?: (PagesItemFilterInput__sections_5___featuresSideBySide | null),form?: (PagesItemFilterInput__sections_15___form | null),freeformText?: (PagesItemFilterInput__sections_14___freeformText | null),hero?: (PagesItemFilterInput__sections_10___hero | null),pricing?: (PagesItemFilterInput__sections_3___pricing | null),pricingTable?: (PagesItemFilterInput__sections_12___pricingTable | null),testimonialSlider?: (PagesItemFilterInput__sections_1___testimonialSlider | null),testimonialsGrid?: (PagesItemFilterInput__sections_2___testimonialsGrid | null)}

export interface PagesItemFilterInput__sections_0___calloutV2 {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),subtitle?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_10___hero {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),customerSatisfactionBanner?: (PagesItemFilterInput__sections_10___hero__customerSatisfactionBanner | null),subtitle?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_10___hero__customerSatisfactionBanner {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),avatars?: (ListFilter | null),text?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_11___callout {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),subtitle?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_12___pricingTable {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),categories?: (ListFilter | null),heading?: (PagesItemFilterInput__sections_12___pricingTable__heading | null)}

export interface PagesItemFilterInput__sections_12___pricingTable__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_13___featureHero {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (PagesItemFilterInput__sections_13___featureHero__heading | null),heroLayout?: (SelectFilter | null),image?: (PagesItemFilterInput__sections_13___featureHero__image | null)}

export interface PagesItemFilterInput__sections_13___featureHero__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_13___featureHero__image {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_14___freeformText {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_15___form {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),cta?: (PagesItemFilterInput__sections_15___form__cta | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_15___form__cta {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),href?: (StringFilter | null),label?: (StringFilter | null),type?: (SelectFilter | null)}

export interface PagesItemFilterInput__sections_1___testimonialSlider {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (PagesItemFilterInput__sections_1___testimonialSlider__heading | null)}

export interface PagesItemFilterInput__sections_1___testimonialSlider__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_2___testimonialsGrid {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (PagesItemFilterInput__sections_2___testimonialsGrid__heading | null)}

export interface PagesItemFilterInput__sections_2___testimonialsGrid__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_3___pricing {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (PagesItemFilterInput__sections_3___pricing__heading | null),plans?: (ListFilter | null)}

export interface PagesItemFilterInput__sections_3___pricing__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_4___faq {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (PagesItemFilterInput__sections_4___faq__heading | null),layout?: (SelectFilter | null),questions?: (ListFilter | null)}

export interface PagesItemFilterInput__sections_4___faq__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_5___featuresSideBySide {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),featuresSideBySideList?: (ListFilter | null),heading?: (PagesItemFilterInput__sections_5___featuresSideBySide__heading | null)}

export interface PagesItemFilterInput__sections_5___featuresSideBySide__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_6___featuresGrid {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),featuresGridList?: (ListFilter | null),heading?: (PagesItemFilterInput__sections_6___featuresGrid__heading | null)}

export interface PagesItemFilterInput__sections_6___featuresGrid__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_7___featuresBigImage {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),featuresBigImageList?: (ListFilter | null),heading?: (PagesItemFilterInput__sections_7___featuresBigImage__heading | null),image?: (PagesItemFilterInput__sections_7___featuresBigImage__image | null)}

export interface PagesItemFilterInput__sections_7___featuresBigImage__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_7___featuresBigImage__image {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_8___featuresCards {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),featuresCardsList?: (ListFilter | null),heading?: (PagesItemFilterInput__sections_8___featuresCards__heading | null)}

export interface PagesItemFilterInput__sections_8___featuresCards__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PagesItemFilterInput__sections_9___companies {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),subtitle?: (StringFilter | null)}

export interface PlanComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    plan?: PricingPlanComponentGenqlSelection
    __typename?: boolean | number
}

export interface PlanComponentFilterInput {AND?: (PlanComponentFilterInput | null),OR?: (PlanComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),plan?: (PlanComponentFilterInput__plan_0___pricingPlan | null)}

export interface PlanComponentFilterInput__plan_0___pricingPlan {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),billed?: (StringFilter | null),isMostPopular?: (Scalars['Boolean'] | null),list?: (ListFilter | null),price?: (StringFilter | null)}

export interface PlansGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PlanComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PlanComponentGenqlSelection
    __typename?: boolean | number
}

export interface PostsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: BlogPostComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: BlogPostComponentGenqlSelection
    __typename?: boolean | number
}

export interface Posts_1GenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ChangelogPostComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ChangelogPostComponentGenqlSelection
    __typename?: boolean | number
}

export interface PricingComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    heading?: HeadingComponentGenqlSelection
    plans?: (PlansGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PlanComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PlanComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface PricingComponentFilterInput {AND?: (PricingComponentFilterInput | null),OR?: (PricingComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (PricingComponentFilterInput__heading | null),plans?: (ListFilter | null)}

export interface PricingComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface PricingPlanComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    billed?: boolean | number
    isMostPopular?: boolean | number
    list?: (ListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ListItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ListItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    price?: boolean | number
    __typename?: boolean | number
}

export interface PricingPlanComponentFilterInput {AND?: (PricingPlanComponentFilterInput | null),OR?: (PricingPlanComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),billed?: (StringFilter | null),isMostPopular?: (Scalars['Boolean'] | null),list?: (ListFilter | null),price?: (StringFilter | null)}

export interface PricingPlansGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PricingPlanComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PricingPlanComponentGenqlSelection
    __typename?: boolean | number
}

export interface PricingTableComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    categories?: (CategoriesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CategoryComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CategoryComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    heading?: HeadingComponentGenqlSelection
    __typename?: boolean | number
}

export interface PricingTableComponentFilterInput {AND?: (PricingTableComponentFilterInput | null),OR?: (PricingTableComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),categories?: (ListFilter | null),heading?: (PricingTableComponentFilterInput__heading | null)}

export interface PricingTableComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface QueryGenqlSelection{
    /** Query across all of the instances of a component. Pass in filters and sorts if you want, and get each instance via the `items` key. */
    _componentInstances?: _componentsGenqlSelection
    /** The structure of the repository. Used by START. */
    _structure?: { __args: {
    /** The format of the structure. */
    format?: (_StructureFormatEnum | null), 
    /** The format of the structure. */
    resolveTargetsWith?: (_ResolveTargetsWithEnum | null), 
    /** A target block to forcefully resolve in the schema. */
    targetBlock?: (TargetBlock | null), 
    /** Whether to include constraints in the structure. */
    withConstraints?: (Scalars['Boolean'] | null), 
    /** Whether to include IDs in the structure. */
    withIDs?: (Scalars['Boolean'] | null), 
    /** Whether to include type options in the structure. */
    withTypeOptions?: (Scalars['Boolean'] | null)} } | boolean | number
    _sys?: RepoSysGenqlSelection
    collections?: CollectionsGenqlSelection
    components?: ComponentsGenqlSelection
    sections?: SectionsGenqlSelection
    site?: SiteGenqlSelection
    __typename?: boolean | number
}

export interface QuestionsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FaqItemComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FaqItemComponentGenqlSelection
    __typename?: boolean | number
}

export interface QuoteComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    author?: AuthorComponentGenqlSelection
    quote?: boolean | number
    __typename?: boolean | number
}

export interface QuoteComponentFilterInput {AND?: (QuoteComponentFilterInput | null),OR?: (QuoteComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),author?: (QuoteComponentFilterInput__author_0___author | null),quote?: (StringFilter | null)}

export interface QuoteComponentFilterInput__author_0___author {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),role?: (StringFilter | null),x?: (StringFilter | null)}

export interface QuotesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: QuoteComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: QuoteComponentGenqlSelection
    __typename?: boolean | number
}

export interface RepoSysGenqlSelection{
    dashboardUrl?: boolean | number
    forkUrl?: boolean | number
    hash?: boolean | number
    id?: boolean | number
    playgroundInfo?: _PlaygroundInfoGenqlSelection
    slug?: boolean | number
    title?: boolean | number
    __typename?: boolean | number
}

export interface RichTextCalloutComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    content?: ContentGenqlSelection
    size?: boolean | number
    type?: boolean | number
    __typename?: boolean | number
}

export interface RichTextCalloutComponentFilterInput {AND?: (RichTextCalloutComponentFilterInput | null),OR?: (RichTextCalloutComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),size?: (SelectFilter | null),type?: (SelectFilter | null)}

export interface RichTextJsonGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    on_BaseRichTextJson?: BaseRichTextJsonGenqlSelection
    on_BodyRichText?: BodyRichTextGenqlSelection
    on_Body_1RichText?: Body_1RichTextGenqlSelection
    on_Body_2RichText?: Body_2RichTextGenqlSelection
    on_ContentRichText?: ContentRichTextGenqlSelection
    on_SubtitleRichText?: SubtitleRichTextGenqlSelection
    on_Subtitle_1RichText?: Subtitle_1RichTextGenqlSelection
    __typename?: boolean | number
}

export interface RightCtasGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ButtonComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ButtonComponentGenqlSelection
    __typename?: boolean | number
}

export interface SectionsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    __typename?: boolean | number
}

export interface SelectFilter {excludes?: (Scalars['String'] | null),excludesAll?: (Scalars['String'][] | null),includes?: (Scalars['String'] | null),includesAll?: (Scalars['String'][] | null),includesAny?: (Scalars['String'][] | null),isEmpty?: (Scalars['Boolean'] | null)}

export interface SettingsGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    logo?: DarkLightImageComponentGenqlSelection
    logoLite?: BlockImageGenqlSelection
    metadata?: MetadataGenqlSelection
    showUseTemplate?: boolean | number
    theme?: ThemeGenqlSelection
    __typename?: boolean | number
}

export interface SiteGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    blog?: BlogGenqlSelection
    changelog?: ChangelogGenqlSelection
    footer?: FooterGenqlSelection
    generalEvents?: GeneralEventsGenqlSelection
    header?: HeaderGenqlSelection
    pages?: (PagesGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PagesItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PagesItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    settings?: SettingsGenqlSelection
    __typename?: boolean | number
}

export interface SocialLinkComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    icon?: BlockImageGenqlSelection
    url?: boolean | number
    __typename?: boolean | number
}

export interface SocialLinkComponentFilterInput {AND?: (SocialLinkComponentFilterInput | null),OR?: (SocialLinkComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),url?: (StringFilter | null)}

export interface SocialLinksGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: SocialLinkComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: SocialLinkComponentGenqlSelection
    __typename?: boolean | number
}

export interface StringFilter {contains?: (Scalars['String'] | null),endsWith?: (Scalars['String'] | null),eq?: (Scalars['String'] | null),isNull?: (Scalars['Boolean'] | null),matches?: (StringMatchesFilter | null),notEq?: (Scalars['String'] | null),startsWith?: (Scalars['String'] | null)}

export interface StringMatchesFilter {caseSensitive?: (Scalars['Boolean'] | null),pattern: Scalars['String']}

export interface SublinksGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: SublinksItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: SublinksItemGenqlSelection
    __typename?: boolean | number
}

export interface SublinksItemGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    link?: UnionPageReferenceComponentCustomTextComponentGenqlSelection
    __typename?: boolean | number
}

export interface SublinksItemFilterInput {AND?: (SublinksItemFilterInput | null),OR?: (SublinksItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),link?: (SublinksItemFilterInput__link | null)}

export interface SublinksItemFilterInput__link {comparativeText?: (SublinksItemFilterInput__link_0___comparativeText | null),pageReference?: (SublinksItemFilterInput__link_1___pageReference | null)}

export interface SublinksItemFilterInput__link_0___comparativeText {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),text?: (StringFilter | null)}

export interface SublinksItemFilterInput__link_1___pageReference {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}

export interface SubmissionsGenqlSelection{
    /** The `adminKey` gives clients the ability to query, delete and update this block's data. **It's not meant to be exposed to the public.** */
    adminKey?: boolean | number
    /** The `ingestKey` gives clients the ability to send new events to this block. Generally, it's safe to expose it to the public. */
    ingestKey?: boolean | number
    schema?: boolean | number
    __typename?: boolean | number
}

export interface Submissions_1GenqlSelection{
    /** The `adminKey` gives clients the ability to query, delete and update this block's data. **It's not meant to be exposed to the public.** */
    adminKey?: boolean | number
    /** The `ingestKey` gives clients the ability to send new events to this block. Generally, it's safe to expose it to the public. */
    ingestKey?: boolean | number
    schema?: boolean | number
    __typename?: boolean | number
}

export interface SubtitleGenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: SubtitleRichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
}

export interface SubtitleRichTextGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
}

export interface Subtitle_1GenqlSelection{
    html?: { __args: {
    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */
    slugs?: (Scalars['Boolean'] | null), 
    /** Inserts a table of contents at the beginning of the HTML. */
    toc?: (Scalars['Boolean'] | null)} } | boolean | number
    json?: Subtitle_1RichTextGenqlSelection
    markdown?: boolean | number
    plainText?: boolean | number
    readingTime?: { __args: {
    /** Words per minute, defaults to average 183wpm */
    wpm?: (Scalars['Int'] | null)} } | boolean | number
    __typename?: boolean | number
}

export interface Subtitle_1RichTextGenqlSelection{
    content?: boolean | number
    toc?: boolean | number
    __typename?: boolean | number
}

export interface TargetBlock {focus?: (Scalars['Boolean'] | null),id: Scalars['String'],label: Scalars['String']}

export interface TestimonialSliderComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    heading?: HeadingComponentGenqlSelection
    quotes?: QuoteComponentGenqlSelection
    __typename?: boolean | number
}

export interface TestimonialSliderComponentFilterInput {AND?: (TestimonialSliderComponentFilterInput | null),OR?: (TestimonialSliderComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (TestimonialSliderComponentFilterInput__heading | null),quotes?: (TestimonialSliderComponentFilterInput__quotes_0___quote | null)}

export interface TestimonialSliderComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface TestimonialSliderComponentFilterInput__quotes_0___quote {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),quote?: (StringFilter | null)}

export interface TestimonialsGridComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    heading?: HeadingComponentGenqlSelection
    quotes?: QuoteComponentGenqlSelection
    __typename?: boolean | number
}

export interface TestimonialsGridComponentFilterInput {AND?: (TestimonialsGridComponentFilterInput | null),OR?: (TestimonialsGridComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),heading?: (TestimonialsGridComponentFilterInput__heading | null),quotes?: (TestimonialsGridComponentFilterInput__quotes_0___quote | null)}

export interface TestimonialsGridComponentFilterInput__heading {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),align?: (SelectFilter | null),subtitle?: (StringFilter | null),tag?: (StringFilter | null),title?: (StringFilter | null)}

export interface TestimonialsGridComponentFilterInput__quotes_0___quote {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),quote?: (StringFilter | null)}

export interface ThemeGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    accent?: boolean | number
    grayScale?: boolean | number
    __typename?: boolean | number
}

export interface TransactionStatusGenqlSelection{
    /** Duration in milliseconds. */
    duration?: boolean | number
    endedAt?: boolean | number
    id?: boolean | number
    message?: boolean | number
    startedAt?: boolean | number
    status?: boolean | number
    __typename?: boolean | number
}

export interface UnionBooleanComponentCustomTextComponentGenqlSelection{
    on_BooleanComponent?:BooleanComponentGenqlSelection,
    on_CustomTextComponent?:CustomTextComponentGenqlSelection,
    on_BlockDocument?: BlockDocumentGenqlSelection,
    __typename?: boolean | number
}

export interface UnionCodeSnippetComponentGenqlSelection{
    on_CodeSnippetComponent?:CodeSnippetComponentGenqlSelection,
    on_BlockDocument?: BlockDocumentGenqlSelection,
    __typename?: boolean | number
}

export interface UnionFaqItemComponentCodeSnippetComponentRichTextCalloutComponentGenqlSelection{
    on_CodeSnippetComponent?:CodeSnippetComponentGenqlSelection,
    on_FaqItemComponent?:FaqItemComponentGenqlSelection,
    on_RichTextCalloutComponent?:RichTextCalloutComponentGenqlSelection,
    on_BlockDocument?: BlockDocumentGenqlSelection,
    __typename?: boolean | number
}

export interface UnionFreeformTextComponentFeaturesCardsComponentCalloutComponentCompaniesComponentFaqComponentFeaturesBigImageComponentFeaturesGridComponentFeatureHeroComponentFeaturesSideBySideComponentCalloutV2ComponentFormComponentHeroComponentPricingTableComponentPricingComponentTestimonialSliderComponentTestimonialsGridComponentGenqlSelection{
    on_CalloutComponent?:CalloutComponentGenqlSelection,
    on_CalloutV2Component?:CalloutV2ComponentGenqlSelection,
    on_CompaniesComponent?:CompaniesComponentGenqlSelection,
    on_FaqComponent?:FaqComponentGenqlSelection,
    on_FeatureHeroComponent?:FeatureHeroComponentGenqlSelection,
    on_FeaturesBigImageComponent?:FeaturesBigImageComponentGenqlSelection,
    on_FeaturesCardsComponent?:FeaturesCardsComponentGenqlSelection,
    on_FeaturesGridComponent?:FeaturesGridComponentGenqlSelection,
    on_FeaturesSideBySideComponent?:FeaturesSideBySideComponentGenqlSelection,
    on_FormComponent?:FormComponentGenqlSelection,
    on_FreeformTextComponent?:FreeformTextComponentGenqlSelection,
    on_HeroComponent?:HeroComponentGenqlSelection,
    on_PricingComponent?:PricingComponentGenqlSelection,
    on_PricingTableComponent?:PricingTableComponentGenqlSelection,
    on_TestimonialSliderComponent?:TestimonialSliderComponentGenqlSelection,
    on_TestimonialsGridComponent?:TestimonialsGridComponentGenqlSelection,
    on_BlockDocument?: BlockDocumentGenqlSelection,
    __typename?: boolean | number
}

export interface UnionPageReferenceComponentCustomTextComponentGenqlSelection{
    on_CustomTextComponent?:CustomTextComponentGenqlSelection,
    on_PageReferenceComponent?:PageReferenceComponentGenqlSelection,
    on_BlockDocument?: BlockDocumentGenqlSelection,
    __typename?: boolean | number
}

export interface ValueComponentGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    plan?: PricingPlanComponentGenqlSelection
    value?: UnionBooleanComponentCustomTextComponentGenqlSelection
    __typename?: boolean | number
}

export interface ValueComponentFilterInput {AND?: (ValueComponentFilterInput | null),OR?: (ValueComponentFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),plan?: (ValueComponentFilterInput__plan_0___pricingPlan | null),value?: (ValueComponentFilterInput__value | null)}

export interface ValueComponentFilterInput__plan_0___pricingPlan {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),billed?: (StringFilter | null),isMostPopular?: (Scalars['Boolean'] | null),list?: (ListFilter | null),price?: (StringFilter | null)}

export interface ValueComponentFilterInput__value {boolean?: (ValueComponentFilterInput__value_0___boolean | null),comparativeText?: (ValueComponentFilterInput__value_1___comparativeText | null)}

export interface ValueComponentFilterInput__value_0___boolean {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),boolean?: (Scalars['Boolean'] | null)}

export interface ValueComponentFilterInput__value_1___comparativeText {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),text?: (StringFilter | null)}

export interface ValuesGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ValueComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ValueComponentGenqlSelection
    __typename?: boolean | number
}

export interface VariantGenqlSelection{
    apiName?: boolean | number
    color?: boolean | number
    id?: boolean | number
    isDefault?: boolean | number
    label?: boolean | number
    __typename?: boolean | number
}

export interface _PlaygroundInfoGenqlSelection{
    claimUrl?: boolean | number
    editUrl?: boolean | number
    expiresAt?: boolean | number
    id?: boolean | number
    __typename?: boolean | number
}

export interface _componentsGenqlSelection{
    author?: (authorComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (AuthorComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (AuthorComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    avatar?: (avatarComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (AvatarComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (AvatarComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    blogPost?: (blogPostComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (BlogPostComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (BlogPostComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    boolean?: (booleanComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (BooleanComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (BooleanComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    button?: (buttonComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ButtonComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ButtonComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    callout?: (calloutComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CalloutComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CalloutComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    calloutV2?: (calloutV2Component_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CalloutV2ComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CalloutV2ComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    category?: (categoryComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CategoryComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CategoryComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    changelogPost?: (changelogPostComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ChangelogPostComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ChangelogPostComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    characteristicsItem?: (characteristicsItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CharacteristicsItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CharacteristicsItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    codeSnippet?: (codeSnippetComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CodeSnippetComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CodeSnippetComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    companies?: (companiesComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CompaniesComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CompaniesComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    company?: (companyComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CompanyComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CompanyComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    customText?: (customTextComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CustomTextComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CustomTextComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    customerSatisfactionBanner?: (customerSatisfactionBannerComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (CustomerSatisfactionBannerComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (CustomerSatisfactionBannerComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    darkLightImage?: (darkLightImageComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (DarkLightImageComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (DarkLightImageComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    faq?: (faqComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FaqComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FaqComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    faqItem?: (faqItemComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FaqItemComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FaqItemComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    feature?: (featureComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeatureComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeatureComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featureHero?: (featureHeroComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeatureHeroComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeatureHeroComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featureWithIcon?: (featureWithIconComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeatureWithIconComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeatureWithIconComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featuresBigImage?: (featuresBigImageComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesBigImageComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesBigImageComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featuresCards?: (featuresCardsComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesCardsComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesCardsComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featuresCardsListItem?: (featuresCardsListItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesCardsListItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesCardsListItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featuresGrid?: (featuresGridComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesGridComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesGridComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featuresSideBySide?: (featuresSideBySideComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesSideBySideComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesSideBySideComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    featuresSideBySideListItem?: (featuresSideBySideListItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FeaturesSideBySideListItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FeaturesSideBySideListItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    form?: (formComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FormComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FormComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    formWrapper?: (formWrapperComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FormWrapperComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FormWrapperComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    freeformText?: (freeformTextComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (FreeformTextComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (FreeformTextComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    headerNavbarLink?: (headerNavbarLinkComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (HeaderNavbarLinkComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (HeaderNavbarLinkComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    heading?: (headingComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (HeadingComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (HeadingComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    hero?: (heroComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (HeroComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (HeroComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    listItem?: (listItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ListItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ListItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    metadataOverrides?: (metadataOverridesComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (MetadataOverridesComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (MetadataOverridesComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    navbarItem?: (navbarItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (NavbarItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (NavbarItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    pageReference?: (pageReferenceComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PageReferenceComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PageReferenceComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    pagesItem?: (pagesItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PagesItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PagesItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    plan?: (planComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PlanComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PlanComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    pricing?: (pricingComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PricingComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PricingComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    pricingPlan?: (pricingPlanComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PricingPlanComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PricingPlanComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    pricingTable?: (pricingTableComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (PricingTableComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (PricingTableComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    quote?: (quoteComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (QuoteComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (QuoteComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    richTextCallout?: (richTextCalloutComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (RichTextCalloutComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (RichTextCalloutComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    socialLink?: (socialLinkComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (SocialLinkComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (SocialLinkComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    sublinksItem?: (sublinksItem_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (SublinksItemFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (SublinksItemOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    testimonialSlider?: (testimonialSliderComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (TestimonialSliderComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (TestimonialSliderComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    testimonialsGrid?: (testimonialsGridComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (TestimonialsGridComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (TestimonialsGridComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    value?: (valueComponent_AsListGenqlSelection & { __args?: {
    /** Filter by a field. */
    filter?: (ValueComponentFilterInput | null), 
    /** Limit the number of items returned. Defaults to 500. */
    first?: (Scalars['Int'] | null), 
    /** Order by a field. */
    orderBy?: (ValueComponentOrderByEnum | null), 
    /** Skip the first n items. */
    skip?: (Scalars['Int'] | null)} })
    __typename?: boolean | number
}

export interface authorComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: AuthorComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: AuthorComponentGenqlSelection
    __typename?: boolean | number
}

export interface avatarComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: AvatarComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: AvatarComponentGenqlSelection
    __typename?: boolean | number
}

export interface blogPostComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: BlogPostComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: BlogPostComponentGenqlSelection
    __typename?: boolean | number
}

export interface booleanComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: BooleanComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: BooleanComponentGenqlSelection
    __typename?: boolean | number
}

export interface buttonComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ButtonComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ButtonComponentGenqlSelection
    __typename?: boolean | number
}

export interface calloutComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CalloutComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CalloutComponentGenqlSelection
    __typename?: boolean | number
}

export interface calloutV2Component_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CalloutV2ComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CalloutV2ComponentGenqlSelection
    __typename?: boolean | number
}

export interface categoryComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CategoryComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CategoryComponentGenqlSelection
    __typename?: boolean | number
}

export interface changelogPostComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ChangelogPostComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ChangelogPostComponentGenqlSelection
    __typename?: boolean | number
}

export interface characteristicsItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CharacteristicsItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CharacteristicsItemGenqlSelection
    __typename?: boolean | number
}

export interface codeSnippetComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CodeSnippetComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CodeSnippetComponentGenqlSelection
    __typename?: boolean | number
}

export interface companiesComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CompaniesComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CompaniesComponentGenqlSelection
    __typename?: boolean | number
}

export interface companyComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CompanyComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CompanyComponentGenqlSelection
    __typename?: boolean | number
}

export interface customTextComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CustomTextComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CustomTextComponentGenqlSelection
    __typename?: boolean | number
}

export interface customerSatisfactionBannerComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: CustomerSatisfactionBannerComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: CustomerSatisfactionBannerComponentGenqlSelection
    __typename?: boolean | number
}

export interface darkLightImageComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: DarkLightImageComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: DarkLightImageComponentGenqlSelection
    __typename?: boolean | number
}

export interface faqComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FaqComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FaqComponentGenqlSelection
    __typename?: boolean | number
}

export interface faqItemComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FaqItemComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FaqItemComponentGenqlSelection
    __typename?: boolean | number
}

export interface featureComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeatureComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeatureComponentGenqlSelection
    __typename?: boolean | number
}

export interface featureHeroComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeatureHeroComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeatureHeroComponentGenqlSelection
    __typename?: boolean | number
}

export interface featureWithIconComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeatureWithIconComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeatureWithIconComponentGenqlSelection
    __typename?: boolean | number
}

export interface featuresBigImageComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesBigImageComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesBigImageComponentGenqlSelection
    __typename?: boolean | number
}

export interface featuresCardsComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesCardsComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesCardsComponentGenqlSelection
    __typename?: boolean | number
}

export interface featuresCardsListItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesCardsListItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesCardsListItemGenqlSelection
    __typename?: boolean | number
}

export interface featuresGridComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesGridComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesGridComponentGenqlSelection
    __typename?: boolean | number
}

export interface featuresSideBySideComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesSideBySideComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesSideBySideComponentGenqlSelection
    __typename?: boolean | number
}

export interface featuresSideBySideListItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FeaturesSideBySideListItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FeaturesSideBySideListItemGenqlSelection
    __typename?: boolean | number
}

export interface formComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FormComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FormComponentGenqlSelection
    __typename?: boolean | number
}

export interface formWrapperComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FormWrapperComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FormWrapperComponentGenqlSelection
    __typename?: boolean | number
}

export interface freeformTextComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: FreeformTextComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: FreeformTextComponentGenqlSelection
    __typename?: boolean | number
}

export interface headerNavbarLinkComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: HeaderNavbarLinkComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: HeaderNavbarLinkComponentGenqlSelection
    __typename?: boolean | number
}

export interface headingComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: HeadingComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: HeadingComponentGenqlSelection
    __typename?: boolean | number
}

export interface heroComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: HeroComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: HeroComponentGenqlSelection
    __typename?: boolean | number
}

export interface listItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ListItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ListItemGenqlSelection
    __typename?: boolean | number
}

export interface metadataOverridesComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: MetadataOverridesComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: MetadataOverridesComponentGenqlSelection
    __typename?: boolean | number
}

export interface navbarItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: NavbarItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: NavbarItemGenqlSelection
    __typename?: boolean | number
}

export interface pageReferenceComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PageReferenceComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PageReferenceComponentGenqlSelection
    __typename?: boolean | number
}

export interface pagesItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PagesItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PagesItemGenqlSelection
    __typename?: boolean | number
}

export interface planComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PlanComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PlanComponentGenqlSelection
    __typename?: boolean | number
}

export interface pricingComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PricingComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PricingComponentGenqlSelection
    __typename?: boolean | number
}

export interface pricingPlanComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PricingPlanComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PricingPlanComponentGenqlSelection
    __typename?: boolean | number
}

export interface pricingTableComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: PricingTableComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: PricingTableComponentGenqlSelection
    __typename?: boolean | number
}

export interface quoteComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: QuoteComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: QuoteComponentGenqlSelection
    __typename?: boolean | number
}

export interface richTextCalloutComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: RichTextCalloutComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: RichTextCalloutComponentGenqlSelection
    __typename?: boolean | number
}

export interface socialLinkComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: SocialLinkComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: SocialLinkComponentGenqlSelection
    __typename?: boolean | number
}

export interface sublinksItem_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: SublinksItemGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: SublinksItemGenqlSelection
    __typename?: boolean | number
}

export interface testimonialSliderComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: TestimonialSliderComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: TestimonialSliderComponentGenqlSelection
    __typename?: boolean | number
}

export interface testimonialsGridComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: TestimonialsGridComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: TestimonialsGridComponentGenqlSelection
    __typename?: boolean | number
}

export interface valueComponent_AsListGenqlSelection{
    _analyticsKey?: { __args: {
    /**
     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.
     * 
     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.
     */
    scope?: (AnalyticsKeyScope | null)} } | boolean | number
    _dashboardUrl?: boolean | number
    _id?: boolean | number
    _idPath?: boolean | number
    _meta?: ListMetaGenqlSelection
    /** The key used to search from the frontend. */
    _searchKey?: boolean | number
    _slug?: boolean | number
    _slugPath?: boolean | number
    _sys?: BlockDocumentSysGenqlSelection
    _title?: boolean | number
    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */
    item?: ValueComponentGenqlSelection
    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */
    items?: ValueComponentGenqlSelection
    __typename?: boolean | number
}

export interface FragmentsMap {
  AuthorComponent: {
    root: AuthorComponent,
    selection: AuthorComponentGenqlSelection,
}
  Authors: {
    root: Authors,
    selection: AuthorsGenqlSelection,
}
  AvatarComponent: {
    root: AvatarComponent,
    selection: AvatarComponentGenqlSelection,
}
  Avatars: {
    root: Avatars,
    selection: AvatarsGenqlSelection,
}
  BaseRichTextJson: {
    root: BaseRichTextJson,
    selection: BaseRichTextJsonGenqlSelection,
}
  BlockAudio: {
    root: BlockAudio,
    selection: BlockAudioGenqlSelection,
}
  BlockCodeSnippet: {
    root: BlockCodeSnippet,
    selection: BlockCodeSnippetGenqlSelection,
}
  BlockColor: {
    root: BlockColor,
    selection: BlockColorGenqlSelection,
}
  BlockDocument: {
    root: BlockDocument,
    selection: BlockDocumentGenqlSelection,
}
  BlockDocumentSys: {
    root: BlockDocumentSys,
    selection: BlockDocumentSysGenqlSelection,
}
  BlockFile: {
    root: BlockFile,
    selection: BlockFileGenqlSelection,
}
  BlockImage: {
    root: BlockImage,
    selection: BlockImageGenqlSelection,
}
  BlockList: {
    root: BlockList,
    selection: BlockListGenqlSelection,
}
  BlockOgImage: {
    root: BlockOgImage,
    selection: BlockOgImageGenqlSelection,
}
  BlockRichText: {
    root: BlockRichText,
    selection: BlockRichTextGenqlSelection,
}
  BlockVideo: {
    root: BlockVideo,
    selection: BlockVideoGenqlSelection,
}
  Blog: {
    root: Blog,
    selection: BlogGenqlSelection,
}
  BlogPostComponent: {
    root: BlogPostComponent,
    selection: BlogPostComponentGenqlSelection,
}
  Body: {
    root: Body,
    selection: BodyGenqlSelection,
}
  BodyRichText: {
    root: BodyRichText,
    selection: BodyRichTextGenqlSelection,
}
  Body_1: {
    root: Body_1,
    selection: Body_1GenqlSelection,
}
  Body_1RichText: {
    root: Body_1RichText,
    selection: Body_1RichTextGenqlSelection,
}
  Body_2: {
    root: Body_2,
    selection: Body_2GenqlSelection,
}
  Body_2RichText: {
    root: Body_2RichText,
    selection: Body_2RichTextGenqlSelection,
}
  BooleanComponent: {
    root: BooleanComponent,
    selection: BooleanComponentGenqlSelection,
}
  ButtonComponent: {
    root: ButtonComponent,
    selection: ButtonComponentGenqlSelection,
}
  CalloutComponent: {
    root: CalloutComponent,
    selection: CalloutComponentGenqlSelection,
}
  CalloutV2Component: {
    root: CalloutV2Component,
    selection: CalloutV2ComponentGenqlSelection,
}
  Categories: {
    root: Categories,
    selection: CategoriesGenqlSelection,
}
  CategoryComponent: {
    root: CategoryComponent,
    selection: CategoryComponentGenqlSelection,
}
  Changelog: {
    root: Changelog,
    selection: ChangelogGenqlSelection,
}
  ChangelogPostComponent: {
    root: ChangelogPostComponent,
    selection: ChangelogPostComponentGenqlSelection,
}
  Characteristics: {
    root: Characteristics,
    selection: CharacteristicsGenqlSelection,
}
  CharacteristicsItem: {
    root: CharacteristicsItem,
    selection: CharacteristicsItemGenqlSelection,
}
  CodeSnippetComponent: {
    root: CodeSnippetComponent,
    selection: CodeSnippetComponentGenqlSelection,
}
  Collections: {
    root: Collections,
    selection: CollectionsGenqlSelection,
}
  CompaniesComponent: {
    root: CompaniesComponent,
    selection: CompaniesComponentGenqlSelection,
}
  CompanyComponent: {
    root: CompanyComponent,
    selection: CompanyComponentGenqlSelection,
}
  ComparisonOptions: {
    root: ComparisonOptions,
    selection: ComparisonOptionsGenqlSelection,
}
  Components: {
    root: Components,
    selection: ComponentsGenqlSelection,
}
  Content: {
    root: Content,
    selection: ContentGenqlSelection,
}
  ContentRichText: {
    root: ContentRichText,
    selection: ContentRichTextGenqlSelection,
}
  CustomTextComponent: {
    root: CustomTextComponent,
    selection: CustomTextComponentGenqlSelection,
}
  CustomerSatisfactionBannerComponent: {
    root: CustomerSatisfactionBannerComponent,
    selection: CustomerSatisfactionBannerComponentGenqlSelection,
}
  DarkLightImageComponent: {
    root: DarkLightImageComponent,
    selection: DarkLightImageComponentGenqlSelection,
}
  FaqComponent: {
    root: FaqComponent,
    selection: FaqComponentGenqlSelection,
}
  FaqItemComponent: {
    root: FaqItemComponent,
    selection: FaqItemComponentGenqlSelection,
}
  FeatureComponent: {
    root: FeatureComponent,
    selection: FeatureComponentGenqlSelection,
}
  FeatureHeroComponent: {
    root: FeatureHeroComponent,
    selection: FeatureHeroComponentGenqlSelection,
}
  FeatureWithIconComponent: {
    root: FeatureWithIconComponent,
    selection: FeatureWithIconComponentGenqlSelection,
}
  Features: {
    root: Features,
    selection: FeaturesGenqlSelection,
}
  FeaturesBigImageComponent: {
    root: FeaturesBigImageComponent,
    selection: FeaturesBigImageComponentGenqlSelection,
}
  FeaturesBigImageList: {
    root: FeaturesBigImageList,
    selection: FeaturesBigImageListGenqlSelection,
}
  FeaturesCardsComponent: {
    root: FeaturesCardsComponent,
    selection: FeaturesCardsComponentGenqlSelection,
}
  FeaturesCardsList: {
    root: FeaturesCardsList,
    selection: FeaturesCardsListGenqlSelection,
}
  FeaturesCardsListItem: {
    root: FeaturesCardsListItem,
    selection: FeaturesCardsListItemGenqlSelection,
}
  FeaturesGridComponent: {
    root: FeaturesGridComponent,
    selection: FeaturesGridComponentGenqlSelection,
}
  FeaturesGridList: {
    root: FeaturesGridList,
    selection: FeaturesGridListGenqlSelection,
}
  FeaturesSideBySideComponent: {
    root: FeaturesSideBySideComponent,
    selection: FeaturesSideBySideComponentGenqlSelection,
}
  FeaturesSideBySideList: {
    root: FeaturesSideBySideList,
    selection: FeaturesSideBySideListGenqlSelection,
}
  FeaturesSideBySideListItem: {
    root: FeaturesSideBySideListItem,
    selection: FeaturesSideBySideListItemGenqlSelection,
}
  Footer: {
    root: Footer,
    selection: FooterGenqlSelection,
}
  FormComponent: {
    root: FormComponent,
    selection: FormComponentGenqlSelection,
}
  FormWrapperComponent: {
    root: FormWrapperComponent,
    selection: FormWrapperComponentGenqlSelection,
}
  FreeformTextComponent: {
    root: FreeformTextComponent,
    selection: FreeformTextComponentGenqlSelection,
}
  GeneralEvents: {
    root: GeneralEvents,
    selection: GeneralEventsGenqlSelection,
}
  GetUploadSignedURL: {
    root: GetUploadSignedURL,
    selection: GetUploadSignedURLGenqlSelection,
}
  Header: {
    root: Header,
    selection: HeaderGenqlSelection,
}
  HeaderNavbarLinkComponent: {
    root: HeaderNavbarLinkComponent,
    selection: HeaderNavbarLinkComponentGenqlSelection,
}
  HeadingComponent: {
    root: HeadingComponent,
    selection: HeadingComponentGenqlSelection,
}
  HeroComponent: {
    root: HeroComponent,
    selection: HeroComponentGenqlSelection,
}
  List: {
    root: List,
    selection: ListGenqlSelection,
}
  ListItem: {
    root: ListItem,
    selection: ListItemGenqlSelection,
}
  ListMeta: {
    root: ListMeta,
    selection: ListMetaGenqlSelection,
}
  MediaBlock: {
    root: MediaBlock,
    selection: MediaBlockGenqlSelection,
}
  Metadata: {
    root: Metadata,
    selection: MetadataGenqlSelection,
}
  MetadataOverridesComponent: {
    root: MetadataOverridesComponent,
    selection: MetadataOverridesComponentGenqlSelection,
}
  MoreCompanies: {
    root: MoreCompanies,
    selection: MoreCompaniesGenqlSelection,
}
  Mutation: {
    root: Mutation,
    selection: MutationGenqlSelection,
}
  Navbar: {
    root: Navbar,
    selection: NavbarGenqlSelection,
}
  NavbarItem: {
    root: NavbarItem,
    selection: NavbarItemGenqlSelection,
}
  Navbar_1: {
    root: Navbar_1,
    selection: Navbar_1GenqlSelection,
}
  Newsletter: {
    root: Newsletter,
    selection: NewsletterGenqlSelection,
}
  PageReferenceComponent: {
    root: PageReferenceComponent,
    selection: PageReferenceComponentGenqlSelection,
}
  Pages: {
    root: Pages,
    selection: PagesGenqlSelection,
}
  PagesItem: {
    root: PagesItem,
    selection: PagesItemGenqlSelection,
}
  PlanComponent: {
    root: PlanComponent,
    selection: PlanComponentGenqlSelection,
}
  Plans: {
    root: Plans,
    selection: PlansGenqlSelection,
}
  Posts: {
    root: Posts,
    selection: PostsGenqlSelection,
}
  Posts_1: {
    root: Posts_1,
    selection: Posts_1GenqlSelection,
}
  PricingComponent: {
    root: PricingComponent,
    selection: PricingComponentGenqlSelection,
}
  PricingPlanComponent: {
    root: PricingPlanComponent,
    selection: PricingPlanComponentGenqlSelection,
}
  PricingPlans: {
    root: PricingPlans,
    selection: PricingPlansGenqlSelection,
}
  PricingTableComponent: {
    root: PricingTableComponent,
    selection: PricingTableComponentGenqlSelection,
}
  Query: {
    root: Query,
    selection: QueryGenqlSelection,
}
  Questions: {
    root: Questions,
    selection: QuestionsGenqlSelection,
}
  QuoteComponent: {
    root: QuoteComponent,
    selection: QuoteComponentGenqlSelection,
}
  Quotes: {
    root: Quotes,
    selection: QuotesGenqlSelection,
}
  RepoSys: {
    root: RepoSys,
    selection: RepoSysGenqlSelection,
}
  RichTextCalloutComponent: {
    root: RichTextCalloutComponent,
    selection: RichTextCalloutComponentGenqlSelection,
}
  RichTextJson: {
    root: RichTextJson,
    selection: RichTextJsonGenqlSelection,
}
  RightCtas: {
    root: RightCtas,
    selection: RightCtasGenqlSelection,
}
  Sections: {
    root: Sections,
    selection: SectionsGenqlSelection,
}
  Settings: {
    root: Settings,
    selection: SettingsGenqlSelection,
}
  Site: {
    root: Site,
    selection: SiteGenqlSelection,
}
  SocialLinkComponent: {
    root: SocialLinkComponent,
    selection: SocialLinkComponentGenqlSelection,
}
  SocialLinks: {
    root: SocialLinks,
    selection: SocialLinksGenqlSelection,
}
  Sublinks: {
    root: Sublinks,
    selection: SublinksGenqlSelection,
}
  SublinksItem: {
    root: SublinksItem,
    selection: SublinksItemGenqlSelection,
}
  Submissions: {
    root: Submissions,
    selection: SubmissionsGenqlSelection,
}
  Submissions_1: {
    root: Submissions_1,
    selection: Submissions_1GenqlSelection,
}
  Subtitle: {
    root: Subtitle,
    selection: SubtitleGenqlSelection,
}
  SubtitleRichText: {
    root: SubtitleRichText,
    selection: SubtitleRichTextGenqlSelection,
}
  Subtitle_1: {
    root: Subtitle_1,
    selection: Subtitle_1GenqlSelection,
}
  Subtitle_1RichText: {
    root: Subtitle_1RichText,
    selection: Subtitle_1RichTextGenqlSelection,
}
  TestimonialSliderComponent: {
    root: TestimonialSliderComponent,
    selection: TestimonialSliderComponentGenqlSelection,
}
  TestimonialsGridComponent: {
    root: TestimonialsGridComponent,
    selection: TestimonialsGridComponentGenqlSelection,
}
  Theme: {
    root: Theme,
    selection: ThemeGenqlSelection,
}
  TransactionStatus: {
    root: TransactionStatus,
    selection: TransactionStatusGenqlSelection,
}
  ValueComponent: {
    root: ValueComponent,
    selection: ValueComponentGenqlSelection,
}
  Values: {
    root: Values,
    selection: ValuesGenqlSelection,
}
  Variant: {
    root: Variant,
    selection: VariantGenqlSelection,
}
  _PlaygroundInfo: {
    root: _PlaygroundInfo,
    selection: _PlaygroundInfoGenqlSelection,
}
  _components: {
    root: _components,
    selection: _componentsGenqlSelection,
}
  authorComponent_AsList: {
    root: authorComponent_AsList,
    selection: authorComponent_AsListGenqlSelection,
}
  avatarComponent_AsList: {
    root: avatarComponent_AsList,
    selection: avatarComponent_AsListGenqlSelection,
}
  blogPostComponent_AsList: {
    root: blogPostComponent_AsList,
    selection: blogPostComponent_AsListGenqlSelection,
}
  booleanComponent_AsList: {
    root: booleanComponent_AsList,
    selection: booleanComponent_AsListGenqlSelection,
}
  buttonComponent_AsList: {
    root: buttonComponent_AsList,
    selection: buttonComponent_AsListGenqlSelection,
}
  calloutComponent_AsList: {
    root: calloutComponent_AsList,
    selection: calloutComponent_AsListGenqlSelection,
}
  calloutV2Component_AsList: {
    root: calloutV2Component_AsList,
    selection: calloutV2Component_AsListGenqlSelection,
}
  categoryComponent_AsList: {
    root: categoryComponent_AsList,
    selection: categoryComponent_AsListGenqlSelection,
}
  changelogPostComponent_AsList: {
    root: changelogPostComponent_AsList,
    selection: changelogPostComponent_AsListGenqlSelection,
}
  characteristicsItem_AsList: {
    root: characteristicsItem_AsList,
    selection: characteristicsItem_AsListGenqlSelection,
}
  codeSnippetComponent_AsList: {
    root: codeSnippetComponent_AsList,
    selection: codeSnippetComponent_AsListGenqlSelection,
}
  companiesComponent_AsList: {
    root: companiesComponent_AsList,
    selection: companiesComponent_AsListGenqlSelection,
}
  companyComponent_AsList: {
    root: companyComponent_AsList,
    selection: companyComponent_AsListGenqlSelection,
}
  customTextComponent_AsList: {
    root: customTextComponent_AsList,
    selection: customTextComponent_AsListGenqlSelection,
}
  customerSatisfactionBannerComponent_AsList: {
    root: customerSatisfactionBannerComponent_AsList,
    selection: customerSatisfactionBannerComponent_AsListGenqlSelection,
}
  darkLightImageComponent_AsList: {
    root: darkLightImageComponent_AsList,
    selection: darkLightImageComponent_AsListGenqlSelection,
}
  faqComponent_AsList: {
    root: faqComponent_AsList,
    selection: faqComponent_AsListGenqlSelection,
}
  faqItemComponent_AsList: {
    root: faqItemComponent_AsList,
    selection: faqItemComponent_AsListGenqlSelection,
}
  featureComponent_AsList: {
    root: featureComponent_AsList,
    selection: featureComponent_AsListGenqlSelection,
}
  featureHeroComponent_AsList: {
    root: featureHeroComponent_AsList,
    selection: featureHeroComponent_AsListGenqlSelection,
}
  featureWithIconComponent_AsList: {
    root: featureWithIconComponent_AsList,
    selection: featureWithIconComponent_AsListGenqlSelection,
}
  featuresBigImageComponent_AsList: {
    root: featuresBigImageComponent_AsList,
    selection: featuresBigImageComponent_AsListGenqlSelection,
}
  featuresCardsComponent_AsList: {
    root: featuresCardsComponent_AsList,
    selection: featuresCardsComponent_AsListGenqlSelection,
}
  featuresCardsListItem_AsList: {
    root: featuresCardsListItem_AsList,
    selection: featuresCardsListItem_AsListGenqlSelection,
}
  featuresGridComponent_AsList: {
    root: featuresGridComponent_AsList,
    selection: featuresGridComponent_AsListGenqlSelection,
}
  featuresSideBySideComponent_AsList: {
    root: featuresSideBySideComponent_AsList,
    selection: featuresSideBySideComponent_AsListGenqlSelection,
}
  featuresSideBySideListItem_AsList: {
    root: featuresSideBySideListItem_AsList,
    selection: featuresSideBySideListItem_AsListGenqlSelection,
}
  formComponent_AsList: {
    root: formComponent_AsList,
    selection: formComponent_AsListGenqlSelection,
}
  formWrapperComponent_AsList: {
    root: formWrapperComponent_AsList,
    selection: formWrapperComponent_AsListGenqlSelection,
}
  freeformTextComponent_AsList: {
    root: freeformTextComponent_AsList,
    selection: freeformTextComponent_AsListGenqlSelection,
}
  headerNavbarLinkComponent_AsList: {
    root: headerNavbarLinkComponent_AsList,
    selection: headerNavbarLinkComponent_AsListGenqlSelection,
}
  headingComponent_AsList: {
    root: headingComponent_AsList,
    selection: headingComponent_AsListGenqlSelection,
}
  heroComponent_AsList: {
    root: heroComponent_AsList,
    selection: heroComponent_AsListGenqlSelection,
}
  listItem_AsList: {
    root: listItem_AsList,
    selection: listItem_AsListGenqlSelection,
}
  metadataOverridesComponent_AsList: {
    root: metadataOverridesComponent_AsList,
    selection: metadataOverridesComponent_AsListGenqlSelection,
}
  navbarItem_AsList: {
    root: navbarItem_AsList,
    selection: navbarItem_AsListGenqlSelection,
}
  pageReferenceComponent_AsList: {
    root: pageReferenceComponent_AsList,
    selection: pageReferenceComponent_AsListGenqlSelection,
}
  pagesItem_AsList: {
    root: pagesItem_AsList,
    selection: pagesItem_AsListGenqlSelection,
}
  planComponent_AsList: {
    root: planComponent_AsList,
    selection: planComponent_AsListGenqlSelection,
}
  pricingComponent_AsList: {
    root: pricingComponent_AsList,
    selection: pricingComponent_AsListGenqlSelection,
}
  pricingPlanComponent_AsList: {
    root: pricingPlanComponent_AsList,
    selection: pricingPlanComponent_AsListGenqlSelection,
}
  pricingTableComponent_AsList: {
    root: pricingTableComponent_AsList,
    selection: pricingTableComponent_AsListGenqlSelection,
}
  quoteComponent_AsList: {
    root: quoteComponent_AsList,
    selection: quoteComponent_AsListGenqlSelection,
}
  richTextCalloutComponent_AsList: {
    root: richTextCalloutComponent_AsList,
    selection: richTextCalloutComponent_AsListGenqlSelection,
}
  socialLinkComponent_AsList: {
    root: socialLinkComponent_AsList,
    selection: socialLinkComponent_AsListGenqlSelection,
}
  sublinksItem_AsList: {
    root: sublinksItem_AsList,
    selection: sublinksItem_AsListGenqlSelection,
}
  testimonialSliderComponent_AsList: {
    root: testimonialSliderComponent_AsList,
    selection: testimonialSliderComponent_AsListGenqlSelection,
}
  testimonialsGridComponent_AsList: {
    root: testimonialsGridComponent_AsList,
    selection: testimonialsGridComponent_AsListGenqlSelection,
}
  valueComponent_AsList: {
    root: valueComponent_AsList,
    selection: valueComponent_AsListGenqlSelection,
}
}
