<context>
# Overview

# Project Context

**Project Status: Pre-MVP**

- Read this file: `.taskmaster/docs/app-design-document.md` - App design document
- Read this file: `.taskmaster/docs/tech-stack.md` - Tech stack, architecture
- DO NOT care about breaking changes. We didn't deploy yet.
- DO NOT care about unit testing, accessibility, visual testing (Storybook), and performance optimization unless asked.
- Care about security, zod validation, authorization, rate limiting, and other production-level concerns. In general, you can see how it's done in the other features.

# Overview

[Provide a high-level overview of your product here. Explain what problem it solves, who it's for, and why it's valuable.]

# Core Features

[List and describe the main features of your product. For each feature, include:

- What it does
- Why it's important
- How it works at a high level]

# User Experience

[Describe the user journey and experience. Include:

- User personas
- Key user flows
- UI/UX considerations]

</context>
<PRD>

# Technical Architecture

[Outline the technical implementation details:

- System components
- Data models
- APIs and integrations
- Infrastructure requirements]

# Development Roadmap

[Break down the development process into phases:

- MVP requirements
- Future enhancements
- Do not think about timelines whatsoever -- all that matters is scope and detailing exactly what needs to be build in each phase so it can later be cut up into tasks]

# Logical Dependency Chain

[Define the logical order of development:

- Which features need to be built first (foundation)
- Getting as quickly as possible to something usable/visible front end that works
- Properly pacing and scoping each feature so it is atomic but can also be built upon and improved as development approaches]

# Risks and Mitigations

[Identify potential risks and how they'll be addressed:

- Technical challenges
- Figuring out the MVP that we can build upon
- Resource constraints]

# Appendix

[Include any additional information:

- Research findings
- Technical specifications]

</PRD>
