{"master": {"tasks": [], "metadata": {"created": "2025-06-22T13:38:45.420Z", "updated": "2025-06-22T22:10:07.199Z", "description": "Tasks for master context"}}, "hero-upgrade": {"tasks": ["Replace existing hero with mvpblocks minimal-hero component", "Fetch hero copy from BaseHub single-entry", "Add animated background if desired"], "metadata": {"created": "2025-07-03T10:00:00.000Z", "description": "Refactor Hero Section with mvpblocks"}}, "lifecycle-sections": {"tasks": ["Extend BaseHub schema with Lifecycle collection", "Implement lifecycles-grid component using mvpblocks side-features", "Create /lifecycles route and dynamic pages"], "metadata": {"created": "2025-07-03T10:00:00.000Z", "description": "Add Lifecycles grid sections"}}, "pricing-section": {"tasks": ["Integrate mvpblocks designer-pricing component", "Bind pricing tiers to BaseHub collection"], "metadata": {"created": "2025-07-03T10:00:00.000Z", "description": "Swap pricing section"}}, "mvpblocks-ui-swap": {"tasks": ["Replace header with mvpblocks header-2 block", "Replace footer with footer-glow block", "Ensure dark/light mode consistency"], "metadata": {"created": "2025-07-03T10:00:00.000Z", "description": "Header/Footer swap"}}}