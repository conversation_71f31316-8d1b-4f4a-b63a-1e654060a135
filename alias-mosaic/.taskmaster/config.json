{"models": {"main": {"provider": "claude-code", "modelId": "sonnet", "maxTokens": 64000, "temperature": 0.2}, "research": {"provider": "claude-code", "modelId": "opus", "maxTokens": 32000, "temperature": 0.1}, "fallback": {"provider": "claude-code", "modelId": "sonnet", "maxTokens": 64000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Task Master", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "userId": "**********"}}