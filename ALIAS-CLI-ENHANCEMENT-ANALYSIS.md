# ALIAS CLI Enhancement Analysis
## Learning from ReNative's Architecture

Based on our analysis of ReNative's codebase, here are the key architectural patterns and improvements we should implement in our ALIAS-stack CLI.

## 🏗️ Current ALIAS CLI Architecture

### Strengths
- **Commander.js Integration**: Good use of Commander for CLI structure
- **Interactive Prompts**: Inquirer.js for user-friendly interactions
- **Project Scaffolding**: Comprehensive project creation workflow
- **Agent Management**: Registry-based agent system
- **Lifecycle Management**: MOSAIC lifecycle integration

### Areas for Improvement
- **Monolithic Structure**: Single file CLI vs. modular architecture
- **Limited Extensibility**: Hard to add new platforms/engines
- **No Plugin System**: Missing extensible plugin architecture
- **Basic Task System**: Simple command structure vs. sophisticated task orchestration
- **Configuration Management**: Basic JSON configs vs. schema-driven configuration

## 🚀 ReNative's Key Architectural Patterns

### 1. **Modular Engine System**
```typescript
// ReNative's engine architecture
interface RnvEngine {
  platforms: RnvEnginePlatforms;
  tasks: ReadonlyArray<RnvTask>;
  config: ConfigFileEngine;
  extendModules?: RnvModule[];
}

// Each platform has its own engine
engines: {
  "@rnv/engine-rn": "React Native",
  "@rnv/engine-rn-web": "React Native Web", 
  "@rnv/engine-rn-electron": "Electron",
  "@rnv/engine-rn-tvos": "tvOS"
}
```

### 2. **Sophisticated Task System**
```typescript
interface RnvTask {
  task: string;
  dependsOn?: string[];
  platforms?: Array<RnvPlatformKey>;
  description: string;
  fn?: RnvTaskFn;
  options?: ReadonlyArray<RnvTaskOption>;
  isGlobalScope?: boolean;
}
```

### 3. **Schema-Driven Configuration**
- JSON Schema validation for all config files
- Type-safe configuration with Zod/TypeScript
- Hierarchical configuration inheritance
- Platform-specific overrides

### 4. **Plugin Architecture**
```typescript
plugins: {
  "react": "source:rnv",
  "react-native": "source:rnv", 
  "next": "source:rnv"
}
```

### 5. **Context Management**
```typescript
interface RnvContext {
  program: RnvContextProgram;
  process: NodeJS.Process;
  runtime: RnvContextRuntime;
  buildConfig: RnvBuildConfig;
  paths: RnvContextPaths;
}
```

## 🎯 Recommended ALIAS CLI Enhancements

### 1. **Modular Engine Architecture**
```typescript
// ALIAS Engine System
interface AliasEngine {
  id: string;
  platforms: AliasPlatformMap;
  tasks: AliasTask[];
  lifecycles: MosaicLifecycle[];
  agents: AgentDefinition[];
}

// Example engines
engines: {
  "@alias/engine-web": "Next.js + React",
  "@alias/engine-mobile": "React Native + Expo", 
  "@alias/engine-api": "Hono + tRPC",
  "@alias/engine-ai": "AI Agents + MCP",
  "@alias/engine-mosaic": "Full MOSAIC Stack"
}
```

### 2. **Enhanced Task System**
```typescript
interface AliasTask {
  task: string;
  lifecycle?: MosaicLifecycleStage;
  dependsOn?: string[];
  agents?: string[];
  platforms?: AliasPlatform[];
  description: string;
  fn: AliasTaskFn;
  options?: AliasTaskOption[];
  mosaicIntegration?: boolean;
}

// Example tasks
tasks: [
  {
    task: "create:feature",
    lifecycle: "development", 
    agents: ["code_generator", "test_generator"],
    platforms: ["web", "mobile"],
    fn: createFeatureTask
  },
  {
    task: "lifecycle:advance",
    lifecycle: "meta",
    agents: ["flow_guardian"],
    fn: advanceLifecycleTask
  }
]
```

### 3. **Schema-Driven Configuration**
```typescript
// alias.config.ts
export default defineAliasConfig({
  engines: {
    "@alias/engine-mosaic": "^1.0.0"
  },
  platforms: {
    web: {
      engine: "@alias/engine-web",
      framework: "next",
      version: "15.3"
    },
    mobile: {
      engine: "@alias/engine-mobile", 
      framework: "expo",
      platforms: ["ios", "android"]
    }
  },
  mosaic: {
    lifecycle: "development",
    agents: ["flow_guardian", "code_reviewer"],
    quantum_commands: true
  },
  ai: {
    model: "claude-3-5-sonnet",
    context_window: "enhanced"
  }
});
```

### 4. **Plugin System**
```typescript
interface AliasPlugin {
  id: string;
  name: string;
  version: string;
  engines?: string[];
  platforms?: string[];
  tasks?: AliasTask[];
  agents?: AgentDefinition[];
  hooks?: PluginHooks;
}

// Plugin registry
plugins: {
  "@alias/plugin-home-assistant": "^1.0.0",
  "@alias/plugin-apple-ecosystem": "^1.0.0", 
  "@alias/plugin-ai-agents": "^1.0.0"
}
```

### 5. **Context Management**
```typescript
interface AliasContext {
  program: Command;
  process: NodeJS.Process;
  config: AliasConfig;
  runtime: {
    currentLifecycle: MosaicLifecycleStage;
    activeAgents: Agent[];
    supportedPlatforms: AliasPlatform[];
  };
  paths: {
    project: string;
    config: string;
    agents: string;
    builds: string;
  };
}
```

## 📁 Proposed Directory Structure

```
packages/
├── cli/                    # Main CLI package
│   ├── src/
│   │   ├── bin.ts         # CLI entry point
│   │   ├── index.ts       # Main CLI logic
│   │   ├── context/       # Context management
│   │   ├── commands/      # Command implementations
│   │   └── utils/         # CLI utilities
│   └── package.json
├── core/                   # Core ALIAS functionality
│   ├── src/
│   │   ├── engines/       # Engine system
│   │   ├── tasks/         # Task system
│   │   ├── config/        # Configuration management
│   │   ├── platforms/     # Platform abstractions
│   │   ├── agents/        # Agent management
│   │   └── lifecycles/    # MOSAIC lifecycle system
│   └── package.json
├── engine-web/            # Web platform engine
├── engine-mobile/         # Mobile platform engine  
├── engine-api/            # API platform engine
├── engine-ai/             # AI platform engine
├── engine-mosaic/         # Full MOSAIC engine
├── plugin-home-assistant/ # Home Assistant plugin
├── plugin-apple/          # Apple ecosystem plugin
└── template-starter/      # Starter templates
```

## 🔄 Migration Strategy

### Phase 1: Core Architecture (Week 1-2)
1. **Restructure CLI**: Split monolithic CLI into modular packages
2. **Implement Context System**: Create ALIAS context management
3. **Basic Engine System**: Implement engine registration and loading
4. **Schema System**: Add configuration schema validation

### Phase 2: Task System (Week 3-4)  
1. **Task Registry**: Implement sophisticated task system
2. **Task Dependencies**: Add task dependency resolution
3. **Lifecycle Integration**: Connect tasks to MOSAIC lifecycles
4. **Agent Integration**: Connect tasks to AI agents

### Phase 3: Platform Engines (Week 5-6)
1. **Web Engine**: Next.js + React platform engine
2. **Mobile Engine**: React Native + Expo platform engine
3. **API Engine**: Hono + tRPC platform engine
4. **AI Engine**: AI agents + MCP platform engine

### Phase 4: Plugin System (Week 7-8)
1. **Plugin Architecture**: Implement plugin loading system
2. **Core Plugins**: Home Assistant, Apple ecosystem plugins
3. **Plugin Registry**: Create plugin discovery and installation
4. **Template System**: Enhanced project templates

## 💻 Implementation Examples

### 1. **Enhanced CLI Entry Point**
```typescript
// packages/cli/src/bin.ts
#!/usr/bin/env node
import { run } from './';
run({ ALIAS_HOME_DIR: undefined });

// packages/cli/src/index.ts
import { program } from 'commander';
import {
  createAliasApi,
  createAliasContext,
  executeAliasCore,
  registerEngine,
  AliasTaskCoreOptionPresets
} from '@alias/core';
import EngineWeb from '@alias/engine-web';
import EngineMobile from '@alias/engine-mobile';
import EngineAI from '@alias/engine-ai';

export const run = async ({ ALIAS_HOME_DIR }: { ALIAS_HOME_DIR?: string }) => {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json')).toString());

  program.version(packageJson.version, '-v, --version', 'output current version');

  // Register core options
  AliasTaskCoreOptionPresets.withCore().forEach((param) => {
    program.option(generateStringFromTaskOption(param), param.description);
  });

  program.allowUnknownOption(true);
  program.arguments('[cmd] [option]').action((cmd, option) => {
    cmdValue = cmd;
    cmdOption = option;
  });

  program.parse(process.argv);

  // Create ALIAS context and API
  createAliasApi({ spinner: Spinner, prompt: Prompt, logger: Logger });
  createAliasContext({ program, process, cmd: cmdValue, subCmd: cmdOption, ALIAS_HOME_DIR });

  // Register engines
  await registerEngine(EngineWeb);
  await registerEngine(EngineMobile);
  await registerEngine(EngineAI);

  await executeAliasCore();
};
```

### 2. **Engine Implementation**
```typescript
// packages/engine-web/src/index.ts
import { createAliasEngine } from '@alias/core';
import { webTasks } from './tasks';
import { webPlatforms } from './platforms';

export default createAliasEngine({
  platforms: webPlatforms,
  config: {
    id: '@alias/engine-web',
    name: 'ALIAS Web Engine',
    version: '1.0.0',
    description: 'Next.js + React web applications with MOSAIC integration'
  },
  tasks: webTasks,
  extendModules: [
    { id: 'next', version: '15.3.0' },
    { id: 'react', version: '19.0.0' },
    { id: '@alias/mosaic-web', version: '1.0.0' }
  ]
});

// packages/engine-web/src/tasks/index.ts
export const webTasks = [
  {
    task: 'create:component',
    platforms: ['web'],
    lifecycle: 'development',
    agents: ['code_generator', 'test_generator'],
    description: 'Create a new React component with MOSAIC integration',
    options: [
      { name: 'name', type: 'string', required: true },
      { name: 'type', type: 'choice', choices: ['page', 'component', 'layout'] },
      { name: 'lifecycle', type: 'choice', choices: ['discovery', 'development', 'testing'] }
    ],
    fn: createComponentTask
  },
  {
    task: 'build:web',
    platforms: ['web'],
    dependsOn: ['lint', 'test'],
    description: 'Build web application for production',
    fn: buildWebTask
  }
];
```

### 3. **Configuration Schema**
```typescript
// packages/core/src/schema/config.ts
import { z } from 'zod';

export const AliasConfigSchema = z.object({
  projectName: z.string(),
  version: z.string().default('1.0.0'),

  engines: z.record(z.string()),

  platforms: z.record(z.object({
    engine: z.string(),
    framework: z.string().optional(),
    version: z.string().optional(),
    config: z.record(z.any()).optional()
  })),

  mosaic: z.object({
    lifecycle: z.enum(['discovery', 'qualification', 'architecture', 'development', 'testing', 'deployment']),
    agents: z.array(z.string()),
    quantum_commands: z.boolean().default(true),
    home_assistant: z.boolean().default(false)
  }),

  ai: z.object({
    model: z.enum(['claude-3-5-sonnet', 'claude-3-opus', 'gpt-4-turbo']),
    context_window: z.enum(['standard', 'enhanced']).default('enhanced'),
    auto_documentation: z.boolean().default(true)
  }),

  plugins: z.record(z.string()).optional(),

  paths: z.object({
    src: z.string().default('./src'),
    build: z.string().default('./dist'),
    agents: z.string().default('./agents'),
    docs: z.string().default('./docs')
  }).optional()
});

export type AliasConfig = z.infer<typeof AliasConfigSchema>;
```

### 4. **Task System with MOSAIC Integration**
```typescript
// packages/core/src/tasks/types.ts
export interface AliasTask<OKey = string> {
  task: string;
  lifecycle?: MosaicLifecycleStage;
  dependsOn?: string[];
  agents?: string[];
  platforms?: AliasPlatform[];
  description: string;
  fn: AliasTaskFn<OKey>;
  options?: AliasTaskOption<OKey>[];
  mosaicIntegration?: boolean;
  quantumCommand?: boolean;
}

export interface MosaicTaskContext {
  lifecycle: MosaicLifecycleStage;
  agents: Agent[];
  quantum: QuantumCommandInterface;
  homeAssistant?: HomeAssistantInterface;
}

// packages/core/src/tasks/creators.ts
export const createAliasTask = <OKey extends string>(
  opts: CreateAliasTaskOpt<OKey>
): AliasTask<OKey> => {
  return {
    ...opts,
    key: `${opts.task}${opts.platforms?.join('-') || ''}`,
    mosaicIntegration: true,
    quantumCommand: opts.quantumCommand ?? true
  };
};
```

## 🎯 Immediate Next Steps

1. **Clone ReNative patterns** for our CLI architecture
2. **Create modular package structure** following ReNative's approach
3. **Implement engine system** for different platform targets
4. **Add schema-driven configuration** with TypeScript types
5. **Build sophisticated task system** with dependencies and lifecycle integration

## 🚀 Implementation Priority

### Week 1: Foundation
- [ ] Restructure CLI into modular packages
- [ ] Implement context management system
- [ ] Create basic engine registration
- [ ] Add configuration schema validation

### Week 2: Core Systems
- [ ] Build sophisticated task system
- [ ] Add task dependency resolution
- [ ] Implement MOSAIC lifecycle integration
- [ ] Create agent task coordination

### Week 3: Platform Engines
- [ ] Web engine (Next.js + React)
- [ ] Mobile engine (React Native + Expo)
- [ ] API engine (Hono + tRPC)
- [ ] AI engine (Agents + MCP)

### Week 4: Advanced Features
- [ ] Plugin system architecture
- [ ] Template system enhancement
- [ ] Home Assistant integration
- [ ] Apple ecosystem support

This architecture will make ALIAS CLI as powerful and extensible as ReNative while maintaining our unique MOSAIC and AI-first approach.
