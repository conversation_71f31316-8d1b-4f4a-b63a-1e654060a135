# ALIAS CLI Implementation Starter
## ReNative-Inspired Architecture

This document provides the concrete implementation steps to transform our ALIAS CLI using ReNative's proven patterns.

## 🏗️ Project Structure Setup

### 1. **Create Monorepo Structure**
```bash
# Create new CLI structure
mkdir -p alias-stack-cli/{packages,templates,docs}
cd alias-stack-cli

# Initialize monorepo
cat > package.json << 'EOF'
{
  "name": "@alias/stack-cli-monorepo",
  "version": "1.0.0",
  "private": true,
  "workspaces": {
    "packages": ["packages/*", "templates/*"]
  },
  "scripts": {
    "bootstrap": "npm install && npm run build",
    "build": "lerna run build",
    "dev": "lerna run dev --parallel",
    "test": "lerna run test",
    "lint": "lerna run lint"
  },
  "devDependencies": {
    "lerna": "^8.0.0",
    "typescript": "^5.3.0"
  }
}
EOF

# Initialize lerna
npx lerna init
```

### 2. **Core Package Structure**
```bash
# Create core packages
mkdir -p packages/{cli,core,engine-web,engine-mobile,engine-ai,engine-mosaic}

# CLI package
mkdir -p packages/cli/src/{bin,commands,utils}
mkdir -p packages/core/src/{engines,tasks,config,platforms,agents,lifecycles,context,schema}
```

## 📦 Package Implementations

### 1. **CLI Package (packages/cli)**
```typescript
// packages/cli/package.json
{
  "name": "@alias/cli",
  "version": "1.0.0",
  "bin": {
    "alias": "./dist/bin.js"
  },
  "dependencies": {
    "@alias/core": "workspace:*",
    "commander": "^11.0.0",
    "chalk": "^5.0.0",
    "ora": "^7.0.0",
    "inquirer": "^9.0.0"
  }
}

// packages/cli/src/bin.ts
#!/usr/bin/env node
import { run } from './index.js';
run({ ALIAS_HOME_DIR: undefined });

// packages/cli/src/index.ts
import { program } from 'commander';
import { 
  createAliasApi, 
  createAliasContext,
  executeAliasCore,
  registerEngine,
  AliasTaskCoreOptionPresets,
  generateStringFromTaskOption
} from '@alias/core';

export const run = async ({ ALIAS_HOME_DIR }: { ALIAS_HOME_DIR?: string }) => {
  const packageJson = await import('../package.json');
  let cmdValue = '';
  let cmdOption = '';

  program.version(packageJson.version, '-v, --version', 'output current version');

  // Register core options from ReNative pattern
  AliasTaskCoreOptionPresets.withCore().forEach((param) => {
    program.option(generateStringFromTaskOption(param), param.description);
  });

  program.allowUnknownOption(true);
  program.helpOption(false);

  program.arguments('[cmd] [option]').action((cmd, option) => {
    cmdValue = cmd;
    cmdOption = option;
  });

  program.parse(process.argv);

  // Create ALIAS context and API (ReNative pattern)
  createAliasApi({ 
    spinner: (await import('ora')).default,
    prompt: (await import('inquirer')).default,
    logger: console 
  });
  
  createAliasContext({ 
    program, 
    process, 
    cmd: cmdValue, 
    subCmd: cmdOption, 
    ALIAS_HOME_DIR 
  });

  // Auto-register engines (ReNative pattern)
  await autoRegisterEngines();
  
  await executeAliasCore();
};

async function autoRegisterEngines() {
  try {
    const { default: EngineWeb } = await import('@alias/engine-web');
    const { default: EngineMobile } = await import('@alias/engine-mobile');
    const { default: EngineAI } = await import('@alias/engine-ai');
    
    await registerEngine(EngineWeb);
    await registerEngine(EngineMobile);
    await registerEngine(EngineAI);
  } catch (error) {
    // Engines not installed, continue with core functionality
  }
}
```

### 2. **Core Package (packages/core)**
```typescript
// packages/core/package.json
{
  "name": "@alias/core",
  "version": "1.0.0",
  "main": "./dist/index.js",
  "types": "./dist/index.d.ts",
  "dependencies": {
    "zod": "^3.22.0",
    "commander": "^11.0.0",
    "chalk": "^5.0.0"
  }
}

// packages/core/src/context/types.ts
export interface AliasContext {
  program: any; // Commander program
  process: NodeJS.Process;
  cmd: string;
  subCmd: string;
  runtime: AliasContextRuntime;
  buildConfig: AliasBuildConfig;
  paths: AliasContextPaths;
  ALIAS_HOME_DIR?: string;
}

export interface AliasContextRuntime {
  currentLifecycle: MosaicLifecycleStage;
  activeAgents: Agent[];
  supportedPlatforms: AliasPlatform[];
  supportedEngines: AliasEngine[];
}

// packages/core/src/context/provider.ts
let _context: AliasContext;

export const createAliasContext = (opts: Partial<AliasContext>) => {
  _context = {
    ...opts,
    runtime: {
      currentLifecycle: 'discovery',
      activeAgents: [],
      supportedPlatforms: [],
      supportedEngines: []
    },
    buildConfig: {},
    paths: {
      project: process.cwd(),
      config: '.alias',
      agents: 'agents',
      builds: 'dist'
    }
  } as AliasContext;
};

export const getContext = (): AliasContext => _context;

// packages/core/src/engines/types.ts
export interface AliasEngine<OKey extends string = string> {
  id: string;
  name: string;
  version: string;
  platforms: AliasPlatformMap;
  tasks: ReadonlyArray<AliasTask<OKey>>;
  config: EngineConfig;
  extendModules?: AliasModule[];
  rootPath?: string;
}

export interface CreateAliasEngineOpts<OKey extends string> {
  platforms: AliasPlatformMap;
  config: EngineConfig;
  tasks: ReadonlyArray<AliasTask<OKey>>;
  extendModules?: AliasModule[];
  rootPath?: string;
}

// packages/core/src/engines/creators.ts
export const createAliasEngine = <OKey extends string>(
  opts: CreateAliasEngineOpts<OKey>
): AliasEngine<OKey> => {
  return {
    id: opts.config.id,
    name: opts.config.name,
    version: opts.config.version,
    platforms: opts.platforms,
    tasks: opts.tasks,
    config: opts.config,
    extendModules: opts.extendModules,
    rootPath: opts.rootPath
  };
};

// packages/core/src/tasks/types.ts
export interface AliasTask<OKey = string> {
  task: string;
  lifecycle?: MosaicLifecycleStage;
  dependsOn?: string[];
  agents?: string[];
  platforms?: AliasPlatform[];
  description: string;
  fn: AliasTaskFn<OKey>;
  options?: AliasTaskOption<OKey>[];
  mosaicIntegration?: boolean;
  quantumCommand?: boolean;
  key: string;
}

export type AliasTaskFn<OKey = string> = (
  context: AliasContext,
  options: Record<string, any>
) => Promise<void>;

// packages/core/src/tasks/creators.ts
export const createAliasTask = <OKey extends string>(
  opts: CreateAliasTaskOpt<OKey>
): AliasTask<OKey> => {
  return {
    ...opts,
    key: `${opts.task}${opts.platforms?.join('-') || ''}`,
    mosaicIntegration: opts.mosaicIntegration ?? true,
    quantumCommand: opts.quantumCommand ?? true
  };
};
```

### 3. **Web Engine Package (packages/engine-web)**
```typescript
// packages/engine-web/package.json
{
  "name": "@alias/engine-web",
  "version": "1.0.0",
  "main": "./dist/index.js",
  "dependencies": {
    "@alias/core": "workspace:*"
  }
}

// packages/engine-web/src/index.ts
import { createAliasEngine } from '@alias/core';
import { webTasks } from './tasks/index.js';
import { webPlatforms } from './platforms/index.js';

export default createAliasEngine({
  platforms: webPlatforms,
  config: {
    id: '@alias/engine-web',
    name: 'ALIAS Web Engine',
    version: '1.0.0',
    description: 'Next.js + React web applications with MOSAIC integration'
  },
  tasks: webTasks,
  extendModules: [
    { id: 'next', version: '15.3.0' },
    { id: 'react', version: '19.0.0' },
    { id: '@alias/mosaic-web', version: '1.0.0' }
  ]
});

// packages/engine-web/src/tasks/index.ts
import { createAliasTask } from '@alias/core';

export const webTasks = [
  createAliasTask({
    task: 'create:component',
    platforms: ['web'],
    lifecycle: 'development',
    agents: ['code_generator', 'test_generator'],
    description: 'Create a new React component with MOSAIC integration',
    options: [
      { name: 'name', type: 'string', required: true },
      { name: 'type', type: 'choice', choices: ['page', 'component', 'layout'] }
    ],
    fn: async (context, options) => {
      // Implementation for creating React components
      console.log(`Creating ${options.type}: ${options.name}`);
    }
  }),
  
  createAliasTask({
    task: 'build:web',
    platforms: ['web'],
    dependsOn: ['lint', 'test'],
    description: 'Build web application for production',
    fn: async (context, options) => {
      // Implementation for building web app
      console.log('Building web application...');
    }
  })
];
```

## 🚀 Quick Start Implementation

### 1. **Initialize Project**
```bash
# Clone the structure
git clone <this-repo> alias-stack-cli
cd alias-stack-cli

# Install dependencies
npm run bootstrap

# Build packages
npm run build
```

### 2. **Test CLI**
```bash
# Link CLI globally
cd packages/cli && npm link

# Test commands
alias --version
alias create my-app --type web-application
alias agent browse
alias lifecycle status
```

### 3. **Add New Engine**
```bash
# Create new engine
mkdir packages/engine-api
cd packages/engine-api

# Follow engine template pattern
# Implement tasks, platforms, and configuration
```

This implementation provides a solid foundation following ReNative's proven architecture patterns while maintaining ALIAS's unique MOSAIC and AI-first approach.
