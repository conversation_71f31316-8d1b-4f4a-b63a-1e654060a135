#!/bin/bash

# Script to combine MOSAIC audio sections into a single file for Apple Music
# Also creates properly tagged audio files for import

echo "Creating MOSAIC Technical Explainer audio compilation..."

# Create output directory
mkdir -p combined

# Create file list in correct order
cat > file_list.txt << EOF
file 'tts_ALIAS_20250704_000421.mp3'
file 'tts_MOSAI_20250704_000106.mp3'
file 'tts_Solo__20250704_001643.mp3'
file 'tts_MOSAI_20250704_001703.mp3'
file 'tts_AUROR_20250704_001726.mp3'
file 'tts_Here'_20250704_001807.mp3'
file 'tts_Let's_20250704_001828.mp3'
file 'tts_Let's_20250704_001850.mp3'
file 'tts_QUANT_20250704_001909.mp3'
file 'tts_Every_20250704_001930.mp3'
file 'tts_Each__20250704_001951.mp3'
file 'tts_MOSAI_20250704_002011.mp3'
file 'tts_MOSAI_20250704_002034.mp3'
file 'tts_Zero-_20250704_002053.mp3'
file 'tts_MOSAI_20250704_002114.mp3'
file 'tts_MOSAI_20250704_002137.mp3'
EOF

# Combine all files into one
ffmpeg -f concat -safe 0 -i file_list.txt -c copy "combined/MOSAIC_Technical_Explainer_Full.mp3"

# Add metadata tags for Apple Music
ffmpeg -i "combined/MOSAIC_Technical_Explainer_Full.mp3" -c copy \
  -metadata title="MOSAIC Technical Explainer" \
  -metadata artist="ALIAS" \
  -metadata album="MOSAIC Documentation" \
  -metadata year="2025" \
  -metadata genre="Podcast" \
  -metadata comment="Technical explanation of MOSAIC - Meta-Orchestration System for AI-Integrated Collaboration" \
  -metadata track="1/1" \
  "combined/MOSAIC_Technical_Explainer_Tagged.mp3"

# Create individual chapter files with metadata (optional)
tracks=(
    "01-Opening:ALIAS Introduction"
    "02-System_Architecture:Technical Overview"
    "03-Core_Problem:Context Switching"
    "04-APEX_Lifecycle:Development Automation"
    "05-AURORA_SHIELD:Customer and Security"
    "06-Event_Orchestration:Lifecycle Communication"
    "07-Implementation_Example:Feature Flow"
    "08-Knowledge_Data:PRISM NEXUS FLUX"
    "09-Financial_Meta:QUANTUM ECHO PULSE"
    "10-Execution_Pipeline:11-Stage Process"
    "11-Agent_Architecture:ReAct Patterns"
    "12-Data_Architecture:Polyglot Persistence"
    "13-Performance_Scale:Metrics and Scaling"
    "14-Security:Zero-Trust Model"
    "15-Implementation:Infrastructure"
    "16-Summary:Closing Thoughts"
)

# Array of original filenames in order
files=(
    "tts_ALIAS_20250704_000421.mp3"
    "tts_MOSAI_20250704_000106.mp3"
    "tts_Solo__20250704_001643.mp3"
    "tts_MOSAI_20250704_001703.mp3"
    "tts_AUROR_20250704_001726.mp3"
    "tts_Here'_20250704_001807.mp3"
    "tts_Let's_20250704_001828.mp3"
    "tts_Let's_20250704_001850.mp3"
    "tts_QUANT_20250704_001909.mp3"
    "tts_Every_20250704_001930.mp3"
    "tts_Each__20250704_001951.mp3"
    "tts_MOSAI_20250704_002011.mp3"
    "tts_MOSAI_20250704_002034.mp3"
    "tts_Zero-_20250704_002053.mp3"
    "tts_MOSAI_20250704_002114.mp3"
    "tts_MOSAI_20250704_002137.mp3"
)

# Create tagged individual files
for i in "${!tracks[@]}"; do
    IFS=':' read -r filename title <<< "${tracks[$i]}"
    track_num=$((i + 1))
    
    ffmpeg -i "${files[$i]}" -c copy \
        -metadata title="$title" \
        -metadata artist="ALIAS" \
        -metadata album="MOSAIC Technical Explainer" \
        -metadata track="$track_num/16" \
        -metadata genre="Technology" \
        "combined/MOSAIC_${filename}.mp3"
done

# Clean up
rm file_list.txt

echo "Done! Files created in ./combined/"
echo ""
echo "To add to Apple Music:"
echo "1. Open Music app"
echo "2. File > Import... and select either:"
echo "   - MOSAIC_Technical_Explainer_Tagged.mp3 (full version)"
echo "   - Individual chapter files"
echo "3. Create a playlist called 'MOSAIC Technical Explainer'"
echo "4. Add the imported files to the playlist"