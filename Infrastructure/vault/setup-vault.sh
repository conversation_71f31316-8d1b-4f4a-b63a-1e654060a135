#!/bin/bash

# MOSAIC HashiCorp Vault Setup Script
# Comprehensive secrets management configuration for MOSAIC Universal Framework

set -euo pipefail

# Configuration
VAULT_ADDR="${VAULT_ADDR:-http://localhost:8200}"
VAULT_TOKEN="${VAULT_TOKEN:-}"
ENVIRONMENT="${ENVIRONMENT:-dev}"
DEPLOYMENT_SCALE="${DEPLOYMENT_SCALE:-small}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if vault CLI is installed
    if ! command -v vault &> /dev/null; then
        log_error "Vault CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Vault is accessible
    if ! vault status &> /dev/null; then
        log_error "Cannot connect to Vault at $VAULT_ADDR"
        exit 1
    fi
    
    # Check if authenticated
    if [ -z "$VAULT_TOKEN" ]; then
        log_error "VAULT_TOKEN environment variable is not set"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Initialize Vault if needed
initialize_vault() {
    log_info "Checking Vault initialization status..."
    
    if vault status | grep -q "Initialized.*false"; then
        log_info "Initializing Vault..."
        vault operator init -key-shares=5 -key-threshold=3 > vault-init.txt
        log_success "Vault initialized. Keys saved to vault-init.txt"
        log_warning "Please securely store the unseal keys and root token!"
    else
        log_info "Vault is already initialized"
    fi
}

# Enable secret engines
enable_secret_engines() {
    log_info "Enabling secret engines..."
    
    # Enable KV v2 secret engine for MOSAIC secrets
    vault secrets enable -path=secret kv-v2 2>/dev/null || log_info "KV v2 engine already enabled"
    
    # Enable database secret engine for dynamic credentials
    vault secrets enable database 2>/dev/null || log_info "Database engine already enabled"
    
    # Enable PKI for certificate management
    vault secrets enable pki 2>/dev/null || log_info "PKI engine already enabled"
    vault secrets enable -path=pki_int pki 2>/dev/null || log_info "Intermediate PKI engine already enabled"
    
    # Enable transit for encryption as a service
    vault secrets enable transit 2>/dev/null || log_info "Transit engine already enabled"
    
    # Enable AWS secrets engine for cloud credentials
    vault secrets enable aws 2>/dev/null || log_info "AWS engine already enabled"
    
    log_success "Secret engines enabled"
}

# Configure PKI
configure_pki() {
    log_info "Configuring PKI for certificate management..."
    
    # Configure root CA
    vault write pki/config/ca pem_bundle="$(cat certs/ca-cert.pem certs/ca-key.pem)" 2>/dev/null || {
        vault write -field=certificate pki/root/generate/internal \
            common_name="MOSAIC Root CA" \
            ttl=87600h > ca-cert.pem
    }
    
    # Configure intermediate CA
    vault write -format=json pki_int/intermediate/generate/internal \
        common_name="MOSAIC Intermediate CA" \
        | jq -r '.data.csr' > pki_intermediate.csr
    
    vault write -format=json pki/root/sign-intermediate \
        csr=@pki_intermediate.csr \
        format=pem_bundle ttl="43800h" \
        | jq -r '.data.certificate' > intermediate.cert.pem
    
    vault write pki_int/intermediate/set-signed certificate=@intermediate.cert.pem
    
    # Configure certificate roles
    vault write pki_int/roles/mosaic-server \
        allowed_domains="mosaic.alias.dev,localhost" \
        allow_subdomains=true \
        max_ttl="720h"
    
    vault write pki_int/roles/mosaic-client \
        allowed_domains="mosaic.alias.dev" \
        allow_subdomains=true \
        client_flag=true \
        max_ttl="720h"
    
    log_success "PKI configured"
}

# Configure database connections
configure_database() {
    log_info "Configuring database connections..."
    
    # PostgreSQL connection
    vault write database/config/mosaic-postgres \
        plugin_name=postgresql-database-plugin \
        connection_url="postgresql://{{username}}:{{password}}@postgres:5432/mosaic?sslmode=disable" \
        allowed_roles="mosaic-postgres-role" \
        username="vault" \
        password="$POSTGRES_VAULT_PASSWORD"
    
    # Redis connection
    vault write database/config/mosaic-redis \
        plugin_name=redis-database-plugin \
        host="redis" \
        port=6379 \
        username="vault" \
        password="$REDIS_VAULT_PASSWORD" \
        allowed_roles="mosaic-redis-role"
    
    # Create database roles
    vault write database/roles/mosaic-postgres-role \
        db_name=mosaic-postgres \
        creation_statements="CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; GRANT SELECT ON ALL TABLES IN SCHEMA public TO \"{{name}}\";" \
        default_ttl="1h" \
        max_ttl="24h"
    
    vault write database/roles/mosaic-redis-role \
        db_name=mosaic-redis \
        creation_statements='["ACL SETUSER {{name}} on >{{password}} ~* +@all"]' \
        default_ttl="1h" \
        max_ttl="24h"
    
    log_success "Database connections configured"
}

# Configure transit encryption
configure_transit() {
    log_info "Configuring transit encryption..."
    
    # Create encryption keys for different data types
    vault write -f transit/keys/mosaic-general
    vault write -f transit/keys/mosaic-pii
    vault write -f transit/keys/mosaic-financial
    vault write -f transit/keys/mosaic-health
    
    log_success "Transit encryption configured"
}

# Enable authentication methods
enable_auth_methods() {
    log_info "Enabling authentication methods..."
    
    # Enable Kubernetes auth
    vault auth enable kubernetes 2>/dev/null || log_info "Kubernetes auth already enabled"
    
    # Enable AWS auth
    vault auth enable aws 2>/dev/null || log_info "AWS auth already enabled"
    
    # Enable AppRole auth for applications
    vault auth enable approle 2>/dev/null || log_info "AppRole auth already enabled"
    
    # Enable userpass for human users
    vault auth enable userpass 2>/dev/null || log_info "Userpass auth already enabled"
    
    log_success "Authentication methods enabled"
}

# Configure Kubernetes auth
configure_kubernetes_auth() {
    log_info "Configuring Kubernetes authentication..."
    
    # Get Kubernetes cluster info
    K8S_HOST="https://kubernetes.default.svc:443"
    K8S_CA_CERT=$(kubectl config view --raw --minify --flatten -o jsonpath='{.clusters[].cluster.certificate-authority-data}' | base64 -d)
    
    vault write auth/kubernetes/config \
        token_reviewer_jwt="$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)" \
        kubernetes_host="$K8S_HOST" \
        kubernetes_ca_cert="$K8S_CA_CERT"
    
    # Create roles for MOSAIC lifecycles
    for lifecycle in apex-lc prism-lc aurora-lc pulse-lc nexus-lc flux-lc spark-lc shield-lc quantum-lc echo-lc flow-lc; do
        vault write auth/kubernetes/role/mosaic-$lifecycle \
            bound_service_account_names="mosaic-$lifecycle" \
            bound_service_account_namespaces="mosaic" \
            policies="mosaic-$lifecycle-policy" \
            ttl=24h
    done
    
    log_success "Kubernetes authentication configured"
}

# Create policies
create_policies() {
    log_info "Creating Vault policies..."
    
    # Apply main MOSAIC secrets policy
    vault policy write mosaic-secrets policies/mosaic-secrets.hcl
    
    # Create lifecycle-specific policies
    for lifecycle in apex-lc prism-lc aurora-lc pulse-lc nexus-lc flux-lc spark-lc shield-lc quantum-lc echo-lc flow-lc; do
        cat > "policies/mosaic-$lifecycle-policy.hcl" << EOF
# Policy for $lifecycle
path "secret/data/mosaic/lifecycles/$lifecycle/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/shared/*" {
  capabilities = ["read", "list"]
}

path "database/creds/mosaic-*" {
  capabilities = ["read"]
}

path "transit/encrypt/mosaic-*" {
  capabilities = ["create", "update"]
}

path "transit/decrypt/mosaic-*" {
  capabilities = ["create", "update"]
}
EOF
        vault policy write "mosaic-$lifecycle-policy" "policies/mosaic-$lifecycle-policy.hcl"
    done
    
    log_success "Policies created"
}

# Populate initial secrets
populate_secrets() {
    log_info "Populating initial secrets..."
    
    # Database credentials
    vault kv put secret/mosaic/database/postgres \
        username="mosaic_user" \
        password="$(openssl rand -base64 32)" \
        host="postgres" \
        port="5432" \
        database="mosaic"
    
    vault kv put secret/mosaic/database/redis \
        password="$(openssl rand -base64 32)" \
        host="redis" \
        port="6379"
    
    # Kafka credentials
    vault kv put secret/mosaic/infrastructure/kafka \
        bootstrap_servers="kafka:9092" \
        security_protocol="SASL_PLAINTEXT" \
        sasl_mechanism="PLAIN" \
        sasl_username="mosaic" \
        sasl_password="$(openssl rand -base64 32)"
    
    # JWT secrets
    vault kv put secret/mosaic/auth/jwt \
        secret="$(openssl rand -base64 64)" \
        issuer="mosaic.alias.dev" \
        audience="mosaic-api"
    
    # API keys for external services
    vault kv put secret/mosaic/api-keys/openai \
        api_key="$OPENAI_API_KEY"
    
    vault kv put secret/mosaic/api-keys/anthropic \
        api_key="$ANTHROPIC_API_KEY"
    
    # Monitoring secrets
    vault kv put secret/mosaic/monitoring/grafana \
        admin_password="$(openssl rand -base64 32)"
    
    vault kv put secret/mosaic/monitoring/prometheus \
        basic_auth_password="$(openssl rand -base64 32)"
    
    log_success "Initial secrets populated"
}

# Configure audit logging
configure_audit() {
    log_info "Configuring audit logging..."
    
    vault audit enable file file_path=/vault/logs/audit.log
    
    log_success "Audit logging configured"
}

# Main execution
main() {
    log_info "Starting MOSAIC Vault setup..."
    
    check_prerequisites
    initialize_vault
    enable_secret_engines
    configure_pki
    configure_database
    configure_transit
    enable_auth_methods
    configure_kubernetes_auth
    create_policies
    populate_secrets
    configure_audit
    
    log_success "MOSAIC Vault setup completed successfully!"
    log_info "Next steps:"
    log_info "1. Securely store the unseal keys and root token"
    log_info "2. Configure your applications to use Vault for secrets"
    log_info "3. Set up secret rotation schedules"
    log_info "4. Monitor audit logs for security events"
}

# Run main function
main "$@"
