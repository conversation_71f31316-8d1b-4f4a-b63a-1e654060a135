# MOSAIC Secrets Management Policies for HashiCorp Vault
# Comprehensive secret access control for all MOSAIC components

# Database secrets policy
path "secret/data/mosaic/database/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/database/*" {
  capabilities = ["list", "read", "delete"]
}

# API keys and external service credentials
path "secret/data/mosaic/api-keys/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/api-keys/*" {
  capabilities = ["list", "read", "delete"]
}

# Infrastructure secrets (Kafka, Redis, etc.)
path "secret/data/mosaic/infrastructure/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/infrastructure/*" {
  capabilities = ["list", "read", "delete"]
}

# SSL/TLS certificates
path "secret/data/mosaic/certificates/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/certificates/*" {
  capabilities = ["list", "read", "delete"]
}

# Authentication tokens and JWT secrets
path "secret/data/mosaic/auth/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/auth/*" {
  capabilities = ["list", "read", "delete"]
}

# Cloud provider credentials
path "secret/data/mosaic/cloud/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/cloud/*" {
  capabilities = ["list", "read", "delete"]
}

# Monitoring and observability secrets
path "secret/data/mosaic/monitoring/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/monitoring/*" {
  capabilities = ["list", "read", "delete"]
}

# Home Assistant integration secrets
path "secret/data/mosaic/home-assistant/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/home-assistant/*" {
  capabilities = ["list", "read", "delete"]
}

# Apple ecosystem integration secrets
path "secret/data/mosaic/apple/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/apple/*" {
  capabilities = ["list", "read", "delete"]
}

# AI and ML service credentials
path "secret/data/mosaic/ai/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/ai/*" {
  capabilities = ["list", "read", "delete"]
}

# Lifecycle-specific secrets
path "secret/data/mosaic/lifecycles/apex-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/prism-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/aurora-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/pulse-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/nexus-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/flux-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/spark-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/shield-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/quantum-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/echo-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/lifecycles/flow-lc/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Environment-specific access controls
path "secret/data/mosaic/environments/dev/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/environments/staging/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/data/mosaic/environments/prod/*" {
  capabilities = ["read", "list"]
}

# Backup and disaster recovery secrets
path "secret/data/mosaic/backup/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/backup/*" {
  capabilities = ["list", "read", "delete"]
}

# Deployment and CI/CD secrets
path "secret/data/mosaic/cicd/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "secret/metadata/mosaic/cicd/*" {
  capabilities = ["list", "read", "delete"]
}

# Dynamic secrets for databases
path "database/creds/mosaic-postgres-*" {
  capabilities = ["read"]
}

path "database/creds/mosaic-redis-*" {
  capabilities = ["read"]
}

# PKI for certificate management
path "pki/issue/mosaic-server" {
  capabilities = ["create", "update"]
}

path "pki/issue/mosaic-client" {
  capabilities = ["create", "update"]
}

# Transit encryption for sensitive data
path "transit/encrypt/mosaic-*" {
  capabilities = ["create", "update"]
}

path "transit/decrypt/mosaic-*" {
  capabilities = ["create", "update"]
}

# Kubernetes auth method
path "auth/kubernetes/role/mosaic-*" {
  capabilities = ["read"]
}

# AWS auth method for cloud resources
path "auth/aws/role/mosaic-*" {
  capabilities = ["read"]
}

# Audit and compliance
path "sys/audit" {
  capabilities = ["read", "list"]
}

path "sys/audit-hash" {
  capabilities = ["create", "update"]
}

# Health and status monitoring
path "sys/health" {
  capabilities = ["read"]
}

path "sys/metrics" {
  capabilities = ["read"]
}

# Secret rotation capabilities
path "secret/data/mosaic/rotation/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Emergency break-glass access (restricted)
path "secret/data/mosaic/emergency/*" {
  capabilities = ["read"]
}

# Terraform state encryption keys
path "secret/data/mosaic/terraform/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Container registry credentials
path "secret/data/mosaic/registry/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Webhook and notification secrets
path "secret/data/mosaic/webhooks/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Feature flag and configuration secrets
path "secret/data/mosaic/feature-flags/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Analytics and telemetry secrets
path "secret/data/mosaic/analytics/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Third-party integration secrets
path "secret/data/mosaic/integrations/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# License and subscription keys
path "secret/data/mosaic/licenses/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Development and testing secrets
path "secret/data/mosaic/testing/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Bucket.co integration secrets
path "secret/data/mosaic/bucket/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# ReNative cross-platform secrets
path "secret/data/mosaic/renative/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Uncloud CLI secrets
path "secret/data/mosaic/uncloud/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}
