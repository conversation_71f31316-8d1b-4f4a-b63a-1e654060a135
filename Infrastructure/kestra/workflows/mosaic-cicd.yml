id: mosaic-cicd-pipeline
namespace: mosaic.deployment

description: |
  MOSAIC Universal Framework CI/CD Pipeline
  Supports automated testing, building, and deployment across multiple environments
  with dynamic scaling based on deployment context

labels:
  project: MOSAIC
  team: ALIAS
  environment: "{{ environment }}"

inputs:
  - id: environment
    type: SELECT
    defaults: dev
    values:
      - dev
      - staging
      - prod
    description: Target environment for deployment

  - id: deployment_scale
    type: SELECT
    defaults: small
    values:
      - solo
      - small
      - enterprise
    description: Deployment scale configuration

  - id: cloud_provider
    type: SELECT
    defaults: aws
    values:
      - aws
      - gcp
      - azure
    description: Target cloud provider

  - id: git_branch
    type: STRING
    defaults: main
    description: Git branch to deploy

  - id: force_rebuild
    type: BOOLEAN
    defaults: false
    description: Force rebuild of all Docker images

  - id: run_integration_tests
    type: BOOLEAN
    defaults: true
    description: Run integration tests before deployment

  - id: enable_rollback
    type: BOOLEAN
    defaults: true
    description: Enable automatic rollback on failure

variables:
  deployment_id: "{{ execution.startDate | date('yyyyMMdd-HHmmss') }}-{{ inputs.deployment_scale }}-{{ inputs.environment }}"
  docker_registry: "mosaic-registry.alias.dev"
  terraform_workspace: "{{ inputs.environment }}-{{ inputs.cloud_provider }}"
  
tasks:
  # Pre-deployment validation and setup
  - id: validate-inputs
    type: io.kestra.core.tasks.scripts.Bash
    description: Validate deployment inputs and prerequisites
    script: |
      echo "🔍 Validating deployment inputs..."
      
      # Validate environment
      if [[ ! "{{ inputs.environment }}" =~ ^(dev|staging|prod)$ ]]; then
        echo "❌ Invalid environment: {{ inputs.environment }}"
        exit 1
      fi
      
      # Validate scale
      if [[ ! "{{ inputs.deployment_scale }}" =~ ^(solo|small|enterprise)$ ]]; then
        echo "❌ Invalid deployment scale: {{ inputs.deployment_scale }}"
        exit 1
      fi
      
      # Check cloud provider credentials
      case "{{ inputs.cloud_provider }}" in
        aws)
          aws sts get-caller-identity || exit 1
          ;;
        gcp)
          gcloud auth list --filter=status:ACTIVE || exit 1
          ;;
        azure)
          az account show || exit 1
          ;;
      esac
      
      echo "✅ All inputs validated successfully"
      echo "📋 Deployment ID: {{ vars.deployment_id }}"

  - id: checkout-code
    type: io.kestra.plugin.git.Clone
    description: Checkout MOSAIC source code
    url: https://github.com/alias-organization/mosaic.git
    branch: "{{ inputs.git_branch }}"
    username: "{{ secret('GIT_USERNAME') }}"
    password: "{{ secret('GIT_TOKEN') }}"

  # Infrastructure provisioning
  - id: terraform-plan
    type: io.kestra.core.tasks.scripts.Bash
    description: Plan Terraform infrastructure changes
    workingDirectory: "{{ outputs.checkout-code.directory }}/infrastructure/terraform"
    script: |
      echo "🏗️ Planning Terraform infrastructure..."
      
      # Initialize Terraform
      terraform init \
        -backend-config="bucket={{ secret('TERRAFORM_STATE_BUCKET') }}" \
        -backend-config="key=mosaic/{{ vars.terraform_workspace }}/terraform.tfstate" \
        -backend-config="region={{ secret('AWS_REGION') }}"
      
      # Select or create workspace
      terraform workspace select {{ vars.terraform_workspace }} || \
      terraform workspace new {{ vars.terraform_workspace }}
      
      # Plan infrastructure
      terraform plan \
        -var="environment={{ inputs.environment }}" \
        -var="deployment_scale={{ inputs.deployment_scale }}" \
        -var="cloud_provider={{ inputs.cloud_provider }}" \
        -var="domain_name={{ secret('DOMAIN_NAME') }}" \
        -out=tfplan
      
      echo "✅ Terraform plan completed"

  - id: terraform-apply
    type: io.kestra.core.tasks.scripts.Bash
    description: Apply Terraform infrastructure changes
    workingDirectory: "{{ outputs.checkout-code.directory }}/infrastructure/terraform"
    script: |
      echo "🚀 Applying Terraform infrastructure..."
      
      terraform apply -auto-approve tfplan
      
      # Export outputs for later use
      terraform output -json > terraform-outputs.json
      
      echo "✅ Infrastructure provisioned successfully"
    outputs:
      - terraform-outputs.json

  # Build and test phase
  - id: build-lifecycle-images
    type: io.kestra.core.tasks.flows.Parallel
    description: Build all MOSAIC lifecycle Docker images in parallel
    tasks:
      - id: build-apex-lc
        type: io.kestra.plugin.docker.Build
        dockerfile: mosaic/apex-lc/Dockerfile
        tags:
          - "{{ vars.docker_registry }}/apex-lc:{{ vars.deployment_id }}"
          - "{{ vars.docker_registry }}/apex-lc:latest"
        push: true

      - id: build-prism-lc
        type: io.kestra.plugin.docker.Build
        dockerfile: mosaic/prism-lc/Dockerfile
        tags:
          - "{{ vars.docker_registry }}/prism-lc:{{ vars.deployment_id }}"
          - "{{ vars.docker_registry }}/prism-lc:latest"
        push: true

      - id: build-aurora-lc
        type: io.kestra.plugin.docker.Build
        dockerfile: mosaic/aurora-lc/Dockerfile
        tags:
          - "{{ vars.docker_registry }}/aurora-lc:{{ vars.deployment_id }}"
          - "{{ vars.docker_registry }}/aurora-lc:latest"
        push: true

      - id: build-pulse-lc
        type: io.kestra.plugin.docker.Build
        dockerfile: mosaic/pulse-lc/Dockerfile
        tags:
          - "{{ vars.docker_registry }}/pulse-lc:{{ vars.deployment_id }}"
          - "{{ vars.docker_registry }}/pulse-lc:latest"
        push: true

  - id: run-unit-tests
    type: io.kestra.core.tasks.scripts.Bash
    description: Run comprehensive unit tests
    workingDirectory: "{{ outputs.checkout-code.directory }}"
    script: |
      echo "🧪 Running unit tests..."
      
      # Install dependencies
      npm install
      
      # Run tests for each lifecycle
      for lifecycle in apex-lc prism-lc aurora-lc pulse-lc; do
        echo "Testing $lifecycle..."
        cd mosaic/$lifecycle
        npm test -- --coverage --ci
        cd ../..
      done
      
      echo "✅ All unit tests passed"

  - id: run-integration-tests
    type: io.kestra.core.tasks.scripts.Bash
    description: Run integration tests
    condition: "{{ inputs.run_integration_tests }}"
    workingDirectory: "{{ outputs.checkout-code.directory }}"
    script: |
      echo "🔗 Running integration tests..."
      
      # Start test infrastructure
      cd mosaic/tests
      docker-compose -f docker-compose.test.yml up -d
      
      # Wait for services to be ready
      sleep 30
      
      # Run integration tests
      npm run test:integration
      
      # Cleanup
      docker-compose -f docker-compose.test.yml down
      
      echo "✅ Integration tests completed"

  # Deployment phase
  - id: deploy-with-ansible
    type: io.kestra.core.tasks.scripts.Bash
    description: Deploy MOSAIC using Ansible
    workingDirectory: "{{ outputs.checkout-code.directory }}/infrastructure/ansible"
    script: |
      echo "🚀 Deploying MOSAIC with Ansible..."
      
      # Create inventory based on Terraform outputs
      python3 scripts/generate-inventory.py \
        --terraform-output ../terraform/terraform-outputs.json \
        --environment {{ inputs.environment }} \
        --scale {{ inputs.deployment_scale }}
      
      # Run Ansible playbook
      ansible-playbook \
        -i inventory/{{ inputs.environment }}.yml \
        playbooks/deploy-mosaic.yml \
        -e environment={{ inputs.environment }} \
        -e deployment_scale={{ inputs.deployment_scale }} \
        -e cloud_provider={{ inputs.cloud_provider }} \
        -e mosaic_version={{ vars.deployment_id }}
      
      echo "✅ Deployment completed successfully"

  # Post-deployment validation
  - id: health-check
    type: io.kestra.core.tasks.scripts.Bash
    description: Perform comprehensive health checks
    script: |
      echo "🏥 Performing health checks..."
      
      # Get deployment endpoints from Terraform outputs
      ENDPOINTS=$(cat {{ outputs.terraform-apply.outputFiles['terraform-outputs.json'] }} | jq -r '.load_balancer_dns.value')
      
      # Check each lifecycle endpoint
      for lifecycle in apex-lc prism-lc aurora-lc pulse-lc; do
        echo "Checking $lifecycle health..."
        curl -f "https://$ENDPOINTS/$lifecycle/health" || exit 1
      done
      
      # Check infrastructure health
      echo "Checking infrastructure health..."
      curl -f "https://$ENDPOINTS/health/infrastructure" || exit 1
      
      echo "✅ All health checks passed"

  - id: performance-test
    type: io.kestra.core.tasks.scripts.Bash
    description: Run performance tests
    condition: "{{ inputs.environment == 'staging' or inputs.environment == 'prod' }}"
    script: |
      echo "⚡ Running performance tests..."
      
      # Install k6 for load testing
      curl -s https://github.com/grafana/k6/releases/download/v0.45.0/k6-v0.45.0-linux-amd64.tar.gz | tar xz
      
      # Run load tests
      ./k6-v0.45.0-linux-amd64/k6 run \
        --vus {{ inputs.deployment_scale == 'enterprise' and '100' or '10' }} \
        --duration 5m \
        tests/performance/load-test.js
      
      echo "✅ Performance tests completed"

  # Monitoring and alerting setup
  - id: configure-monitoring
    type: io.kestra.core.tasks.scripts.Bash
    description: Configure monitoring and alerting
    script: |
      echo "📊 Configuring monitoring and alerting..."
      
      # Deploy monitoring stack
      helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
      helm repo add grafana https://grafana.github.io/helm-charts
      helm repo update
      
      # Install Prometheus
      helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --values monitoring/prometheus-values.yml
      
      # Configure Better Stack integration
      curl -X POST "https://uptime.betterstack.com/api/v2/monitors" \
        -H "Authorization: Bearer {{ secret('BETTER_STACK_TOKEN') }}" \
        -H "Content-Type: application/json" \
        -d '{
          "url": "https://{{ outputs.terraform-apply.outputFiles.load_balancer_dns }}/health",
          "monitor_type": "status",
          "check_frequency": 60,
          "request_timeout": 30
        }'
      
      echo "✅ Monitoring configured successfully"

  # Cleanup and notification
  - id: cleanup-old-deployments
    type: io.kestra.core.tasks.scripts.Bash
    description: Cleanup old deployments and resources
    script: |
      echo "🧹 Cleaning up old deployments..."
      
      # Remove old Docker images (keep last 5)
      docker images {{ vars.docker_registry }}/apex-lc --format "table {{.Tag}}" | \
        tail -n +6 | xargs -I {} docker rmi {{ vars.docker_registry }}/apex-lc:{}
      
      # Cleanup old Terraform state backups
      aws s3 ls s3://{{ secret('TERRAFORM_STATE_BUCKET') }}/mosaic/backups/ | \
        sort -k1,2 | head -n -10 | awk '{print $4}' | \
        xargs -I {} aws s3 rm s3://{{ secret('TERRAFORM_STATE_BUCKET') }}/mosaic/backups/{}
      
      echo "✅ Cleanup completed"

  - id: send-notification
    type: io.kestra.plugin.notifications.slack.SlackExecution
    description: Send deployment notification
    url: "{{ secret('SLACK_WEBHOOK_URL') }}"
    channel: "#mosaic-deployments"
    customMessage: |
      🚀 *MOSAIC Deployment Completed*
      
      *Environment:* {{ inputs.environment }}
      *Scale:* {{ inputs.deployment_scale }}
      *Cloud Provider:* {{ inputs.cloud_provider }}
      *Deployment ID:* {{ vars.deployment_id }}
      *Branch:* {{ inputs.git_branch }}
      
      *Status:* ✅ Success
      *Duration:* {{ execution.duration }}
      
      *Endpoints:*
      • Dashboard: https://{{ outputs.terraform-apply.outputFiles.load_balancer_dns }}
      • Monitoring: https://{{ outputs.terraform-apply.outputFiles.monitoring_endpoints.grafana }}
      • API: https://{{ outputs.terraform-apply.outputFiles.load_balancer_dns }}/api

# Error handling and rollback
errors:
  - id: rollback-on-failure
    type: io.kestra.core.tasks.scripts.Bash
    condition: "{{ inputs.enable_rollback }}"
    description: Automatic rollback on deployment failure
    script: |
      echo "🔄 Initiating automatic rollback..."
      
      # Get previous successful deployment
      PREVIOUS_VERSION=$(aws s3 ls s3://{{ secret('DEPLOYMENT_HISTORY_BUCKET') }}/{{ inputs.environment }}/ | \
        sort -k1,2 -r | head -n 2 | tail -n 1 | awk '{print $4}')
      
      if [ -n "$PREVIOUS_VERSION" ]; then
        echo "Rolling back to: $PREVIOUS_VERSION"
        
        # Rollback using Ansible
        ansible-playbook \
          -i inventory/{{ inputs.environment }}.yml \
          playbooks/rollback-mosaic.yml \
          -e target_version=$PREVIOUS_VERSION
        
        echo "✅ Rollback completed"
      else
        echo "❌ No previous version found for rollback"
        exit 1
      fi

  - id: failure-notification
    type: io.kestra.plugin.notifications.slack.SlackExecution
    url: "{{ secret('SLACK_WEBHOOK_URL') }}"
    channel: "#mosaic-alerts"
    customMessage: |
      🚨 *MOSAIC Deployment Failed*
      
      *Environment:* {{ inputs.environment }}
      *Deployment ID:* {{ vars.deployment_id }}
      *Error:* {{ error.message }}
      
      *Action Required:* Manual intervention needed
      *Logs:* Check Kestra execution logs for details
