---
# MOSAIC Deployment Playbook - Comprehensive Application Deployment
# Supports solo, small business, and enterprise deployments

- name: Deploy MOSAIC Universal Framework
  hosts: all
  become: yes
  gather_facts: yes
  vars:
    mosaic_version: "{{ mosaic_version | default('latest') }}"
    deployment_scale: "{{ deployment_scale | default('small') }}"
    environment: "{{ environment | default('dev') }}"
    cloud_provider: "{{ cloud_provider | default('aws') }}"
    
    # Scale-specific configurations
    scale_configs:
      solo:
        kafka_replicas: 1
        redis_replicas: 1
        postgres_replicas: 1
        lifecycle_replicas: 1
        resource_limits:
          cpu: "500m"
          memory: "1Gi"
        resource_requests:
          cpu: "250m"
          memory: "512Mi"
      small:
        kafka_replicas: 3
        redis_replicas: 3
        postgres_replicas: 2
        lifecycle_replicas: 2
        resource_limits:
          cpu: "2"
          memory: "4Gi"
        resource_requests:
          cpu: "1"
          memory: "2Gi"
      enterprise:
        kafka_replicas: 9
        redis_replicas: 9
        postgres_replicas: 3
        lifecycle_replicas: 5
        resource_limits:
          cpu: "8"
          memory: "16Gi"
        resource_requests:
          cpu: "4"
          memory: "8Gi"
    
    current_scale: "{{ scale_configs[deployment_scale] }}"
    
    # MOSAIC Lifecycles
    mosaic_lifecycles:
      - name: apex-lc
        port: 3001
        description: "Autonomous Persona-Enhanced eXecution Lifecycle"
      - name: prism-lc
        port: 3002
        description: "Pattern Recognition and Insight Synthesis Management"
      - name: aurora-lc
        port: 3003
        description: "Automated User Relationship and Optimization Resource Allocation"
      - name: pulse-lc
        port: 3004
        description: "Performance Understanding and Lifecycle Synchronization Engine"
      - name: nexus-lc
        port: 3005
        description: "Network eXchange and Unified Systems"
      - name: flux-lc
        port: 3006
        description: "Flexible Learning and User eXperience"
      - name: spark-lc
        port: 3007
        description: "Strategic Planning and Resource Coordination"
      - name: shield-lc
        port: 3008
        description: "Security, Health, and Infrastructure Enhancement Lifecycle Defense"
      - name: quantum-lc
        port: 3009
        description: "Quality and User Analytics with Network Transformation and Understanding Management"
      - name: echo-lc
        port: 3010
        description: "Event Coordination and Harmonization Operations"
      - name: flow-lc
        port: 3011
        description: "Federated Lifecycle Operations Workflow"

  pre_tasks:
    - name: Validate deployment parameters
      assert:
        that:
          - deployment_scale in ['solo', 'small', 'enterprise']
          - environment in ['dev', 'staging', 'prod']
          - cloud_provider in ['aws', 'gcp', 'azure']
        fail_msg: "Invalid deployment parameters"

    - name: Set deployment facts
      set_fact:
        deployment_timestamp: "{{ ansible_date_time.epoch }}"
        deployment_id: "{{ ansible_date_time.epoch }}-{{ deployment_scale }}-{{ environment }}"

  tasks:
    - name: Create MOSAIC directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /opt/mosaic
        - /opt/mosaic/config
        - /opt/mosaic/data
        - /opt/mosaic/logs
        - /opt/mosaic/backups
        - /var/log/mosaic

    - name: Install system dependencies
      package:
        name: "{{ item }}"
        state: present
      loop:
        - docker
        - docker-compose
        - curl
        - wget
        - unzip
        - jq
        - htop
        - vim
        - git

    - name: Start and enable Docker
      systemd:
        name: docker
        state: started
        enabled: yes

    - name: Add user to docker group
      user:
        name: "{{ ansible_user }}"
        groups: docker
        append: yes

    - name: Install kubectl
      get_url:
        url: "https://dl.k8s.io/release/v1.28.0/bin/linux/amd64/kubectl"
        dest: /usr/local/bin/kubectl
        mode: '0755'

    - name: Install Helm
      shell: |
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
      args:
        creates: /usr/local/bin/helm

    - name: Create MOSAIC configuration
      template:
        src: mosaic-config.yml.j2
        dest: /opt/mosaic/config/mosaic.yml
        mode: '0644'
      notify: restart mosaic services

    - name: Create Docker Compose file
      template:
        src: docker-compose.yml.j2
        dest: /opt/mosaic/docker-compose.yml
        mode: '0644'
      notify: restart mosaic services

    - name: Create Kubernetes manifests
      template:
        src: "{{ item }}.yml.j2"
        dest: "/opt/mosaic/k8s-{{ item }}.yml"
        mode: '0644'
      loop:
        - namespace
        - configmap
        - secrets
        - kafka
        - redis
        - postgres
        - lifecycles
        - ingress
        - monitoring
      when: deployment_scale != 'solo'

    - name: Pull MOSAIC Docker images
      docker_image:
        name: "{{ item }}"
        source: pull
        force_source: yes
      loop:
        - "mosaic/apex-lc:{{ mosaic_version }}"
        - "mosaic/prism-lc:{{ mosaic_version }}"
        - "mosaic/aurora-lc:{{ mosaic_version }}"
        - "mosaic/pulse-lc:{{ mosaic_version }}"
        - "mosaic/nexus-lc:{{ mosaic_version }}"
        - "mosaic/flux-lc:{{ mosaic_version }}"
        - "mosaic/spark-lc:{{ mosaic_version }}"
        - "mosaic/shield-lc:{{ mosaic_version }}"
        - "mosaic/quantum-lc:{{ mosaic_version }}"
        - "mosaic/echo-lc:{{ mosaic_version }}"
        - "mosaic/flow-lc:{{ mosaic_version }}"

    - name: Deploy infrastructure services (solo/small)
      docker_compose:
        project_src: /opt/mosaic
        state: present
        pull: yes
      when: deployment_scale in ['solo', 'small']

    - name: Deploy to Kubernetes (enterprise)
      k8s:
        state: present
        definition: "{{ lookup('file', '/opt/mosaic/k8s-' + item + '.yml') | from_yaml_all | list }}"
        wait: true
        wait_timeout: 600
      loop:
        - namespace
        - configmap
        - secrets
        - kafka
        - redis
        - postgres
        - lifecycles
        - ingress
        - monitoring
      when: deployment_scale == 'enterprise'

    - name: Wait for services to be ready
      uri:
        url: "http://localhost:{{ item.port }}/health"
        method: GET
        status_code: 200
      register: health_check
      until: health_check.status == 200
      retries: 30
      delay: 10
      loop: "{{ mosaic_lifecycles }}"
      when: deployment_scale in ['solo', 'small']

    - name: Configure monitoring and alerting
      include_tasks: monitoring.yml

    - name: Configure backup and disaster recovery
      include_tasks: backup.yml

    - name: Configure security hardening
      include_tasks: security.yml

    - name: Run post-deployment validation
      include_tasks: validation.yml

    - name: Generate deployment report
      template:
        src: deployment-report.md.j2
        dest: "/opt/mosaic/deployment-report-{{ deployment_id }}.md"
        mode: '0644'

  handlers:
    - name: restart mosaic services
      docker_compose:
        project_src: /opt/mosaic
        state: present
        restarted: yes
      when: deployment_scale in ['solo', 'small']

    - name: restart kubernetes services
      k8s:
        state: present
        definition: "{{ lookup('file', '/opt/mosaic/k8s-lifecycles.yml') | from_yaml_all | list }}"
        wait: true
        wait_timeout: 300
      when: deployment_scale == 'enterprise'

  post_tasks:
    - name: Display deployment summary
      debug:
        msg: |
          MOSAIC Deployment Complete!
          
          Deployment ID: {{ deployment_id }}
          Scale: {{ deployment_scale }}
          Environment: {{ environment }}
          Cloud Provider: {{ cloud_provider }}
          
          Services deployed:
          {% for lifecycle in mosaic_lifecycles %}
          - {{ lifecycle.name }}: http://localhost:{{ lifecycle.port }}
          {% endfor %}
          
          Configuration: /opt/mosaic/config/mosaic.yml
          Logs: /var/log/mosaic/
          Report: /opt/mosaic/deployment-report-{{ deployment_id }}.md
          
          Next steps:
          1. Verify all services are running: mosaic status
          2. Check logs: mosaic logs --follow
          3. Access monitoring dashboard: http://localhost:3000
          4. Review deployment report for any issues

    - name: Send deployment notification
      uri:
        url: "{{ webhook_url }}"
        method: POST
        body_format: json
        body:
          deployment_id: "{{ deployment_id }}"
          status: "completed"
          scale: "{{ deployment_scale }}"
          environment: "{{ environment }}"
          timestamp: "{{ deployment_timestamp }}"
          services: "{{ mosaic_lifecycles | length }}"
      when: webhook_url is defined
