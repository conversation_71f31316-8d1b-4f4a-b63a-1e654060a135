# ALIAS Hardware Infrastructure Master Plan
**Version:** 1.0  
**Date:** July 01, 2025  
**Status:** Implementation Ready  
**Budget:** Enterprise Starter Configuration

## 🎯 Executive Summary

ALIAS infrastructure evolution from current single-server setup to distributed HA architecture with office Threadripper + NextDC presence, connected via dark fiber.

## 📊 Current Infrastructure Analysis

### Existing Hardware (Proxmox Scan Results)
```yaml
current_server:
  cpu: Intel i7-14700F (20C/28T)
  ram: 128GB DDR5 (15.66GB active)
  storage:
    boot: 476GB NVMe (95% FULL - CRITICAL)
    data: Multiple SSDs (mixed usage)
  network: 1GbE + Tailscale
  workload:
    vms: 1 (minimal usage)
    containers: 6 LXC + 23 Docker
    gitlab: Running on port 8080
```

### Immediate Issues
1. **Root partition 95% full** - needs immediate cleanup
2. **Port 80/443 conflicts** - GitLab misconfigured
3. **No VLANs** - security/isolation concerns
4. **No HA** - single point of failure

## 🏗️ Phase 1: Office Threadripper Build (Q1 2025)

### Hardware Specification
```yaml
threadripper_office_server:
  cpu: AMD Threadripper PRO 7955WX
    cores: 16C/32T
    reason: "Sweet spot for price/performance"
  
  motherboard: ASUS Pro WS WRX90E-SAGE SE
    features: 
      - "8-channel DDR5"
      - "Multiple PCIe 5.0 slots"
      - "IPMI for remote management"
  
  memory: 256GB DDR5-5200 ECC (8x32GB)
    expandable: "Up to 2TB"
  
  storage:
    boot:
      - 2x 2TB Samsung 990 PRO (mirror)
    vm_pool:
      - 2x 4TB Samsung 990 PRO (mirror)
    data_pool:
      - 4x 8TB Samsung 870 QVO (RAIDZ1)
    cache:
      - 1x 2TB Solidigm P5520 (L2ARC)
  
  networking:
    10gbe: Intel X710-DA2 (2x SFP+)
    management: Onboard 2.5GbE
    backup: Mellanox ConnectX-3 (IB/10GbE)
  
  power:
    psu: 2x 1200W Platinum (redundant)
    ups: APC SMX3000 (30min runtime)
  
  total_cost: ~$15,000 AUD
```

### Network Infrastructure Upgrade
```yaml
unifi_network:
  gateway:
    model: UniFi Dream Machine SE
    price: $929
    features:
      - "8x 2.5GbE PoE++"
      - "2x 10GbE SFP+"
      - "IDS/IPS"
      - "Built-in controller"
  
  switches:
    office:
      model: UniFi Switch Enterprise 8 PoE
      price: $429
      use: "Office connections"
    
    server:
      model: UniFi Switch Aggregation
      price: $699
      use: "10G server backbone"
  
  access_points:
    model: UniFi U6 Pro (x3)
    price: $279 each
    coverage: "Full office coverage"
  
  total_network: ~$2,900 AUD
```

## 🌐 Phase 2: NextDC Colocation (Q2 2025)

### Rack Configuration
```yaml
nextdc_deployment:
  location: NextDC S2 Sydney
  size: Quarter rack (11RU)
  power: 2kW redundant A+B
  
  monthly_costs:
    rack_space: $1,000
    power: $600 (2kW)
    cross_connect: $350
    ips: $100 (/28 block)
    total: $2,050/month
```

### HA Server Configuration
```yaml
ha_servers:
  primary:
    model: Dell PowerEdge R640
    cpu: 2x Xeon Gold 6226R
    ram: 256GB DDR4 ECC
    storage:
      - 2x 960GB BOSS boot
      - 4x 3.84TB U.2 NVMe
    network: 4x 25GbE SFP28
    cost: $8,000 (refurbished)
  
  backup:
    model: Dell PowerEdge R630
    cpu: 2x Xeon E5-2680v4
    ram: 128GB DDR4 ECC
    storage:
      - 2x 480GB SATA boot
      - 6x 2TB SAS SSD
    network: 4x 10GbE SFP+
    cost: $4,000 (refurbished)
  
  storage:
    model: Synology RS3621xs+
    drives: 8x 8TB Synology HAT5300
    capacity: 48TB usable (SHR-2)
    cost: $8,000
```

### Dark Fiber Connectivity
```yaml
dark_fiber:
  provider: Nexthop
  bandwidth: 10Gbps (upgradeable to 100G)
  
  costs:
    setup: $5,000
    monthly: $2,000
  
  configuration:
    office_end: "Threadripper 10G SFP+"
    nextdc_end: "Dell switch 10G SFP+"
    encryption: "MACsec AES-256"
    redundancy: "Backup via Wireguard/Tailscale"
```

## 🔧 Network Architecture (VLANs)

### VLAN Design
```yaml
vlan_structure:
  10_management:
    subnet: ************/24
    devices:
      - UniFi controllers
      - IPMI/iDRAC
      - Proxmox management
  
  20_infrastructure:
    subnet: ************/24
    services:
      - Proxmox cluster
      - GitLab
      - Monitoring
  
  30_containers:
    subnet: ************/24
    purpose: "Docker/LXC containers"
  
  40_dmz:
    subnet: ************/24
    services:
      - Reverse proxy
      - Public-facing services
  
  50_storage:
    subnet: ************/24
    purpose: "iSCSI/NFS traffic"
  
  100-199_clients:
    subnet: **********/16
    allocation: "/24 per client"
```

## 📈 Implementation Timeline

### Month 1: Foundation
- [ ] Clean up current server storage
- [ ] Order UniFi equipment
- [ ] Fix GitLab port configuration
- [ ] Implement basic VLANs

### Month 2: Office Build
- [ ] Assemble Threadripper server
- [ ] Deploy UniFi network
- [ ] Migrate services to VLANs
- [ ] Set up monitoring

### Month 3: NextDC Setup
- [ ] Provision NextDC space
- [ ] Install dark fiber
- [ ] Deploy HA servers
- [ ] Configure replication

### Month 4: Production
- [ ] GitLab Geo setup
- [ ] Automated failover
- [ ] Client migrations
- [ ] Documentation

## 💰 Total Investment

### CapEx
| Component | Cost (AUD) |
|-----------|------------|
| Threadripper Build | $15,000 |
| UniFi Network | $2,900 |
| NextDC Servers | $20,000 |
| Dark Fiber Setup | $5,000 |
| **Total CapEx** | **$42,900** |

### OpEx (Monthly)
| Service | Cost (AUD) |
|---------|------------|
| NextDC Colocation | $2,050 |
| Dark Fiber | $2,000 |
| Internet Backup | $500 |
| **Total OpEx** | **$4,550** |

## 🚀 GitLab Optimizations

### Resource Allocation
```yaml
gitlab_resources:
  office_threadripper:
    role: "Primary GitLab instance"
    allocation:
      vcpu: 24
      ram: 128GB
      storage: 2TB NVMe
    
  nextdc_primary:
    role: "GitLab Geo secondary"
    allocation:
      vcpu: 16
      ram: 64GB
      storage: 1TB NVMe
```

### Performance Tuning
```ruby
# /etc/gitlab/gitlab.rb optimizations
gitlab_rails['db_pool'] = 40
sidekiq['max_concurrency'] = 25
prometheus_monitoring['enable'] = true

# Enable Git LFS with object storage
gitlab_rails['lfs_enabled'] = true
gitlab_rails['lfs_object_store_enabled'] = true
gitlab_rails['lfs_object_store_connection'] = {
  'provider' => 'AWS',
  'endpoint' => 'http://*************:9000', # MinIO
}
```

## 🔐 Security Implementation

### Zero Trust Architecture
1. **Network segmentation** via VLANs
2. **mTLS** between all services
3. **RBAC** with SSO (Okta/Auth0)
4. **Secrets management** with Vault
5. **Audit logging** to SIEM

### Backup Strategy
- **3-2-1 Rule**: 3 copies, 2 media types, 1 offsite
- **GitLab**: Hourly snapshots, daily to S3
- **VMs**: Proxmox Backup Server
- **Databases**: Continuous replication

## 📊 Monitoring Stack

### Observability Platform
```yaml
monitoring:
  metrics:
    prometheus: "Federation with Thanos"
    grafana: "Custom ALIAS dashboards"
  
  logs:
    loki: "Log aggregation"
    promtail: "Log shipping"
  
  traces:
    tempo: "Distributed tracing"
    jaeger: "APM for GitLab"
  
  alerts:
    alertmanager: "PagerDuty integration"
    deadmanssnitch: "Cron monitoring"
```

---
*This infrastructure plan provides ALIAS with enterprise-grade capabilities while maintaining startup agility and cost-effectiveness.*