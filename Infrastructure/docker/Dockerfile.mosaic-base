# MOSAIC Base Container Image
# Multi-stage build for optimized production containers

# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    bash

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S mosaic && \
    adduser -S mosaic -u 1001

# Install runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    tzdata

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=mosaic:mosaic /app/dist ./dist
COPY --from=builder --chown=mosaic:mosaic /app/node_modules ./node_modules
COPY --from=builder --chown=mosaic:mosaic /app/package*.json ./

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/config && \
    chown -R mosaic:mosaic /app

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV LOG_LEVEL=info
ENV METRICS_PORT=9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Switch to non-root user
USER mosaic

# Expose ports
EXPOSE ${PORT} ${METRICS_PORT}

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Default command
CMD ["node", "dist/index.js"]

# Labels for metadata
LABEL maintainer="ALIAS Organization <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="MOSAIC Universal Framework Base Container"
LABEL org.opencontainers.image.title="MOSAIC Base"
LABEL org.opencontainers.image.description="Base container for MOSAIC lifecycle components"
LABEL org.opencontainers.image.vendor="ALIAS Organization"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.source="https://github.com/alias-organization/mosaic"
