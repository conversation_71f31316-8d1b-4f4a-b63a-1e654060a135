# MOSAIC Prometheus Configuration
# Comprehensive monitoring for MOSAIC Universal Framework

# Global Prometheus configuration
prometheus:
  prometheusSpec:
    # Resource allocation based on deployment scale
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "8Gi"
        cpu: "4000m"
    
    # Storage configuration
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: "fast-ssd"
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 100Gi
    
    # Retention policy
    retention: 30d
    retentionSize: 90GB
    
    # External labels for federation
    externalLabels:
      cluster: "mosaic-cluster"
      environment: "{{ .Values.environment }}"
      region: "{{ .Values.region }}"
    
    # Service monitor selector
    serviceMonitorSelectorNilUsesHelmValues: false
    serviceMonitorSelector:
      matchLabels:
        app.kubernetes.io/part-of: mosaic
    
    # Pod monitor selector
    podMonitorSelectorNilUsesHelmValues: false
    podMonitorSelector:
      matchLabels:
        app.kubernetes.io/part-of: mosaic
    
    # Rule selector
    ruleSelectorNilUsesHelmValues: false
    ruleSelector:
      matchLabels:
        app.kubernetes.io/part-of: mosaic
    
    # Additional scrape configs for MOSAIC lifecycles
    additionalScrapeConfigs:
      - job_name: 'mosaic-apex-lc'
        static_configs:
          - targets: ['apex-lc:3001']
        metrics_path: '/metrics'
        scrape_interval: 15s
        scrape_timeout: 10s
        
      - job_name: 'mosaic-prism-lc'
        static_configs:
          - targets: ['prism-lc:3002']
        metrics_path: '/metrics'
        scrape_interval: 15s
        
      - job_name: 'mosaic-aurora-lc'
        static_configs:
          - targets: ['aurora-lc:3003']
        metrics_path: '/metrics'
        scrape_interval: 15s
        
      - job_name: 'mosaic-pulse-lc'
        static_configs:
          - targets: ['pulse-lc:3004']
        metrics_path: '/metrics'
        scrape_interval: 15s
        
      - job_name: 'mosaic-infrastructure'
        static_configs:
          - targets: ['kafka:9092', 'redis:6379', 'postgres:5432']
        metrics_path: '/metrics'
        scrape_interval: 30s
        
      - job_name: 'mosaic-event-bus'
        static_configs:
          - targets: ['event-bus:8080']
        metrics_path: '/metrics'
        scrape_interval: 15s
        
      - job_name: 'mosaic-context-store'
        static_configs:
          - targets: ['context-store:8081']
        metrics_path: '/metrics'
        scrape_interval: 15s

# Grafana configuration
grafana:
  enabled: true
  
  # Admin credentials from Vault
  adminPassword: "{{ .Values.grafana.adminPassword }}"
  
  # Resource allocation
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
  
  # Persistence
  persistence:
    enabled: true
    size: 10Gi
    storageClassName: "fast-ssd"
  
  # Grafana configuration
  grafana.ini:
    server:
      root_url: "https://monitoring.mosaic.alias.dev"
      serve_from_sub_path: true
    
    security:
      admin_user: admin
      admin_password: "{{ .Values.grafana.adminPassword }}"
      secret_key: "{{ .Values.grafana.secretKey }}"
    
    auth:
      disable_login_form: false
      disable_signout_menu: false
    
    auth.anonymous:
      enabled: false
    
    analytics:
      reporting_enabled: false
      check_for_updates: false
    
    log:
      mode: console
      level: info
    
    alerting:
      enabled: true
      execute_alerts: true
    
    unified_alerting:
      enabled: true
  
  # Data sources
  datasources:
    datasources.yaml:
      apiVersion: 1
      datasources:
        - name: Prometheus
          type: prometheus
          url: http://prometheus-kube-prometheus-prometheus:9090
          access: proxy
          isDefault: true
          
        - name: Loki
          type: loki
          url: http://loki:3100
          access: proxy
          
        - name: Jaeger
          type: jaeger
          url: http://jaeger-query:16686
          access: proxy
  
  # Dashboard providers
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: 'mosaic-dashboards'
          orgId: 1
          folder: 'MOSAIC'
          type: file
          disableDeletion: false
          editable: true
          options:
            path: /var/lib/grafana/dashboards/mosaic
  
  # Pre-configured dashboards
  dashboards:
    mosaic:
      mosaic-overview:
        gnetId: 12900
        revision: 1
        datasource: Prometheus
      
      mosaic-lifecycles:
        url: https://raw.githubusercontent.com/alias-organization/mosaic/main/monitoring/dashboards/lifecycles.json
      
      mosaic-infrastructure:
        url: https://raw.githubusercontent.com/alias-organization/mosaic/main/monitoring/dashboards/infrastructure.json
      
      mosaic-performance:
        url: https://raw.githubusercontent.com/alias-organization/mosaic/main/monitoring/dashboards/performance.json

# Alertmanager configuration
alertmanager:
  alertmanagerSpec:
    # Resource allocation
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    
    # Storage
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: "fast-ssd"
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 10Gi
    
    # Alertmanager configuration
    config:
      global:
        smtp_smarthost: 'smtp.gmail.com:587'
        smtp_from: '<EMAIL>'
        smtp_auth_username: '{{ .Values.alertmanager.smtp.username }}'
        smtp_auth_password: '{{ .Values.alertmanager.smtp.password }}'
      
      route:
        group_by: ['alertname', 'cluster', 'service']
        group_wait: 10s
        group_interval: 10s
        repeat_interval: 1h
        receiver: 'web.hook'
        routes:
          - match:
              alertname: DeadMansSwitch
            receiver: 'null'
          - match:
              severity: critical
            receiver: 'critical-alerts'
          - match:
              severity: warning
            receiver: 'warning-alerts'
      
      receivers:
        - name: 'null'
        
        - name: 'web.hook'
          webhook_configs:
            - url: 'http://alertmanager-webhook:8080/webhook'
              send_resolved: true
        
        - name: 'critical-alerts'
          slack_configs:
            - api_url: '{{ .Values.alertmanager.slack.webhook }}'
              channel: '#mosaic-critical'
              title: 'MOSAIC Critical Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
              send_resolved: true
          
          email_configs:
            - to: '<EMAIL>'
              subject: 'MOSAIC Critical Alert: {{ .GroupLabels.alertname }}'
              body: |
                {{ range .Alerts }}
                Alert: {{ .Annotations.summary }}
                Description: {{ .Annotations.description }}
                Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
                {{ end }}
        
        - name: 'warning-alerts'
          slack_configs:
            - api_url: '{{ .Values.alertmanager.slack.webhook }}'
              channel: '#mosaic-alerts'
              title: 'MOSAIC Warning Alert'
              text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
              send_resolved: true

# Node Exporter configuration
nodeExporter:
  enabled: true
  
# Kube State Metrics configuration
kubeStateMetrics:
  enabled: true

# Prometheus Operator configuration
prometheusOperator:
  enabled: true
  
  # Resource allocation
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"

# Additional monitoring components
additionalPrometheusRules:
  - name: mosaic-custom-rules
    groups:
      - name: mosaic.lifecycle.rules
        rules:
          - alert: MosaicLifecycleDown
            expr: up{job=~"mosaic-.*-lc"} == 0
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "MOSAIC Lifecycle {{ $labels.job }} is down"
              description: "MOSAIC Lifecycle {{ $labels.job }} has been down for more than 1 minute"
          
          - alert: MosaicHighMemoryUsage
            expr: (container_memory_usage_bytes{pod=~"mosaic-.*"} / container_spec_memory_limit_bytes) > 0.8
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High memory usage in {{ $labels.pod }}"
              description: "Memory usage is above 80% for {{ $labels.pod }}"
          
          - alert: MosaicHighCPUUsage
            expr: rate(container_cpu_usage_seconds_total{pod=~"mosaic-.*"}[5m]) > 0.8
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage in {{ $labels.pod }}"
              description: "CPU usage is above 80% for {{ $labels.pod }}"
          
          - alert: MosaicEventBusLag
            expr: kafka_consumer_lag_sum{topic=~"mosaic-.*"} > 1000
            for: 2m
            labels:
              severity: warning
            annotations:
              summary: "High event bus lag for topic {{ $labels.topic }}"
              description: "Consumer lag is {{ $value }} messages for topic {{ $labels.topic }}"

# Service monitors for MOSAIC components
serviceMonitors:
  - name: mosaic-lifecycles
    selector:
      matchLabels:
        app.kubernetes.io/part-of: mosaic
        app.kubernetes.io/component: lifecycle
    endpoints:
      - port: metrics
        interval: 15s
        path: /metrics

# Better Stack integration
betterStack:
  enabled: true
  apiKey: "{{ .Values.betterStack.apiKey }}"
  
  # Uptime monitoring
  monitors:
    - name: "MOSAIC API Health"
      url: "https://api.mosaic.alias.dev/health"
      check_frequency: 60
      request_timeout: 30
      
    - name: "MOSAIC Dashboard"
      url: "https://dashboard.mosaic.alias.dev"
      check_frequency: 300
      request_timeout: 30
