# MOSAIC Networking Module - Multi-Cloud VPC/Network Setup

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "cloud_provider" {
  description = "Cloud provider (aws, gcp, azure)"
  type        = string
}

variable "deployment_scale" {
  description = "Deployment scale"
  type        = string
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = ""
}

variable "availability_zones" {
  description = "AWS availability zones"
  type        = list(string)
  default     = []
}

variable "gcp_project" {
  description = "GCP project ID"
  type        = string
  default     = ""
}

variable "gcp_zones" {
  description = "GCP zones"
  type        = list(string)
  default     = []
}

variable "azure_subscription_id" {
  description = "Azure subscription ID"
  type        = string
  default     = ""
}

variable "tags" {
  description = "Common tags"
  type        = map(string)
  default     = {}
}

locals {
  # CIDR blocks for different scales
  cidr_config = {
    solo = {
      vpc_cidr     = "10.0.0.0/16"
      public_cidrs = ["********/24", "********/24"]
      private_cidrs = ["*********/24", "*********/24"]
      db_cidrs     = ["*********/24", "*********/24"]
    }
    small = {
      vpc_cidr     = "10.0.0.0/16"
      public_cidrs = ["********/24", "********/24", "********/24"]
      private_cidrs = ["*********/24", "*********/24", "*********/24"]
      db_cidrs     = ["*********/24", "*********/24", "*********/24"]
    }
    enterprise = {
      vpc_cidr     = "10.0.0.0/16"
      public_cidrs = ["********/24", "********/24", "********/24", "********/24"]
      private_cidrs = ["*********/24", "*********/24", "*********/24", "*********/24"]
      db_cidrs     = ["*********/24", "*********/24", "*********/24", "*********/24"]
    }
  }
  
  current_config = local.cidr_config[var.deployment_scale]
}

# AWS Networking
resource "aws_vpc" "main" {
  count = var.cloud_provider == "aws" ? 1 : 0
  
  cidr_block           = local.current_config.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = merge(var.tags, {
    Name = "mosaic-vpc-${var.environment}"
    Type = "VPC"
  })
}

resource "aws_internet_gateway" "main" {
  count = var.cloud_provider == "aws" ? 1 : 0
  
  vpc_id = aws_vpc.main[0].id
  
  tags = merge(var.tags, {
    Name = "mosaic-igw-${var.environment}"
    Type = "InternetGateway"
  })
}

resource "aws_subnet" "public" {
  count = var.cloud_provider == "aws" ? length(local.current_config.public_cidrs) : 0
  
  vpc_id                  = aws_vpc.main[0].id
  cidr_block              = local.current_config.public_cidrs[count.index]
  availability_zone       = var.availability_zones[count.index % length(var.availability_zones)]
  map_public_ip_on_launch = true
  
  tags = merge(var.tags, {
    Name = "mosaic-public-subnet-${count.index + 1}-${var.environment}"
    Type = "PublicSubnet"
    Tier = "Public"
  })
}

resource "aws_subnet" "private" {
  count = var.cloud_provider == "aws" ? length(local.current_config.private_cidrs) : 0
  
  vpc_id            = aws_vpc.main[0].id
  cidr_block        = local.current_config.private_cidrs[count.index]
  availability_zone = var.availability_zones[count.index % length(var.availability_zones)]
  
  tags = merge(var.tags, {
    Name = "mosaic-private-subnet-${count.index + 1}-${var.environment}"
    Type = "PrivateSubnet"
    Tier = "Private"
  })
}

resource "aws_subnet" "database" {
  count = var.cloud_provider == "aws" ? length(local.current_config.db_cidrs) : 0
  
  vpc_id            = aws_vpc.main[0].id
  cidr_block        = local.current_config.db_cidrs[count.index]
  availability_zone = var.availability_zones[count.index % length(var.availability_zones)]
  
  tags = merge(var.tags, {
    Name = "mosaic-db-subnet-${count.index + 1}-${var.environment}"
    Type = "DatabaseSubnet"
    Tier = "Database"
  })
}

resource "aws_eip" "nat" {
  count = var.cloud_provider == "aws" ? length(aws_subnet.public) : 0
  
  domain = "vpc"
  
  tags = merge(var.tags, {
    Name = "mosaic-nat-eip-${count.index + 1}-${var.environment}"
    Type = "ElasticIP"
  })
  
  depends_on = [aws_internet_gateway.main]
}

resource "aws_nat_gateway" "main" {
  count = var.cloud_provider == "aws" ? length(aws_subnet.public) : 0
  
  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = aws_subnet.public[count.index].id
  
  tags = merge(var.tags, {
    Name = "mosaic-nat-gateway-${count.index + 1}-${var.environment}"
    Type = "NATGateway"
  })
  
  depends_on = [aws_internet_gateway.main]
}

resource "aws_route_table" "public" {
  count = var.cloud_provider == "aws" ? 1 : 0
  
  vpc_id = aws_vpc.main[0].id
  
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main[0].id
  }
  
  tags = merge(var.tags, {
    Name = "mosaic-public-rt-${var.environment}"
    Type = "RouteTable"
    Tier = "Public"
  })
}

resource "aws_route_table" "private" {
  count = var.cloud_provider == "aws" ? length(aws_nat_gateway.main) : 0
  
  vpc_id = aws_vpc.main[0].id
  
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.main[count.index].id
  }
  
  tags = merge(var.tags, {
    Name = "mosaic-private-rt-${count.index + 1}-${var.environment}"
    Type = "RouteTable"
    Tier = "Private"
  })
}

resource "aws_route_table_association" "public" {
  count = var.cloud_provider == "aws" ? length(aws_subnet.public) : 0
  
  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public[0].id
}

resource "aws_route_table_association" "private" {
  count = var.cloud_provider == "aws" ? length(aws_subnet.private) : 0
  
  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index % length(aws_route_table.private)].id
}

resource "aws_route_table_association" "database" {
  count = var.cloud_provider == "aws" ? length(aws_subnet.database) : 0
  
  subnet_id      = aws_subnet.database[count.index].id
  route_table_id = aws_route_table.private[count.index % length(aws_route_table.private)].id
}

# VPC Endpoints for AWS services
resource "aws_vpc_endpoint" "s3" {
  count = var.cloud_provider == "aws" ? 1 : 0
  
  vpc_id       = aws_vpc.main[0].id
  service_name = "com.amazonaws.${var.aws_region}.s3"
  
  tags = merge(var.tags, {
    Name = "mosaic-s3-endpoint-${var.environment}"
    Type = "VPCEndpoint"
  })
}

resource "aws_vpc_endpoint" "ecr_dkr" {
  count = var.cloud_provider == "aws" ? 1 : 0
  
  vpc_id              = aws_vpc.main[0].id
  service_name        = "com.amazonaws.${var.aws_region}.ecr.dkr"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = aws_subnet.private[*].id
  security_group_ids  = [aws_security_group.vpc_endpoints[0].id]
  
  tags = merge(var.tags, {
    Name = "mosaic-ecr-dkr-endpoint-${var.environment}"
    Type = "VPCEndpoint"
  })
}

resource "aws_security_group" "vpc_endpoints" {
  count = var.cloud_provider == "aws" ? 1 : 0
  
  name_prefix = "mosaic-vpc-endpoints-${var.environment}"
  vpc_id      = aws_vpc.main[0].id
  
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [local.current_config.vpc_cidr]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = merge(var.tags, {
    Name = "mosaic-vpc-endpoints-sg-${var.environment}"
    Type = "SecurityGroup"
  })
}

# Outputs
output "vpc_id" {
  description = "VPC ID"
  value = var.cloud_provider == "aws" ? aws_vpc.main[0].id : ""
}

output "vpc_cidr" {
  description = "VPC CIDR block"
  value = local.current_config.vpc_cidr
}

output "public_subnet_ids" {
  description = "Public subnet IDs"
  value = var.cloud_provider == "aws" ? aws_subnet.public[*].id : []
}

output "private_subnet_ids" {
  description = "Private subnet IDs"
  value = var.cloud_provider == "aws" ? aws_subnet.private[*].id : []
}

output "database_subnet_ids" {
  description = "Database subnet IDs"
  value = var.cloud_provider == "aws" ? aws_subnet.database[*].id : []
}

output "nat_gateway_ids" {
  description = "NAT Gateway IDs"
  value = var.cloud_provider == "aws" ? aws_nat_gateway.main[*].id : []
}

output "internet_gateway_id" {
  description = "Internet Gateway ID"
  value = var.cloud_provider == "aws" ? aws_internet_gateway.main[0].id : ""
}
