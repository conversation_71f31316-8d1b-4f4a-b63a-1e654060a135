# MOSAIC Infrastructure as Code - Main Terraform Configuration
# Supports multi-cloud deployment (AWS, GCP, Azure) with High Availability

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
    vault = {
      source  = "hashicorp/vault"
      version = "~> 3.0"
    }
  }

  backend "s3" {
    bucket         = var.terraform_state_bucket
    key            = "mosaic/terraform.tfstate"
    region         = var.aws_region
    encrypt        = true
    dynamodb_table = var.terraform_lock_table
  }
}

# Variables
variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "cloud_provider" {
  description = "Primary cloud provider (aws, gcp, azure)"
  type        = string
  default     = "aws"
}

variable "deployment_scale" {
  description = "Deployment scale (solo, small, enterprise)"
  type        = string
  default     = "small"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "gcp_project" {
  description = "GCP project ID"
  type        = string
  default     = ""
}

variable "azure_subscription_id" {
  description = "Azure subscription ID"
  type        = string
  default     = ""
}

variable "terraform_state_bucket" {
  description = "S3 bucket for Terraform state"
  type        = string
}

variable "terraform_lock_table" {
  description = "DynamoDB table for Terraform state locking"
  type        = string
}

variable "domain_name" {
  description = "Domain name for MOSAIC deployment"
  type        = string
}

variable "enable_multi_region" {
  description = "Enable multi-region deployment for HA"
  type        = bool
  default     = false
}

variable "enable_disaster_recovery" {
  description = "Enable disaster recovery setup"
  type        = bool
  default     = false
}

# Local values for scaling configuration
locals {
  scale_config = {
    solo = {
      kafka_nodes        = 1
      redis_nodes        = 1
      postgres_replicas  = 1
      k8s_node_count     = 2
      instance_type      = "t3.medium"
      storage_size       = "100Gi"
    }
    small = {
      kafka_nodes        = 3
      redis_nodes        = 3
      postgres_replicas  = 2
      k8s_node_count     = 3
      instance_type      = "t3.large"
      storage_size       = "500Gi"
    }
    enterprise = {
      kafka_nodes        = 9
      redis_nodes        = 9
      postgres_replicas  = 3
      k8s_node_count     = 6
      instance_type      = "t3.xlarge"
      storage_size       = "2Ti"
    }
  }

  current_scale = local.scale_config[var.deployment_scale]
  
  common_tags = {
    Environment = var.environment
    Project     = "MOSAIC"
    ManagedBy   = "Terraform"
    Scale       = var.deployment_scale
  }
}

# Data sources
data "aws_availability_zones" "available" {
  count = var.cloud_provider == "aws" ? 1 : 0
  state = "available"
}

data "google_compute_zones" "available" {
  count = var.cloud_provider == "gcp" ? 1 : 0
}

# Networking Module
module "networking" {
  source = "./modules/networking"
  
  environment      = var.environment
  cloud_provider   = var.cloud_provider
  deployment_scale = var.deployment_scale
  
  # AWS specific
  aws_region           = var.aws_region
  availability_zones   = var.cloud_provider == "aws" ? data.aws_availability_zones.available[0].names : []
  
  # GCP specific
  gcp_project = var.gcp_project
  gcp_zones   = var.cloud_provider == "gcp" ? data.google_compute_zones.available[0].names : []
  
  # Azure specific
  azure_subscription_id = var.azure_subscription_id
  
  tags = local.common_tags
}

# Security Module
module "security" {
  source = "./modules/security"
  
  environment      = var.environment
  cloud_provider   = var.cloud_provider
  deployment_scale = var.deployment_scale
  
  vpc_id     = module.networking.vpc_id
  subnet_ids = module.networking.private_subnet_ids
  
  tags = local.common_tags
}

# Kubernetes Cluster Module
module "kubernetes" {
  source = "./modules/kubernetes"
  
  environment      = var.environment
  cloud_provider   = var.cloud_provider
  deployment_scale = var.deployment_scale
  
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security.k8s_security_group_id]
  
  node_count    = local.current_scale.k8s_node_count
  instance_type = local.current_scale.instance_type
  
  tags = local.common_tags
}

# Storage Module (Kafka, Redis, PostgreSQL)
module "storage" {
  source = "./modules/storage"
  
  environment      = var.environment
  cloud_provider   = var.cloud_provider
  deployment_scale = var.deployment_scale
  
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security.storage_security_group_id]
  
  kafka_nodes       = local.current_scale.kafka_nodes
  redis_nodes       = local.current_scale.redis_nodes
  postgres_replicas = local.current_scale.postgres_replicas
  storage_size      = local.current_scale.storage_size
  
  tags = local.common_tags
}

# Load Balancer Module
module "load_balancer" {
  source = "./modules/load_balancer"
  
  environment      = var.environment
  cloud_provider   = var.cloud_provider
  deployment_scale = var.deployment_scale
  
  vpc_id     = module.networking.vpc_id
  subnet_ids = module.networking.public_subnet_ids
  
  domain_name = var.domain_name
  
  tags = local.common_tags
}

# Monitoring Module
module "monitoring" {
  source = "./modules/monitoring"
  
  environment      = var.environment
  cloud_provider   = var.cloud_provider
  deployment_scale = var.deployment_scale
  
  vpc_id     = module.networking.vpc_id
  subnet_ids = module.networking.private_subnet_ids
  
  k8s_cluster_name = module.kubernetes.cluster_name
  
  tags = local.common_tags
}

# HashiCorp Vault Module
module "vault" {
  source = "./modules/vault"
  
  environment      = var.environment
  cloud_provider   = var.cloud_provider
  deployment_scale = var.deployment_scale
  
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security.vault_security_group_id]
  
  tags = local.common_tags
}

# Uncloud CLI Integration Module
module "uncloud" {
  source = "./modules/uncloud"
  
  environment      = var.environment
  deployment_scale = var.deployment_scale
  
  k8s_cluster_name   = module.kubernetes.cluster_name
  k8s_cluster_config = module.kubernetes.cluster_config
  
  enable_multi_region      = var.enable_multi_region
  enable_disaster_recovery = var.enable_disaster_recovery
  
  tags = local.common_tags
}

# Outputs
output "vpc_id" {
  description = "VPC ID"
  value       = module.networking.vpc_id
}

output "k8s_cluster_name" {
  description = "Kubernetes cluster name"
  value       = module.kubernetes.cluster_name
}

output "k8s_cluster_endpoint" {
  description = "Kubernetes cluster endpoint"
  value       = module.kubernetes.cluster_endpoint
  sensitive   = true
}

output "load_balancer_dns" {
  description = "Load balancer DNS name"
  value       = module.load_balancer.dns_name
}

output "vault_endpoint" {
  description = "HashiCorp Vault endpoint"
  value       = module.vault.endpoint
  sensitive   = true
}

output "monitoring_endpoints" {
  description = "Monitoring service endpoints"
  value = {
    prometheus = module.monitoring.prometheus_endpoint
    grafana    = module.monitoring.grafana_endpoint
    alertmanager = module.monitoring.alertmanager_endpoint
  }
  sensitive = true
}

output "storage_endpoints" {
  description = "Storage service endpoints"
  value = {
    kafka_brokers    = module.storage.kafka_brokers
    redis_endpoints  = module.storage.redis_endpoints
    postgres_endpoint = module.storage.postgres_endpoint
  }
  sensitive = true
}

output "uncloud_config" {
  description = "Uncloud CLI configuration"
  value       = module.uncloud.config
  sensitive   = true
}
