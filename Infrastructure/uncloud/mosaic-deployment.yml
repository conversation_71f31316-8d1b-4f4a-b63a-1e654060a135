# MOSAIC Uncloud Deployment Configuration
# Multi-server High Availability deployment strategy for MOSAIC Universal Framework

apiVersion: uncloud.io/v1
kind: MosaicDeployment
metadata:
  name: mosaic-ha-deployment
  namespace: mosaic
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
    deployment.uncloud.io/type: high-availability
  annotations:
    description: "MOSAIC Universal Framework High Availability Deployment"
    contact: "<EMAIL>"
    deployment.uncloud.io/strategy: "multi-region"
    deployment.uncloud.io/scale: "{{ .Values.deployment_scale }}"

spec:
  # Deployment configuration
  deployment:
    strategy: RollingUpdate
    maxUnavailable: 25%
    maxSurge: 25%
    
  # High Availability configuration
  highAvailability:
    enabled: true
    minReplicas: 3
    maxReplicas: 100
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
    
    # Multi-region deployment
    regions:
      - name: primary
        provider: "{{ .Values.cloud_provider }}"
        region: "{{ .Values.primary_region }}"
        zones: 3
        weight: 70
        
      - name: secondary
        provider: "{{ .Values.cloud_provider }}"
        region: "{{ .Values.secondary_region }}"
        zones: 2
        weight: 30
    
    # Disaster recovery
    disasterRecovery:
      enabled: true
      backupRegion: "{{ .Values.backup_region }}"
      rpoMinutes: 15  # Recovery Point Objective
      rtoMinutes: 30  # Recovery Time Objective
      
  # Infrastructure components
  infrastructure:
    # Event Bus (Kafka) cluster
    eventBus:
      type: kafka
      replicas: "{{ .Values.scale_config.kafka_nodes }}"
      resources:
        requests:
          cpu: "1"
          memory: "2Gi"
          storage: "100Gi"
        limits:
          cpu: "4"
          memory: "8Gi"
      
      # High availability configuration
      highAvailability:
        replicationFactor: 3
        minInSyncReplicas: 2
        uncleanLeaderElection: false
        
      # Cross-region replication
      crossRegionReplication:
        enabled: true
        replicationFactor: 2
        
    # Context Store (Redis + PostgreSQL)
    contextStore:
      redis:
        type: cluster
        replicas: "{{ .Values.scale_config.redis_nodes }}"
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        
        # Redis cluster configuration
        cluster:
          enabled: true
          replicas: 3
          masterNodes: 3
          
      postgresql:
        type: cluster
        replicas: "{{ .Values.scale_config.postgres_replicas }}"
        resources:
          requests:
            cpu: "1"
            memory: "2Gi"
            storage: "200Gi"
          limits:
            cpu: "4"
            memory: "8Gi"
        
        # PostgreSQL HA configuration
        highAvailability:
          enabled: true
          synchronousReplication: true
          maxConnections: 200
          
  # MOSAIC Lifecycles deployment
  lifecycles:
    # APEX-LC: Autonomous Persona-Enhanced eXecution
    apex-lc:
      replicas: "{{ .Values.scale_config.lifecycle_replicas }}"
      image: "mosaic/apex-lc:{{ .Values.image_tag }}"
      port: 3001
      resources:
        requests:
          cpu: "{{ .Values.scale_config.resource_requests.cpu }}"
          memory: "{{ .Values.scale_config.resource_requests.memory }}"
        limits:
          cpu: "{{ .Values.scale_config.resource_limits.cpu }}"
          memory: "{{ .Values.scale_config.resource_limits.memory }}"
      
      # Health checks
      healthCheck:
        httpGet:
          path: /health
          port: 3001
        initialDelaySeconds: 30
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 3
        
      # Auto-scaling
      autoscaling:
        enabled: true
        minReplicas: 2
        maxReplicas: 20
        targetCPUUtilizationPercentage: 70
        
    # PRISM-LC: Pattern Recognition and Insight Synthesis
    prism-lc:
      replicas: "{{ .Values.scale_config.lifecycle_replicas }}"
      image: "mosaic/prism-lc:{{ .Values.image_tag }}"
      port: 3002
      resources:
        requests:
          cpu: "{{ .Values.scale_config.resource_requests.cpu }}"
          memory: "{{ .Values.scale_config.resource_requests.memory }}"
        limits:
          cpu: "{{ .Values.scale_config.resource_limits.cpu }}"
          memory: "{{ .Values.scale_config.resource_limits.memory }}"
      
      # Persistent storage for knowledge base
      storage:
        enabled: true
        size: "50Gi"
        storageClass: "fast-ssd"
        
    # AURORA-LC: Automated User Relationship and Optimization
    aurora-lc:
      replicas: "{{ .Values.scale_config.lifecycle_replicas }}"
      image: "mosaic/aurora-lc:{{ .Values.image_tag }}"
      port: 3003
      resources:
        requests:
          cpu: "{{ .Values.scale_config.resource_requests.cpu }}"
          memory: "{{ .Values.scale_config.resource_requests.memory }}"
        limits:
          cpu: "{{ .Values.scale_config.resource_limits.cpu }}"
          memory: "{{ .Values.scale_config.resource_limits.memory }}"
          
    # PULSE-LC: Performance Understanding and Lifecycle Synchronization
    pulse-lc:
      replicas: "{{ .Values.scale_config.lifecycle_replicas }}"
      image: "mosaic/pulse-lc:{{ .Values.image_tag }}"
      port: 3004
      resources:
        requests:
          cpu: "{{ .Values.scale_config.resource_requests.cpu }}"
          memory: "{{ .Values.scale_config.resource_requests.memory }}"
        limits:
          cpu: "{{ .Values.scale_config.resource_limits.cpu }}"
          memory: "{{ .Values.scale_config.resource_limits.memory }}"
      
      # Special configuration for meta-orchestration
      specialConfig:
        orchestrationMode: true
        globalView: true
        
  # Networking configuration
  networking:
    # Load balancer configuration
    loadBalancer:
      type: "{{ .Values.load_balancer_type }}"
      annotations:
        service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
        service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
        
    # Ingress configuration
    ingress:
      enabled: true
      className: "nginx"
      annotations:
        nginx.ingress.kubernetes.io/ssl-redirect: "true"
        nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
        cert-manager.io/cluster-issuer: "letsencrypt-prod"
        
      hosts:
        - host: "api.mosaic.alias.dev"
          paths:
            - path: /
              pathType: Prefix
              service: mosaic-gateway
              
        - host: "dashboard.mosaic.alias.dev"
          paths:
            - path: /
              pathType: Prefix
              service: mosaic-dashboard
              
      tls:
        - secretName: mosaic-tls
          hosts:
            - "api.mosaic.alias.dev"
            - "dashboard.mosaic.alias.dev"
            
    # Service mesh configuration
    serviceMesh:
      enabled: true
      type: istio
      mtls: true
      
  # Security configuration
  security:
    # Pod security standards
    podSecurityStandards:
      enforce: "restricted"
      audit: "restricted"
      warn: "restricted"
      
    # Network policies
    networkPolicies:
      enabled: true
      defaultDeny: true
      
    # Secret management
    secretManagement:
      provider: vault
      vaultAddress: "{{ .Values.vault_address }}"
      authMethod: kubernetes
      
    # Image security
    imageSecurity:
      scanEnabled: true
      allowedRegistries:
        - "mosaic-registry.alias.dev"
        - "docker.io"
        - "gcr.io"
        
  # Monitoring and observability
  monitoring:
    # Prometheus monitoring
    prometheus:
      enabled: true
      scrapeInterval: "15s"
      retention: "30d"
      
    # Grafana dashboards
    grafana:
      enabled: true
      dashboards:
        - mosaic-overview
        - mosaic-lifecycles
        - mosaic-infrastructure
        - mosaic-performance
        
    # Distributed tracing
    tracing:
      enabled: true
      provider: jaeger
      samplingRate: 0.1
      
    # Log aggregation
    logging:
      enabled: true
      provider: loki
      retention: "30d"
      
    # Better Stack integration
    betterStack:
      enabled: true
      monitors:
        - name: "MOSAIC API Health"
          url: "https://api.mosaic.alias.dev/health"
          frequency: 60
          
  # Backup and disaster recovery
  backup:
    enabled: true
    schedule: "0 2 * * *"  # Daily at 2 AM
    retention: "30d"
    
    # Storage backends
    storage:
      - type: s3
        bucket: "{{ .Values.backup_bucket }}"
        region: "{{ .Values.backup_region }}"
        
    # Components to backup
    components:
      - postgresql
      - redis
      - kafka
      - persistent-volumes
      
  # Scaling configuration based on deployment scale
  scaling:
    solo:
      replicas: 1
      resources:
        requests:
          cpu: "250m"
          memory: "512Mi"
        limits:
          cpu: "500m"
          memory: "1Gi"
          
    small:
      replicas: 2
      resources:
        requests:
          cpu: "1"
          memory: "2Gi"
        limits:
          cpu: "2"
          memory: "4Gi"
          
    enterprise:
      replicas: 5
      resources:
        requests:
          cpu: "4"
          memory: "8Gi"
        limits:
          cpu: "8"
          memory: "16Gi"
          
  # Environment-specific configuration
  environments:
    dev:
      replicas: 1
      resources: minimal
      monitoring: basic
      
    staging:
      replicas: 2
      resources: standard
      monitoring: full
      
    prod:
      replicas: 3
      resources: optimized
      monitoring: comprehensive
      backup: enabled
