#!/bin/bash

# MOSAIC Uncloud CLI Setup Script
# Configures uncloud CLI for MOSAIC High Availability deployment

set -euo pipefail

# Configuration
UNCLOUD_VERSION="${UNCLOUD_VERSION:-latest}"
MOSAIC_NAMESPACE="${MOSAIC_NAMESPACE:-mosaic}"
DEPLOYMENT_SCALE="${DEPLOYMENT_SCALE:-small}"
ENVIRONMENT="${ENVIRONMENT:-dev}"
CLOUD_PROVIDER="${CLOUD_PROVIDER:-aws}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install it first."
        exit 1
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed. Please install it first."
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed. Please install it first."
        exit 1
    fi
    
    # Check Kubernetes cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Install uncloud CLI
install_uncloud_cli() {
    log_info "Installing uncloud CLI..."
    
    # Download and install uncloud CLI
    case "$(uname -s)" in
        Linux*)
            PLATFORM="linux"
            ;;
        Darwin*)
            PLATFORM="darwin"
            ;;
        *)
            log_error "Unsupported platform: $(uname -s)"
            exit 1
            ;;
    esac
    
    ARCH="$(uname -m)"
    case "$ARCH" in
        x86_64)
            ARCH="amd64"
            ;;
        arm64|aarch64)
            ARCH="arm64"
            ;;
        *)
            log_error "Unsupported architecture: $ARCH"
            exit 1
            ;;
    esac
    
    # Download uncloud CLI
    DOWNLOAD_URL="https://github.com/uncloud-io/cli/releases/download/${UNCLOUD_VERSION}/uncloud-${PLATFORM}-${ARCH}"
    
    log_info "Downloading uncloud CLI from $DOWNLOAD_URL"
    curl -L "$DOWNLOAD_URL" -o /tmp/uncloud
    chmod +x /tmp/uncloud
    
    # Install to system path
    sudo mv /tmp/uncloud /usr/local/bin/uncloud
    
    # Verify installation
    if uncloud version &> /dev/null; then
        log_success "Uncloud CLI installed successfully"
        uncloud version
    else
        log_error "Failed to install uncloud CLI"
        exit 1
    fi
}

# Configure uncloud for MOSAIC
configure_uncloud() {
    log_info "Configuring uncloud for MOSAIC deployment..."
    
    # Initialize uncloud configuration
    uncloud init --project mosaic --environment "$ENVIRONMENT"
    
    # Set cloud provider
    uncloud config set cloud-provider "$CLOUD_PROVIDER"
    
    # Set deployment scale
    uncloud config set deployment-scale "$DEPLOYMENT_SCALE"
    
    # Configure Kubernetes context
    CURRENT_CONTEXT=$(kubectl config current-context)
    uncloud config set kubernetes-context "$CURRENT_CONTEXT"
    
    # Configure namespace
    uncloud config set namespace "$MOSAIC_NAMESPACE"
    
    # Set registry configuration
    uncloud config set registry "mosaic-registry.alias.dev"
    
    # Configure monitoring
    uncloud config set monitoring.enabled true
    uncloud config set monitoring.prometheus.enabled true
    uncloud config set monitoring.grafana.enabled true
    
    # Configure backup
    uncloud config set backup.enabled true
    uncloud config set backup.schedule "0 2 * * *"
    uncloud config set backup.retention "30d"
    
    log_success "Uncloud configuration completed"
}

# Create MOSAIC deployment configuration
create_deployment_config() {
    log_info "Creating MOSAIC deployment configuration..."
    
    # Create deployment directory
    mkdir -p ~/.uncloud/deployments/mosaic
    
    # Copy deployment configuration
    cp mosaic-deployment.yml ~/.uncloud/deployments/mosaic/
    
    # Create environment-specific configurations
    for env in dev staging prod; do
        cat > ~/.uncloud/deployments/mosaic/values-${env}.yml << EOF
# MOSAIC ${env} environment configuration
environment: ${env}
deployment_scale: ${DEPLOYMENT_SCALE}
cloud_provider: ${CLOUD_PROVIDER}

# Image configuration
image_tag: ${env}
image_pull_policy: Always

# Scaling configuration
scale_config:
  kafka_nodes: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo 1 || [ "$DEPLOYMENT_SCALE" = "small" ] && echo 3 || echo 9)
  redis_nodes: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo 1 || [ "$DEPLOYMENT_SCALE" = "small" ] && echo 3 || echo 9)
  postgres_replicas: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo 1 || [ "$DEPLOYMENT_SCALE" = "small" ] && echo 2 || echo 3)
  lifecycle_replicas: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo 1 || [ "$DEPLOYMENT_SCALE" = "small" ] && echo 2 || echo 5)
  
  resource_requests:
    cpu: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo "250m" || [ "$DEPLOYMENT_SCALE" = "small" ] && echo "1" || echo "4")
    memory: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo "512Mi" || [ "$DEPLOYMENT_SCALE" = "small" ] && echo "2Gi" || echo "8Gi")
  
  resource_limits:
    cpu: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo "500m" || [ "$DEPLOYMENT_SCALE" = "small" ] && echo "2" || echo "8")
    memory: $([ "$DEPLOYMENT_SCALE" = "solo" ] && echo "1Gi" || [ "$DEPLOYMENT_SCALE" = "small" ] && echo "4Gi" || echo "16Gi")

# Network configuration
load_balancer_type: $([ "$CLOUD_PROVIDER" = "aws" ] && echo "nlb" || [ "$CLOUD_PROVIDER" = "gcp" ] && echo "gce" || echo "azure")

# Region configuration
primary_region: $([ "$CLOUD_PROVIDER" = "aws" ] && echo "us-west-2" || [ "$CLOUD_PROVIDER" = "gcp" ] && echo "us-central1" || echo "eastus")
secondary_region: $([ "$CLOUD_PROVIDER" = "aws" ] && echo "us-east-1" || [ "$CLOUD_PROVIDER" = "gcp" ] && echo "us-east1" || echo "westus2")
backup_region: $([ "$CLOUD_PROVIDER" = "aws" ] && echo "eu-west-1" || [ "$CLOUD_PROVIDER" = "gcp" ] && echo "europe-west1" || echo "northeurope")

# Storage configuration
backup_bucket: mosaic-backups-${env}-$(date +%s)

# Vault configuration
vault_address: https://vault.mosaic.alias.dev
EOF
    done
    
    log_success "Deployment configuration created"
}

# Setup monitoring integration
setup_monitoring() {
    log_info "Setting up monitoring integration..."
    
    # Configure Better Stack integration
    if [ -n "${BETTER_STACK_TOKEN:-}" ]; then
        uncloud config set monitoring.better-stack.token "$BETTER_STACK_TOKEN"
        uncloud config set monitoring.better-stack.enabled true
    fi
    
    # Configure Sentry integration
    if [ -n "${SENTRY_DSN:-}" ]; then
        uncloud config set monitoring.sentry.dsn "$SENTRY_DSN"
        uncloud config set monitoring.sentry.enabled true
    fi
    
    # Configure custom dashboards
    uncloud config set monitoring.dashboards.mosaic-overview true
    uncloud config set monitoring.dashboards.mosaic-lifecycles true
    uncloud config set monitoring.dashboards.mosaic-infrastructure true
    
    log_success "Monitoring integration configured"
}

# Setup backup and disaster recovery
setup_backup() {
    log_info "Setting up backup and disaster recovery..."
    
    # Configure backup storage
    case "$CLOUD_PROVIDER" in
        aws)
            uncloud config set backup.storage.type s3
            uncloud config set backup.storage.bucket "mosaic-backups-${ENVIRONMENT}"
            uncloud config set backup.storage.region "${AWS_REGION:-us-west-2}"
            ;;
        gcp)
            uncloud config set backup.storage.type gcs
            uncloud config set backup.storage.bucket "mosaic-backups-${ENVIRONMENT}"
            uncloud config set backup.storage.region "${GCP_REGION:-us-central1}"
            ;;
        azure)
            uncloud config set backup.storage.type azure
            uncloud config set backup.storage.container "mosaic-backups-${ENVIRONMENT}"
            uncloud config set backup.storage.region "${AZURE_REGION:-eastus}"
            ;;
    esac
    
    # Configure disaster recovery
    uncloud config set disaster-recovery.enabled true
    uncloud config set disaster-recovery.rpo-minutes 15
    uncloud config set disaster-recovery.rto-minutes 30
    
    log_success "Backup and disaster recovery configured"
}

# Validate deployment configuration
validate_deployment() {
    log_info "Validating deployment configuration..."
    
    # Validate deployment file
    uncloud validate deployment ~/.uncloud/deployments/mosaic/mosaic-deployment.yml
    
    # Check cluster resources
    uncloud check resources --deployment mosaic --environment "$ENVIRONMENT"
    
    # Validate network policies
    uncloud validate network-policies --namespace "$MOSAIC_NAMESPACE"
    
    # Check security policies
    uncloud validate security --deployment mosaic
    
    log_success "Deployment configuration validated"
}

# Create deployment scripts
create_deployment_scripts() {
    log_info "Creating deployment scripts..."
    
    # Create deploy script
    cat > deploy-mosaic.sh << 'EOF'
#!/bin/bash
# MOSAIC Deployment Script using Uncloud CLI

set -euo pipefail

ENVIRONMENT="${1:-dev}"
DEPLOYMENT_SCALE="${2:-small}"

echo "🚀 Deploying MOSAIC to $ENVIRONMENT environment with $DEPLOYMENT_SCALE scale..."

# Deploy infrastructure
uncloud deploy infrastructure \
    --deployment mosaic \
    --environment "$ENVIRONMENT" \
    --values-file "values-${ENVIRONMENT}.yml" \
    --wait

# Deploy applications
uncloud deploy applications \
    --deployment mosaic \
    --environment "$ENVIRONMENT" \
    --values-file "values-${ENVIRONMENT}.yml" \
    --wait

# Run health checks
uncloud health-check \
    --deployment mosaic \
    --environment "$ENVIRONMENT" \
    --timeout 300

echo "✅ MOSAIC deployment completed successfully!"
EOF
    
    # Create rollback script
    cat > rollback-mosaic.sh << 'EOF'
#!/bin/bash
# MOSAIC Rollback Script using Uncloud CLI

set -euo pipefail

ENVIRONMENT="${1:-dev}"
REVISION="${2:-previous}"

echo "🔄 Rolling back MOSAIC in $ENVIRONMENT environment to revision $REVISION..."

# Rollback deployment
uncloud rollback \
    --deployment mosaic \
    --environment "$ENVIRONMENT" \
    --revision "$REVISION" \
    --wait

# Verify rollback
uncloud health-check \
    --deployment mosaic \
    --environment "$ENVIRONMENT" \
    --timeout 300

echo "✅ MOSAIC rollback completed successfully!"
EOF
    
    # Create scale script
    cat > scale-mosaic.sh << 'EOF'
#!/bin/bash
# MOSAIC Scaling Script using Uncloud CLI

set -euo pipefail

ENVIRONMENT="${1:-dev}"
SCALE="${2:-small}"

echo "📈 Scaling MOSAIC in $ENVIRONMENT environment to $SCALE scale..."

# Update deployment scale
uncloud scale \
    --deployment mosaic \
    --environment "$ENVIRONMENT" \
    --scale "$SCALE" \
    --wait

# Verify scaling
uncloud status \
    --deployment mosaic \
    --environment "$ENVIRONMENT"

echo "✅ MOSAIC scaling completed successfully!"
EOF
    
    # Make scripts executable
    chmod +x deploy-mosaic.sh rollback-mosaic.sh scale-mosaic.sh
    
    log_success "Deployment scripts created"
}

# Main execution
main() {
    log_info "Starting MOSAIC Uncloud CLI setup..."
    
    check_prerequisites
    install_uncloud_cli
    configure_uncloud
    create_deployment_config
    setup_monitoring
    setup_backup
    validate_deployment
    create_deployment_scripts
    
    log_success "MOSAIC Uncloud CLI setup completed successfully!"
    log_info "Next steps:"
    log_info "1. Review deployment configuration in ~/.uncloud/deployments/mosaic/"
    log_info "2. Deploy MOSAIC: ./deploy-mosaic.sh $ENVIRONMENT $DEPLOYMENT_SCALE"
    log_info "3. Monitor deployment: uncloud status --deployment mosaic"
    log_info "4. Access dashboards: uncloud dashboard --deployment mosaic"
}

# Run main function
main "$@"
