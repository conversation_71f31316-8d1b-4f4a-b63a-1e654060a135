---
# MOSAIC Kubernetes Namespace and RBAC Configuration
apiVersion: v1
kind: Namespace
metadata:
  name: mosaic
  labels:
    name: mosaic
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "MOSAIC Universal Framework namespace"
    contact: "<EMAIL>"

---
# Service Account for MOSAIC components
apiVersion: v1
kind: ServiceAccount
metadata:
  name: mosaic-service-account
  namespace: mosaic
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
  annotations:
    description: "Service account for MOSAIC lifecycle components"

---
# Cluster Role for MOSAIC
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: mosaic-cluster-role
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
rules:
  # Pod management
  - apiGroups: [""]
    resources: ["pods", "pods/log", "pods/status"]
    verbs: ["get", "list", "watch"]
  
  # Service discovery
  - apiGroups: [""]
    resources: ["services", "endpoints"]
    verbs: ["get", "list", "watch"]
  
  # ConfigMap and Secret access
  - apiGroups: [""]
    resources: ["configmaps", "secrets"]
    verbs: ["get", "list", "watch"]
  
  # Node information
  - apiGroups: [""]
    resources: ["nodes", "nodes/metrics"]
    verbs: ["get", "list", "watch"]
  
  # Deployment management
  - apiGroups: ["apps"]
    resources: ["deployments", "replicasets", "statefulsets"]
    verbs: ["get", "list", "watch"]
  
  # Ingress management
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses"]
    verbs: ["get", "list", "watch"]
  
  # Custom Resource Definitions
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  
  # Metrics access
  - apiGroups: ["metrics.k8s.io"]
    resources: ["pods", "nodes"]
    verbs: ["get", "list"]

---
# Cluster Role Binding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: mosaic-cluster-role-binding
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
subjects:
  - kind: ServiceAccount
    name: mosaic-service-account
    namespace: mosaic
roleRef:
  kind: ClusterRole
  name: mosaic-cluster-role
  apiGroup: rbac.authorization.k8s.io

---
# Role for namespace-specific operations
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: mosaic-namespace-role
  namespace: mosaic
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
rules:
  # Full access to namespace resources
  - apiGroups: [""]
    resources: ["*"]
    verbs: ["*"]
  
  # App management
  - apiGroups: ["apps"]
    resources: ["*"]
    verbs: ["*"]
  
  # Networking
  - apiGroups: ["networking.k8s.io"]
    resources: ["*"]
    verbs: ["*"]
  
  # Autoscaling
  - apiGroups: ["autoscaling"]
    resources: ["*"]
    verbs: ["*"]

---
# Role Binding for namespace operations
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: mosaic-namespace-role-binding
  namespace: mosaic
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
subjects:
  - kind: ServiceAccount
    name: mosaic-service-account
    namespace: mosaic
roleRef:
  kind: Role
  name: mosaic-namespace-role
  apiGroup: rbac.authorization.k8s.io

---
# Network Policy for MOSAIC namespace
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mosaic-network-policy
  namespace: mosaic
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    # Allow ingress from same namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: mosaic
    
    # Allow ingress from monitoring namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
    
    # Allow ingress from ingress controller
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
    
    # Allow specific ports
    - ports:
        - protocol: TCP
          port: 3000
        - protocol: TCP
          port: 3001
        - protocol: TCP
          port: 3002
        - protocol: TCP
          port: 3003
        - protocol: TCP
          port: 3004
        - protocol: TCP
          port: 9090
  
  egress:
    # Allow all egress (can be restricted based on security requirements)
    - {}

---
# Resource Quota for MOSAIC namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: mosaic-resource-quota
  namespace: mosaic
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
spec:
  hard:
    # Compute resources
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    
    # Storage
    requests.storage: 1Ti
    persistentvolumeclaims: "20"
    
    # Object counts
    pods: "100"
    services: "50"
    secrets: "50"
    configmaps: "50"
    replicationcontrollers: "20"
    deployments.apps: "20"
    statefulsets.apps: "10"

---
# Limit Range for MOSAIC namespace
apiVersion: v1
kind: LimitRange
metadata:
  name: mosaic-limit-range
  namespace: mosaic
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
spec:
  limits:
    # Default limits for containers
    - default:
        cpu: "2"
        memory: "4Gi"
      defaultRequest:
        cpu: "500m"
        memory: "1Gi"
      type: Container
    
    # Limits for pods
    - max:
        cpu: "8"
        memory: "16Gi"
      min:
        cpu: "100m"
        memory: "128Mi"
      type: Pod
    
    # Limits for persistent volume claims
    - max:
        storage: "100Gi"
      min:
        storage: "1Gi"
      type: PersistentVolumeClaim

---
# Priority Class for MOSAIC workloads
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: mosaic-high-priority
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
value: 1000
globalDefault: false
description: "High priority class for critical MOSAIC components"

---
# Priority Class for MOSAIC infrastructure
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: mosaic-infrastructure-priority
  labels:
    app.kubernetes.io/name: mosaic
    app.kubernetes.io/part-of: mosaic
value: 1500
globalDefault: false
description: "Highest priority class for MOSAIC infrastructure components"
