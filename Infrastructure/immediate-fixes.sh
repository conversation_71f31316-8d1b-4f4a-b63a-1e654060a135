#!/bin/bash
# ALIAS Infrastructure - Immediate Fixes Script
# Run as root on Proxmox server

echo "=== ALIAS Infrastructure Immediate Fixes ==="
echo "Starting at: $(date)"

# 1. Fix Critical Storage Issue (95% full)
echo -e "\n[1/5] Cleaning up storage..."
docker system prune -a --volumes -f
apt-get clean
apt-get autoremove -y
journalctl --vacuum-time=7d
find /var/log -type f -name "*.log" -mtime +30 -delete
echo "Storage cleanup complete. New usage:"
df -h /

# 2. Fix GitLab Port Conflicts
echo -e "\n[2/5] Fixing GitLab port configuration..."
cat > /tmp/gitlab-port-fix.rb << 'EOF'
# Temporary fix - add to /etc/gitlab/gitlab.rb
external_url 'http://gitlab.alias.local'
nginx['listen_port'] = 80
nginx['listen_https'] = false

# Proper proxy configuration
nginx['real_ip_trusted_addresses'] = ['127.0.0.1', '***********/24']
nginx['real_ip_header'] = 'X-Forwarded-For'
nginx['real_ip_recursive'] = 'on'

# Container registry
registry_external_url 'http://registry.alias.local:5000'
EOF
echo "GitLab configuration prepared. Review /tmp/gitlab-port-fix.rb"

# 3. Create VLAN-aware bridge configuration
echo -e "\n[3/5] Preparing VLAN configuration..."
cat > /tmp/interfaces-vlan << 'EOF'
# Add to /etc/network/interfaces
auto vmbr0
iface vmbr0 inet static
    address *************/24
    gateway ***********
    bridge-ports eno1
    bridge-stp off
    bridge-fd 0
    bridge-vlan-aware yes
    bridge-vids 10,20,30,40,50,100-199

# Management VLAN
auto vmbr0.10
iface vmbr0.10 inet static
    address **************/24

# Infrastructure VLAN
auto vmbr0.20
iface vmbr0.20 inet static
    address **************/24

# Container VLAN
auto vmbr0.30
iface vmbr0.30 inet static
    address ************/24
EOF
echo "VLAN configuration prepared. Review /tmp/interfaces-vlan"

# 4. Create service organization script
echo -e "\n[4/5] Creating service registry..."
cat > /opt/alias-infra/service-registry.yml << 'EOF'
# ALIAS Service Registry
services:
  infrastructure:
    proxmox:
      url: https://**************:8006
      port: 8006
      vlan: 20
    
    gitlab:
      url: http://**************:80
      port: 80
      vlan: 20
      components:
        rails: 80
        registry: 5000
        ssh: 22
        prometheus: 9090
    
    monitoring:
      grafana: **************:3000
      prometheus: **************:9090
      loki: **************:3100

  containers:
    portainer: ************0:9443
    traefik: ************1:80,443
    registry: *************:5000
EOF

# 5. Create monitoring check script
echo -e "\n[5/5] Creating health check script..."
cat > /opt/alias-infra/health-check.sh << 'EOF'
#!/bin/bash
# ALIAS Infrastructure Health Check

echo "=== ALIAS Infrastructure Health Check ==="
echo "Time: $(date)"

# Storage check
echo -e "\n[Storage Status]"
df -h | grep -E "(^/|Filesystem)"

# Container check
echo -e "\n[Proxmox Containers]"
pct list

# Docker check
echo -e "\n[Docker Containers]"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Network check
echo -e "\n[Network Status]"
ip -4 addr show | grep inet

# GitLab check
echo -e "\n[GitLab Status]"
gitlab-ctl status | grep "^run:"

# Service ports
echo -e "\n[Listening Ports]"
ss -tlnp | grep LISTEN | grep -E "(80|443|8080|22|5000|8006)"
EOF
chmod +x /opt/alias-infra/health-check.sh

echo -e "\n=== Setup Complete ==="
echo "Next steps:"
echo "1. Review and apply GitLab config: /tmp/gitlab-port-fix.rb"
echo "2. Review and apply network config: /tmp/interfaces-vlan"
echo "3. Run health check: /opt/alias-infra/health-check.sh"
echo "4. Consider running: gitlab-ctl reconfigure"
echo ""
echo "Completed at: $(date)"