/**
 * Letta AI Agent File (.af) Converter for ALIAS MOSAIC
 * 
 * Provides bidirectional conversion between Letta agent files and MOSAIC agents,
 * enabling agent portability, state persistence, and cross-framework compatibility.
 */

import { z } from 'zod';

// Letta Agent File Schema
const LettaAgentSchema = z.object({
  version: z.string(),
  metadata: z.object({
    name: z.string(),
    description: z.string().optional(),
    created_at: z.string(),
    updated_at: z.string(),
    tags: z.array(z.string()).optional(),
  }),
  model: z.object({
    provider: z.string(),
    name: z.string(),
    parameters: z.record(z.any()).optional(),
  }),
  memory: z.object({
    messages: z.array(z.object({
      role: z.enum(['system', 'user', 'assistant', 'function']),
      content: z.string(),
      timestamp: z.string().optional(),
      metadata: z.record(z.any()).optional(),
    })),
    blocks: z.array(z.object({
      id: z.string(),
      type: z.string(),
      content: z.any(),
      metadata: z.record(z.any()).optional(),
    })).optional(),
  }),
  system: z.object({
    prompts: z.array(z.object({
      id: z.string(),
      content: z.string(),
      role: z.string().optional(),
    })),
    rules: z.array(z.object({
      id: z.string(),
      description: z.string(),
      condition: z.string().optional(),
      action: z.string().optional(),
    })).optional(),
  }),
  tools: z.array(z.object({
    name: z.string(),
    description: z.string(),
    parameters: z.record(z.any()),
    returns: z.record(z.any()).optional(),
  })).optional(),
  environment: z.record(z.string()).optional(),
});

// MOSAIC Agent Schema
const MOSAICAgentSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  domain: z.object({
    primary: z.enum(['SAD', 'KAD', 'IKD', 'MLD', 'TKD', 'MCD', 'ESD', 'UPD']),
    secondary: z.array(z.enum(['SAD', 'KAD', 'IKD', 'MLD', 'TKD', 'MCD', 'ESD', 'UPD'])).optional(),
  }),
  capabilities: z.array(z.string()),
  memory: z.object({
    shortTerm: z.array(z.any()),
    longTerm: z.array(z.any()),
    workingMemory: z.record(z.any()),
  }),
  tools: z.array(z.object({
    id: z.string(),
    name: z.string(),
    command: z.string(),
    parameters: z.record(z.any()),
    domain: z.string().optional(),
  })),
  lifecycle: z.object({
    stage: z.number().min(1).max(11),
    substage: z.string().optional(),
  }),
  quantumCommands: z.array(z.object({
    command: z.string(),
    description: z.string(),
    handler: z.string(),
  })),
  metadata: z.object({
    created: z.string(),
    updated: z.string(),
    version: z.string(),
    checkpoints: z.array(z.string()).optional(),
  }),
});

type LettaAgent = z.infer<typeof LettaAgentSchema>;
type MOSAICAgent = z.infer<typeof MOSAICAgentSchema>;
type PRISMDomain = MOSAICAgent['domain']['primary'];

export class LettaAgentConverter {
  private domainMapping: Record<string, PRISMDomain> = {
    'system': 'SAD',
    'knowledge': 'KAD',
    'inference': 'IKD',
    'learning': 'MLD',
    'temporal': 'TKD',
    'decision': 'MCD',
    'emergent': 'ESD',
    'unified': 'UPD',
  };

  /**
   * Import a Letta agent file and convert to MOSAIC agent
   */
  async importAgent(afContent: string, options?: {
    domain?: PRISMDomain;
    lifecycle?: number;
    validate?: boolean;
  }): Promise<MOSAICAgent> {
    // Parse and validate Letta agent
    const lettaAgent = this.parseAFFile(afContent);
    
    if (options?.validate) {
      this.validateLettaAgent(lettaAgent);
    }

    // Convert to MOSAIC agent
    const mosaicAgent: MOSAICAgent = {
      id: this.generateMOSAICId(lettaAgent),
      name: lettaAgent.metadata.name,
      description: lettaAgent.metadata.description,
      domain: {
        primary: options?.domain || this.inferPRISMDomain(lettaAgent),
        secondary: this.inferSecondaryDomains(lettaAgent),
      },
      capabilities: this.extractCapabilities(lettaAgent),
      memory: this.convertMemory(lettaAgent.memory),
      tools: this.convertTools(lettaAgent.tools || []),
      lifecycle: {
        stage: options?.lifecycle || this.inferLifecycleStage(lettaAgent),
        substage: 'imported',
      },
      quantumCommands: this.generateQuantumCommands(lettaAgent),
      metadata: {
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        version: '1.0.0',
        checkpoints: [],
      },
    };

    return mosaicAgent;
  }

  /**
   * Export a MOSAIC agent to Letta AF format
   */
  async exportAgent(agent: MOSAICAgent, options?: {
    includeHistory?: boolean;
    checkpoint?: boolean;
  }): Promise<string> {
    const lettaAgent: LettaAgent = {
      version: '1.0',
      metadata: {
        name: agent.name,
        description: agent.description || '',
        created_at: agent.metadata.created,
        updated_at: agent.metadata.updated,
        tags: [agent.domain.primary, `lifecycle-${agent.lifecycle.stage}`],
      },
      model: this.extractModelConfig(agent),
      memory: this.convertMOSAICMemory(agent.memory, options?.includeHistory),
      system: this.extractSystemConfig(agent),
      tools: this.convertMOSAICTools(agent.tools),
      environment: this.extractEnvironment(agent),
    };

    return this.serializeToAF(lettaAgent);
  }

  /**
   * Parse Letta AF file content
   */
  private parseAFFile(content: string): LettaAgent {
    try {
      const parsed = JSON.parse(content);
      return LettaAgentSchema.parse(parsed);
    } catch (error) {
      throw new Error(`Failed to parse Letta agent file: ${error}`);
    }
  }

  /**
   * Validate Letta agent structure
   */
  private validateLettaAgent(agent: LettaAgent): void {
    // Validate required fields
    if (!agent.metadata.name) {
      throw new Error('Agent name is required');
    }
    
    if (!agent.model.provider || !agent.model.name) {
      throw new Error('Model configuration is required');
    }

    // Validate memory structure
    if (!agent.memory.messages || agent.memory.messages.length === 0) {
      throw new Error('Agent must have at least one message in memory');
    }
  }

  /**
   * Generate MOSAIC agent ID
   */
  private generateMOSAICId(lettaAgent: LettaAgent): string {
    const name = lettaAgent.metadata.name.toLowerCase().replace(/\s+/g, '-');
    const timestamp = Date.now();
    return `mosaic-${name}-${timestamp}`;
  }

  /**
   * Infer PRISM-ICL domain from Letta agent
   */
  private inferPRISMDomain(agent: LettaAgent): PRISMDomain {
    // Analyze agent characteristics to determine domain
    const tools = agent.tools || [];
    const prompts = agent.system.prompts;
    
    // Check for domain indicators
    if (tools.some(t => t.name.includes('architecture') || t.name.includes('system'))) {
      return 'SAD';
    }
    if (tools.some(t => t.name.includes('knowledge') || t.name.includes('search'))) {
      return 'KAD';
    }
    if (prompts.some(p => p.content.includes('inference') || p.content.includes('reasoning'))) {
      return 'IKD';
    }
    if (prompts.some(p => p.content.includes('learning') || p.content.includes('training'))) {
      return 'MLD';
    }
    
    // Default to Knowledge Architecture Domain
    return 'KAD';
  }

  /**
   * Infer secondary domains
   */
  private inferSecondaryDomains(agent: LettaAgent): PRISMDomain[] {
    const domains = new Set<PRISMDomain>();
    
    // Analyze for cross-domain capabilities
    const allContent = [
      ...agent.system.prompts.map(p => p.content),
      ...(agent.tools || []).map(t => t.description),
    ].join(' ').toLowerCase();
    
    if (allContent.includes('temporal') || allContent.includes('time')) {
      domains.add('TKD');
    }
    if (allContent.includes('decision') || allContent.includes('choice')) {
      domains.add('MCD');
    }
    if (allContent.includes('emergent') || allContent.includes('pattern')) {
      domains.add('ESD');
    }
    
    return Array.from(domains);
  }

  /**
   * Extract agent capabilities
   */
  private extractCapabilities(agent: LettaAgent): string[] {
    const capabilities: string[] = [];
    
    // Extract from tools
    if (agent.tools) {
      capabilities.push(...agent.tools.map(t => t.description));
    }
    
    // Extract from system prompts
    agent.system.prompts.forEach(prompt => {
      if (prompt.content.includes('can') || prompt.content.includes('able to')) {
        capabilities.push(prompt.content);
      }
    });
    
    return capabilities;
  }

  /**
   * Convert Letta memory to MOSAIC memory
   */
  private convertMemory(lettaMemory: LettaAgent['memory']): MOSAICAgent['memory'] {
    return {
      shortTerm: lettaMemory.messages.slice(-10), // Last 10 messages
      longTerm: lettaMemory.blocks || [],
      workingMemory: {
        context: lettaMemory.messages.slice(-3),
        activeBlocks: lettaMemory.blocks?.slice(0, 5) || [],
      },
    };
  }

  /**
   * Convert Letta tools to MOSAIC tools
   */
  private convertTools(lettaTools: LettaAgent['tools']): MOSAICAgent['tools'] {
    if (!lettaTools) return [];
    
    return lettaTools.map(tool => ({
      id: `tool-${tool.name}`,
      name: tool.name,
      command: `!${tool.name.toUpperCase()}`,
      parameters: tool.parameters,
      domain: this.inferToolDomain(tool),
    }));
  }

  /**
   * Infer tool domain
   */
  private inferToolDomain(tool: { name: string; description: string }): string {
    const name = tool.name.toLowerCase();
    const desc = tool.description.toLowerCase();
    
    for (const [keyword, domain] of Object.entries(this.domainMapping)) {
      if (name.includes(keyword) || desc.includes(keyword)) {
        return domain;
      }
    }
    
    return 'KAD'; // Default domain
  }

  /**
   * Infer lifecycle stage
   */
  private inferLifecycleStage(agent: LettaAgent): number {
    // Analyze agent maturity
    const messageCount = agent.memory.messages.length;
    const toolCount = agent.tools?.length || 0;
    
    if (messageCount < 10 && toolCount < 3) return 1; // Discovery
    if (messageCount < 50 && toolCount < 5) return 2; // Qualification
    if (messageCount < 100) return 3; // Architecture
    if (messageCount < 500) return 4; // Development
    if (messageCount < 1000) return 5; // Testing
    return 6; // Deployment
  }

  /**
   * Generate quantum commands
   */
  private generateQuantumCommands(agent: LettaAgent): MOSAICAgent['quantumCommands'] {
    const commands: MOSAICAgent['quantumCommands'] = [];
    
    // Generate from tools
    if (agent.tools) {
      commands.push(...agent.tools.map(tool => ({
        command: `!${tool.name.toUpperCase()}`,
        description: tool.description,
        handler: `handle${tool.name}`,
      })));
    }
    
    // Add standard PRISM-ICL commands
    commands.push({
      command: '!STATUS',
      description: 'Get agent status and health',
      handler: 'handleStatus',
    });
    
    return commands;
  }

  /**
   * Extract model configuration
   */
  private extractModelConfig(agent: MOSAICAgent): LettaAgent['model'] {
    return {
      provider: 'mosaic',
      name: `mosaic-${agent.domain.primary.toLowerCase()}`,
      parameters: {
        domain: agent.domain.primary,
        lifecycle: agent.lifecycle.stage,
        version: agent.metadata.version,
      },
    };
  }

  /**
   * Convert MOSAIC memory to Letta format
   */
  private convertMOSAICMemory(
    memory: MOSAICAgent['memory'],
    includeHistory?: boolean
  ): LettaAgent['memory'] {
    const messages = includeHistory 
      ? [...memory.longTerm, ...memory.shortTerm]
      : memory.shortTerm;
    
    return {
      messages: messages.map((msg: any) => ({
        role: msg.role || 'assistant',
        content: msg.content || JSON.stringify(msg),
        timestamp: msg.timestamp || new Date().toISOString(),
        metadata: msg.metadata || {},
      })),
      blocks: memory.longTerm.map((block: any, index: number) => ({
        id: `block-${index}`,
        type: 'memory',
        content: block,
        metadata: {},
      })),
    };
  }

  /**
   * Extract system configuration
   */
  private extractSystemConfig(agent: MOSAICAgent): LettaAgent['system'] {
    return {
      prompts: [
        {
          id: 'main',
          content: `You are ${agent.name}, a MOSAIC agent operating in the ${agent.domain.primary} domain.`,
        },
        {
          id: 'capabilities',
          content: `Your capabilities include: ${agent.capabilities.join(', ')}`,
        },
      ],
      rules: agent.quantumCommands.map(cmd => ({
        id: cmd.command,
        description: cmd.description,
        action: cmd.handler,
      })),
    };
  }

  /**
   * Convert MOSAIC tools to Letta format
   */
  private convertMOSAICTools(tools: MOSAICAgent['tools']): LettaAgent['tools'] {
    return tools.map(tool => ({
      name: tool.name,
      description: `${tool.name} - Domain: ${tool.domain}`,
      parameters: tool.parameters,
    }));
  }

  /**
   * Extract environment configuration
   */
  private extractEnvironment(agent: MOSAICAgent): Record<string, string> {
    return {
      MOSAIC_AGENT_ID: agent.id,
      MOSAIC_DOMAIN: agent.domain.primary,
      MOSAIC_LIFECYCLE: agent.lifecycle.stage.toString(),
      MOSAIC_VERSION: agent.metadata.version,
    };
  }

  /**
   * Serialize agent to AF format
   */
  private serializeToAF(agent: LettaAgent): string {
    return JSON.stringify(agent, null, 2);
  }
}

// Export singleton instance
export const lettaConverter = new LettaAgentConverter();