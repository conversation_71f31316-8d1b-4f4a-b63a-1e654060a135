/**
 * MOSAIC Agent Registry CLI Commands
 * 
 * Provides shadcn-style component management for AI agents,
 * enabling easy discovery, installation, and management of
 * production-ready agents from the MOSAIC registry.
 */

import { Command } from 'commander';
import { z } from 'zod';
import chalk from 'chalk';
import ora from 'ora';
import Table from 'cli-table3';
import inquirer from 'inquirer';
import fuzzy from 'fuzzy';
import { readFile, writeFile, mkdir, access } from 'fs/promises';
import { join, resolve } from 'path';
import fetch from 'node-fetch';
import { execSync } from 'child_process';

// Registry configuration
const REGISTRY_URL = process.env.MOSAIC_REGISTRY_URL || 'https://registry.mosaic.alias.dev';
const REGISTRY_API = `${REGISTRY_URL}/api/v1`;

// Local configuration
const CONFIG_FILE = '.mosaic/registry.json';
const AGENTS_DIR = 'agents';

// Types
interface RegistryAgent {
  id: string;
  name: string;
  description: string;
  category: string;
  domain: string;
  version: string;
  downloads: number;
  rating: number;
  featured: boolean;
}

interface RegistryConfig {
  version: string;
  installedAgents: Array<{
    id: string;
    version: string;
    installedAt: string;
    path: string;
  }>;
  preferences: {
    defaultDomain?: string;
    defaultLifecycle?: number;
    autoUpdate?: boolean;
  };
}

// Create agent registry command
export function createAgentRegistryCommand() {
  const agent = new Command('agent')
    .description('Manage MOSAIC agents from the registry');

  // Browse agents
  agent
    .command('browse')
    .description('Browse available agents in the registry')
    .option('-c, --category <category>', 'Filter by category')
    .option('-d, --domain <domain>', 'Filter by PRISM-ICL domain')
    .option('-f, --featured', 'Show only featured agents')
    .option('-s, --sort <field>', 'Sort by: name, downloads, rating', 'downloads')
    .action(async (options) => {
      await browseAgents(options);
    });

  // Search agents
  agent
    .command('search <query>')
    .description('Search for agents by name or description')
    .option('-l, --limit <number>', 'Limit results', '10')
    .action(async (query, options) => {
      await searchAgents(query, options);
    });

  // Get agent info
  agent
    .command('info <agent-id>')
    .description('Show detailed information about an agent')
    .action(async (agentId) => {
      await showAgentInfo(agentId);
    });

  // Add agent
  agent
    .command('add <agent-id>')
    .description('Install an agent from the registry')
    .option('-p, --path <path>', 'Installation path', AGENTS_DIR)
    .option('-d, --domain <domain>', 'Override domain assignment')
    .option('-l, --lifecycle <stage>', 'Set lifecycle stage', '3')
    .option('--no-install', 'Skip dependency installation')
    .option('--no-setup', 'Skip initial setup')
    .action(async (agentId, options) => {
      await addAgent(agentId, options);
    });

  // Update agent
  agent
    .command('update [agent-id]')
    .description('Update installed agents')
    .option('-a, --all', 'Update all agents')
    .action(async (agentId, options) => {
      await updateAgents(agentId, options);
    });

  // Remove agent
  agent
    .command('remove <agent-id>')
    .description('Remove an installed agent')
    .option('-f, --force', 'Force removal without confirmation')
    .action(async (agentId, options) => {
      await removeAgent(agentId, options);
    });

  // List installed agents
  agent
    .command('installed')
    .alias('list')
    .description('List installed agents')
    .option('-v, --verbose', 'Show detailed information')
    .action(async (options) => {
      await listInstalledAgents(options);
    });

  // Check for updates
  agent
    .command('outdated')
    .description('Check for agent updates')
    .action(async () => {
      await checkOutdatedAgents();
    });

  // Submit agent
  agent
    .command('submit <path>')
    .description('Submit an agent to the registry')
    .action(async (path) => {
      await submitAgent(path);
    });

  // Validate agent
  agent
    .command('validate <path>')
    .description('Validate an agent for registry submission')
    .action(async (path) => {
      await validateAgent(path);
    });

  return agent;
}

// Browse agents implementation
async function browseAgents(options: any) {
  const spinner = ora('Fetching agents from registry...').start();
  
  try {
    const params = new URLSearchParams();
    if (options.category) params.append('category', options.category);
    if (options.domain) params.append('domain', options.domain);
    if (options.featured) params.append('featured', 'true');
    if (options.sort) params.append('sort', options.sort);
    
    const response = await fetch(`${REGISTRY_API}/agents?${params}`);
    const data = await response.json() as { agents: RegistryAgent[] };
    
    spinner.succeed(`Found ${data.agents.length} agents`);
    
    if (data.agents.length === 0) {
      console.log(chalk.yellow('No agents found matching your criteria'));
      return;
    }
    
    // Display agents in a table
    const table = new Table({
      head: [
        chalk.cyan('ID'),
        chalk.cyan('Name'),
        chalk.cyan('Category'),
        chalk.cyan('Domain'),
        chalk.cyan('Downloads'),
        chalk.cyan('Rating')
      ],
      style: { head: [], border: [] }
    });
    
    data.agents.forEach(agent => {
      table.push([
        agent.featured ? chalk.yellow('⭐ ') + agent.id : agent.id,
        agent.name,
        agent.category,
        chalk.magenta(agent.domain),
        agent.downloads.toLocaleString(),
        `${agent.rating}/5`
      ]);
    });
    
    console.log(table.toString());
    console.log(`\nUse ${chalk.cyan('mosaic agent info <id>')} for more details`);
    console.log(`Use ${chalk.green('mosaic agent add <id>')} to install`);
    
  } catch (error) {
    spinner.fail('Failed to fetch agents');
    console.error(chalk.red(error));
    process.exit(1);
  }
}

// Search agents implementation
async function searchAgents(query: string, options: any) {
  const spinner = ora(`Searching for "${query}"...`).start();
  
  try {
    const response = await fetch(`${REGISTRY_API}/agents/search?q=${encodeURIComponent(query)}&limit=${options.limit}`);
    const data = await response.json() as { results: RegistryAgent[] };
    
    spinner.succeed(`Found ${data.results.length} results`);
    
    if (data.results.length === 0) {
      console.log(chalk.yellow('No agents found matching your search'));
      return;
    }
    
    // Display search results
    data.results.forEach((agent, index) => {
      console.log(`\n${chalk.bold(`${index + 1}. ${agent.name}`)} ${chalk.gray(`(${agent.id})`)}`);
      console.log(`   ${agent.description}`);
      console.log(`   ${chalk.gray('Category:')} ${agent.category} | ${chalk.gray('Domain:')} ${chalk.magenta(agent.domain)} | ${chalk.gray('Rating:')} ${agent.rating}/5`);
    });
    
    console.log(`\n${chalk.cyan('mosaic agent add <id>')} to install`);
    
  } catch (error) {
    spinner.fail('Search failed');
    console.error(chalk.red(error));
    process.exit(1);
  }
}

// Show agent info implementation
async function showAgentInfo(agentId: string) {
  const spinner = ora(`Fetching agent details...`).start();
  
  try {
    const response = await fetch(`${REGISTRY_API}/agents/${agentId}`);
    if (!response.ok) {
      throw new Error(`Agent not found: ${agentId}`);
    }
    
    const agent = await response.json();
    spinner.succeed('Agent details loaded');
    
    // Display agent information
    console.log(`\n${chalk.bold(agent.name)} ${chalk.gray(`v${agent.version}`)}`);
    if (agent.featured) console.log(chalk.yellow('⭐ Featured Agent'));
    console.log(chalk.gray('─'.repeat(50)));
    console.log(agent.description);
    console.log();
    
    // Basic info
    console.log(chalk.cyan('Basic Information:'));
    console.log(`  ID: ${agent.id}`);
    console.log(`  Category: ${agent.category}`);
    console.log(`  Domain: ${chalk.magenta(agent.domain.primary)} ${agent.domain.secondary?.length ? `(${agent.domain.secondary.join(', ')})` : ''}`);
    console.log(`  Author: ${agent.author.name}`);
    console.log(`  License: ${agent.license}`);
    console.log();
    
    // Stats
    console.log(chalk.cyan('Statistics:'));
    console.log(`  Downloads: ${agent.downloads.toLocaleString()}`);
    console.log(`  Rating: ${agent.rating}/5`);
    console.log(`  Success Rate: ${agent.performance.metrics.successRate}`);
    console.log(`  Avg Response: ${agent.performance.metrics.avgResponseTime}`);
    console.log();
    
    // Capabilities
    if (agent.domain.capabilities?.length) {
      console.log(chalk.cyan('Capabilities:'));
      agent.domain.capabilities.forEach((cap: string) => {
        console.log(`  • ${cap}`);
      });
      console.log();
    }
    
    // Dependencies
    console.log(chalk.cyan('Dependencies:'));
    console.log(`  Models: ${agent.dependencies.required.models.join(', ')}`);
    console.log(`  Memory: ${agent.dependencies.required.memory}`);
    if (agent.dependencies.optional?.services?.length) {
      console.log(`  Optional: ${agent.dependencies.optional.services.join(', ')}`);
    }
    console.log();
    
    // Examples
    if (agent.examples?.length) {
      console.log(chalk.cyan('Examples:'));
      agent.examples.forEach((ex: any) => {
        console.log(`  ${chalk.bold(ex.title)}`);
        console.log(`    Input: ${chalk.gray(ex.input)}`);
        console.log(`    Output: ${chalk.gray(ex.outputPreview.substring(0, 50))}...`);
      });
      console.log();
    }
    
    // Installation
    console.log(chalk.cyan('Installation:'));
    console.log(`  ${chalk.green('mosaic agent add ' + agent.id)}`);
    console.log();
    
    // Links
    console.log(chalk.cyan('Resources:'));
    console.log(`  Docs: ${agent.support.documentation}`);
    console.log(`  Issues: ${agent.support.issues}`);
    
  } catch (error) {
    spinner.fail('Failed to fetch agent details');
    console.error(chalk.red(error));
    process.exit(1);
  }
}

// Add agent implementation
async function addAgent(agentId: string, options: any) {
  const spinner = ora(`Installing ${agentId}...`).start();
  
  try {
    // Fetch agent metadata
    spinner.text = 'Fetching agent metadata...';
    const metaResponse = await fetch(`${REGISTRY_API}/agents/${agentId}`);
    if (!metaResponse.ok) {
      throw new Error(`Agent not found: ${agentId}`);
    }
    const metadata = await metaResponse.json();
    
    // Check if already installed
    const config = await loadConfig();
    const existing = config.installedAgents.find(a => a.id === agentId);
    if (existing) {
      const { overwrite } = await inquirer.prompt([{
        type: 'confirm',
        name: 'overwrite',
        message: `Agent ${agentId} is already installed. Overwrite?`,
        default: false
      }]);
      
      if (!overwrite) {
        spinner.warn('Installation cancelled');
        return;
      }
    }
    
    // Download agent file
    spinner.text = 'Downloading agent file...';
    const afResponse = await fetch(`${REGISTRY_API}/agents/${agentId}/download`);
    if (!afResponse.ok) {
      throw new Error('Failed to download agent file');
    }
    const agentContent = await afResponse.text();
    
    // Create agent directory
    const agentPath = resolve(options.path, agentId);
    await mkdir(agentPath, { recursive: true });
    
    // Save agent file
    const afPath = join(agentPath, 'agent.af');
    await writeFile(afPath, agentContent);
    
    // Import agent using the import script
    spinner.text = 'Importing agent...';
    const importCommand = [
      './scripts/agent-import.sh',
      afPath,
      options.domain ? `-d ${options.domain}` : '',
      `-l ${options.lifecycle}`,
      `-o ${agentPath}`
    ].filter(Boolean).join(' ');
    
    execSync(importCommand, { stdio: 'pipe' });
    
    // Install dependencies if needed
    if (options.install !== false) {
      spinner.text = 'Installing dependencies...';
      // Run any necessary setup commands
      if (metadata.installation?.setup) {
        execSync(metadata.installation.setup, { cwd: agentPath, stdio: 'pipe' });
      }
    }
    
    // Update config
    const newConfig: RegistryConfig = {
      ...config,
      installedAgents: [
        ...config.installedAgents.filter(a => a.id !== agentId),
        {
          id: agentId,
          version: metadata.version,
          installedAt: new Date().toISOString(),
          path: agentPath
        }
      ]
    };
    await saveConfig(newConfig);
    
    spinner.succeed(`Successfully installed ${chalk.green(metadata.name)}`);
    
    // Show post-install instructions
    console.log(`\n${chalk.cyan('Next steps:')}`);
    console.log(`  1. Review the agent in: ${chalk.gray(agentPath)}`);
    console.log(`  2. Configure as needed: ${chalk.gray(join(agentPath, 'config'))}`);
    console.log(`  3. Test the agent: ${chalk.gray(`mosaic agent test ${agentId}`)}`);
    
    if (metadata.examples?.length) {
      console.log(`\n${chalk.cyan('Quick example:')}`);
      console.log(chalk.gray(metadata.examples[0].code || metadata.examples[0].input));
    }
    
  } catch (error) {
    spinner.fail('Installation failed');
    console.error(chalk.red(error));
    process.exit(1);
  }
}

// List installed agents
async function listInstalledAgents(options: any) {
  try {
    const config = await loadConfig();
    
    if (config.installedAgents.length === 0) {
      console.log(chalk.yellow('No agents installed yet'));
      console.log(`Use ${chalk.cyan('mosaic agent browse')} to discover agents`);
      return;
    }
    
    console.log(chalk.bold(`\nInstalled Agents (${config.installedAgents.length}):`));
    console.log(chalk.gray('─'.repeat(50)));
    
    if (options.verbose) {
      // Detailed view
      for (const agent of config.installedAgents) {
        console.log(`\n${chalk.bold(agent.id)} ${chalk.gray(`v${agent.version}`)}`);
        console.log(`  Path: ${agent.path}`);
        console.log(`  Installed: ${new Date(agent.installedAt).toLocaleDateString()}`);
        
        // Check for updates
        try {
          const response = await fetch(`${REGISTRY_API}/agents/${agent.id}/version`);
          const { latest } = await response.json();
          if (latest !== agent.version) {
            console.log(`  ${chalk.yellow(`Update available: v${latest}`)}`);
          }
        } catch {}
      }
    } else {
      // Simple table view
      const table = new Table({
        head: [
          chalk.cyan('Agent ID'),
          chalk.cyan('Version'),
          chalk.cyan('Installed'),
          chalk.cyan('Status')
        ]
      });
      
      for (const agent of config.installedAgents) {
        let status = chalk.green('Active');
        
        // Check for updates
        try {
          const response = await fetch(`${REGISTRY_API}/agents/${agent.id}/version`);
          const { latest } = await response.json();
          if (latest !== agent.version) {
            status = chalk.yellow('Update available');
          }
        } catch {
          status = chalk.gray('Unknown');
        }
        
        table.push([
          agent.id,
          agent.version,
          new Date(agent.installedAt).toLocaleDateString(),
          status
        ]);
      }
      
      console.log(table.toString());
    }
    
    console.log(`\nUse ${chalk.cyan('mosaic agent update')} to update agents`);
    
  } catch (error) {
    console.error(chalk.red('Failed to list agents:'), error);
    process.exit(1);
  }
}

// Configuration helpers
async function loadConfig(): Promise<RegistryConfig> {
  try {
    const configPath = resolve(CONFIG_FILE);
    await access(configPath);
    const content = await readFile(configPath, 'utf-8');
    return JSON.parse(content);
  } catch {
    // Return default config if file doesn't exist
    return {
      version: '1.0.0',
      installedAgents: [],
      preferences: {}
    };
  }
}

async function saveConfig(config: RegistryConfig): Promise<void> {
  const configPath = resolve(CONFIG_FILE);
  const configDir = resolve('.mosaic');
  await mkdir(configDir, { recursive: true });
  await writeFile(configPath, JSON.stringify(config, null, 2));
}

// Additional implementations for other commands would follow...
// (updateAgents, removeAgent, checkOutdatedAgents, submitAgent, validateAgent)