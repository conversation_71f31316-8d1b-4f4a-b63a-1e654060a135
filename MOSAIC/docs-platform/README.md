# Fumadocs MCP

This is a starter template for building documentation using [Fumadocs](https://fumadocs.vercel.app), integrated with OpenAPI and designed to work seamlessly with the Vercel AI SDK.

It’s powered by [Next.js](https://nextjs.org) and was generated using [Create Fumadocs](https://github.com/fuma-nama/fumadocs).

## Getting Started

Run the development server:

```bash
npm run dev
# or
pnpm dev
# or
yarn dev
```

Then open [http://localhost:3000](http://localhost:3000) in your browser.

## What’s Inside

- 🧩 **Fumadocs** – Fast, flexible documentation powered by MDX.
- 📘 **Orama** – Built-in search and AI Search integration. (Note: Uncomment Orama in providers.tsx)
- 🧠 **AI SDK** – Supports the [Vercel AI SDK](https://sdk.vercel.ai) for advanced AI chat features.
- 🧱 **MCP-Ready** – Easily extend with an optional MCP Server for more advanced workflows.

## Learn More

- [Next.js Documentation](https://nextjs.org/docs) – Learn about Next.js features and APIs.
- [Learn Next.js](https://nextjs.org/learn) – Interactive Next.js tutorial.
- [Fumadocs](https://fumadocs.vercel.app) – Learn more about Fumadocs and how to customize it.
