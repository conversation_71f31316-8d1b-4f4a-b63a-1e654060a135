import { cn } from '@/lib/cn';
import { Building2Icon, RocketIcon, BrainIcon, HomeIcon, UsersIcon, CogIcon } from 'lucide-react';
import type { LinkProps } from 'next/link';
import Link from 'next/link';

export default function HomePage(): React.ReactElement {
  return (
    <main className="container flex flex-col py-16">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold md:text-6xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          MOSAIC Platform
        </h1>
        <p className="text-fd-muted-foreground text-xl mt-4 max-w-2xl mx-auto">
          AI-Driven Development Platform for Work-Life Synthesis. Experience the future where AI agents orchestrate your entire development ecosystem.
        </p>
        <div className="mt-8 flex gap-4 justify-center">
          <Link 
            href="/docs" 
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Get Started
          </Link>
          <Link 
            href="/docs/getting-started/quickstart" 
            className="border border-border px-6 py-3 rounded-lg font-medium hover:bg-fd-accent transition-colors"
          >
            Quick Start
          </Link>
        </div>
      </div>

      <div className="mt-8 grid grid-cols-1 gap-6 text-left md:grid-cols-2 lg:grid-cols-3">
        <Item href="/docs/core/overview">
          <Icon className="core">
            <BrainIcon className="size-full" />
          </Icon>
          <h2 className="mb-2 text-lg font-semibold">Core Architecture</h2>
          <p className="text-sm text-fd-muted-foreground">
            Discover MOSAIC's quantum meta-architecture and PRISM-ICL framework that powers work-life synthesis.
          </p>
        </Item>

        <Item href="/docs/agents/overview">
          <Icon className="agents">
            <UsersIcon className="size-full" />
          </Icon>
          <h2 className="mb-2 text-lg font-semibold">AI Agents</h2>
          <p className="text-sm text-fd-muted-foreground">
            Meet your life companion agents - Flow Guardian, Wellness Advocate, and more intelligent assistants.
          </p>
        </Item>

        <Item href="/docs/integrations/home-assistant">
          <Icon className="home">
            <HomeIcon className="size-full" />
          </Icon>
          <h2 className="mb-2 text-lg font-semibold">Home Integration</h2>
          <p className="text-sm text-fd-muted-foreground">
            Seamlessly connect your physical environment with Home Assistant for complete work-life harmony.
          </p>
        </Item>

        <Item href="/docs/lifecycles/overview">
          <Icon className="lifecycle">
            <CogIcon className="size-full" />
          </Icon>
          <h2 className="mb-2 text-lg font-semibold">11 Lifecycles</h2>
          <p className="text-sm text-fd-muted-foreground">
            Navigate projects through Discovery to Sunset with AI-guided lifecycle management.
          </p>
        </Item>

        <Item href="/docs/api/overview">
          <Icon className="api">
            <RocketIcon className="size-full" />
          </Icon>
          <h2 className="mb-2 text-lg font-semibold">API Reference</h2>
          <p className="text-sm text-fd-muted-foreground">
            Comprehensive API documentation for integrating MOSAIC into your development workflow.
          </p>
        </Item>

        <Item href="/docs/enterprise/overview">
          <Icon className="enterprise">
            <Building2Icon className="size-full" />
          </Icon>
          <h2 className="mb-2 text-lg font-semibold">Enterprise</h2>
          <p className="text-sm text-fd-muted-foreground">
            Scale MOSAIC across your organization with enterprise-grade security and governance.
          </p>
        </Item>
      </div>

      <div className="mt-16 text-center">
        <h2 className="text-2xl font-semibold mb-4">Ready to Experience Work-Life Synthesis?</h2>
        <p className="text-fd-muted-foreground mb-6">
          Join thousands of developers who've transformed their development experience with MOSAIC.
        </p>
        <Link 
          href="/docs/getting-started/installation"
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-all inline-block"
        >
          Start Your Journey
        </Link>
      </div>
    </main>
  );
}

function Icon({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}): React.ReactElement {
  return (
    <div
      className={cn(
        'mb-2 size-9 rounded-lg border p-1.5 shadow-fd-primary/30',
        className,
      )}
      style={{
        boxShadow: 'inset 0px 8px 8px 0px var(--tw-shadow-color)',
      }}
    >
      {children}
    </div>
  );
}

function Item(
  props: LinkProps & { className?: string; children: React.ReactNode },
): React.ReactElement {
  return (
    <Link
      {...props}
      className={cn(
        'rounded-lg border border-border p-6 shadow-xs transition-all hover:bg-fd-accent  bg-fd-accent/30',
        props.className,
      )}
    >
      {props.children}
    </Link>
  );
}
