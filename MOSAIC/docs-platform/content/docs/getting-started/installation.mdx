---
title: Installation
description: Get MOSAIC up and running in your development environment
---

import { Callout } from 'fumadocs-ui/components/callout';
import { Step, Steps } from 'fumadocs-ui/components/steps';
import { Tab, Tabs } from 'fumadocs-ui/components/tabs';

# Installation Guide

Get MOSAIC running in your environment and start experiencing work-life synthesis. Choose your installation method based on your needs.

<Callout type="info">
**Prerequisites**: Node.js 20+, Git, and either Claude <PERSON> or Cursor IDE for optimal experience.
</Callout>

## Quick Install (Recommended)

The fastest way to get started with MOSAIC:

```bash
npm install -g @alias/mosaic-cli
mosaic create my-first-project
cd my-first-project
npm run dev
```

## Installation Options

<Tabs items={['CLI Install', 'Manual Setup', 'Docker', 'Enterprise']}>
  <Tab value="CLI Install">
    ### MOSAIC CLI Installation

    <Steps>
      <Step>
        **Install the CLI globally**
        ```bash
        npm install -g @alias/mosaic-cli
        ```
      </Step>
      <Step>
        **Verify installation**
        ```bash
        mosaic --version
        # Should output: @alias/mosaic-cli v0.0.1
        ```
      </Step>
      <Step>
        **Create your first project**
        ```bash
        mosaic create my-synthesis-project
        ```
      </Step>
      <Step>
        **Start the development server**
        ```bash
        cd my-synthesis-project
        npm run dev
        ```
      </Step>
    </Steps>

    The CLI will guide you through:
    - Project type selection (Web App, API, CLI Tool, AI Agent)
    - AI model configuration (Claude 3.5 Sonnet recommended)
    - Home Assistant integration setup
    - DotAI and agent initialization
  </Tab>

  <Tab value="Manual Setup">
    ### Manual Installation

    For developers who prefer manual control:

    <Steps>
      <Step>
        **Clone the template repository**
        ```bash
        git clone https://github.com/alias-organization/mosaic-template.git my-project
        cd my-project
        ```
      </Step>
      <Step>
        **Install dependencies**
        ```bash
        npm install
        # or
        pnpm install
        # or
        yarn install
        ```
      </Step>
      <Step>
        **Configure environment**
        ```bash
        cp .env.example .env
        # Edit .env with your API keys and preferences
        ```
      </Step>
      <Step>
        **Initialize MOSAIC features**
        ```bash
        npm run mosaic:init
        ```
      </Step>
      <Step>
        **Start development**
        ```bash
        npm run dev
        ```
      </Step>
    </Steps>
  </Tab>

  <Tab value="Docker">
    ### Docker Installation

    Run MOSAIC in containers for isolation and easy deployment:

    <Steps>
      <Step>
        **Pull the MOSAIC image**
        ```bash
        docker pull alias/mosaic:latest
        ```
      </Step>
      <Step>
        **Create docker-compose.yml**
        ```yaml
        version: '3.8'
        services:
          mosaic:
            image: alias/mosaic:latest
            ports:
              - "3000:3000"
              - "8080:8080"
            environment:
              - CLAUDE_API_KEY=${CLAUDE_API_KEY}
              - HOME_ASSISTANT_URL=${HOME_ASSISTANT_URL}
              - HOME_ASSISTANT_TOKEN=${HOME_ASSISTANT_TOKEN}
            volumes:
              - ./projects:/app/projects
              - ./config:/app/config
        ```
      </Step>
      <Step>
        **Start the stack**
        ```bash
        docker-compose up -d
        ```
      </Step>
      <Step>
        **Access the dashboard**
        Open http://localhost:3000 in your browser
      </Step>
    </Steps>
  </Tab>

  <Tab value="Enterprise">
    ### Enterprise Installation

    For teams and organizations requiring advanced features:

    <Steps>
      <Step>
        **Contact Enterprise Sales**
        Reach out to our enterprise team for custom deployment options
      </Step>
      <Step>
        **Infrastructure Planning**
        - Kubernetes deployment manifests
        - Multi-tenant configuration
        - SSO integration setup
        - Compliance configuration
      </Step>
      <Step>
        **Deployment Architecture**
        ```mermaid
        graph TB
            LB[Load Balancer]
            MOSAIC[MOSAIC Cluster]
            HA[Home Assistant]
            DB[(Database)]
            REDIS[(Redis Cache)]
            
            LB --> MOSAIC
            MOSAIC --> HA
            MOSAIC --> DB
            MOSAIC --> REDIS
        ```
      </Step>
      <Step>
        **Production Deployment**
        ```bash
        kubectl apply -f mosaic-enterprise.yaml
        helm install mosaic ./helm-charts/mosaic
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Configuration

### Required Environment Variables

```bash
# AI Configuration
CLAUDE_API_KEY=your_claude_api_key_here
OPENAI_API_KEY=your_openai_key_optional

# Home Assistant Integration
HOME_ASSISTANT_URL=http://homeassistant.local:8123
HOME_ASSISTANT_TOKEN=your_long_lived_access_token

# MOSAIC Configuration
MOSAIC_AGENT_MODE=development  # development | production
MOSAIC_LIFECYCLE_STAGE=discovery
MOSAIC_WORKSPACE_PATH=/path/to/your/workspace

# Optional: Biometric Integration
HEALTH_CONNECT_ENABLED=true
WELLNESS_TRACKING_INTERVAL=300  # 5 minutes
```

### IDE Integration

<Tabs items={['Claude Code', 'Cursor', 'VS Code']}>
  <Tab value="Claude Code">
    **Claude Code Integration** (Recommended)

    MOSAIC has first-class integration with Claude Code:

    ```bash
    # Install Claude Code if not already installed
    npm install -g @anthropic/claude-code

    # Initialize MOSAIC with Claude Code
    mosaic add claude-code
    ```

    Features enabled:
    - ✅ Native agent communication
    - ✅ Automatic lifecycle transitions
    - ✅ Context-aware code generation
    - ✅ Flow state protection
  </Tab>

  <Tab value="Cursor">
    **Cursor IDE Integration**

    Enhanced AI-powered development with Cursor:

    ```bash
    # Configure Cursor for MOSAIC
    mosaic add cursor

    # Install Cursor extension
    cursor --install-extension alias.mosaic-extension
    ```

    Features enabled:
    - ✅ AI pair programming
    - ✅ Context-aware suggestions
    - ✅ Automated code review
    - ✅ Real-time agent status
  </Tab>

  <Tab value="VS Code">
    **VS Code Extension**

    Basic MOSAIC integration for VS Code:

    ```bash
    # Install MOSAIC extension
    code --install-extension alias.mosaic-vscode
    ```

    Features available:
    - ✅ Agent status monitoring
    - ✅ Lifecycle progress tracking
    - ⚠️ Limited AI integration
    - ⚠️ Manual flow management
  </Tab>
</Tabs>

## Verification

After installation, verify everything is working:

<Steps>
  <Step>
    **Check MOSAIC status**
    ```bash
    mosaic status
    ```
    Should show all systems operational
  </Step>
  <Step>
    **Test agent communication**
    ```bash
    mosaic agent list
    mosaic agent status flow_guardian
    ```
  </Step>
  <Step>
    **Verify Home Assistant connection**
    ```bash
    mosaic integrations status
    ```
  </Step>
  <Step>
    **Run health check**
    ```bash
    mosaic health
    ```
    Should pass all system checks
  </Step>
</Steps>

## Troubleshooting

### Common Issues

**Claude API Key Issues**
```bash
# Test your API key
curl -H "Authorization: Bearer $CLAUDE_API_KEY" \
  https://api.anthropic.com/v1/models
```

**Home Assistant Connection**
```bash
# Test HA connection
curl -H "Authorization: Bearer $HOME_ASSISTANT_TOKEN" \
  $HOME_ASSISTANT_URL/api/states
```

**Port Conflicts**
```bash
# Check if ports are available
lsof -i :3000  # MOSAIC dashboard
lsof -i :8080  # Agent communication
```

<Callout type="warning">
**Having trouble?** Join our [Discord community](https://discord.gg/mosaic-synthesis) or check the [troubleshooting guide](/docs/troubleshooting).
</Callout>

## Next Steps

With MOSAIC installed, you're ready to:

1. **[Create Your First Project](/docs/getting-started/quickstart)** - Build something amazing
2. **[Configure Home Integration](/docs/integrations/home-assistant)** - Connect your physical space
3. **[Meet Your Agents](/docs/agents/overview)** - Discover your AI companions
4. **[Explore Lifecycles](/docs/lifecycles/overview)** - Understand project evolution

---

*Welcome to the future of development. Your journey toward work-life synthesis begins now.*