---
title: Buy museum tickets
description: Purchase museum tickets for general entry or special events.
full: true
_openapi:
  method: POST
  route: /tickets
  toc: []
  structuredData:
    headings: []
    contents:
      - content: Purchase museum tickets for general entry or special events.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

<APIPage document={"./openapi.yml"} operations={[{"path":"/tickets","method":"post"}]} webhooks={[]} hasHead={false} />