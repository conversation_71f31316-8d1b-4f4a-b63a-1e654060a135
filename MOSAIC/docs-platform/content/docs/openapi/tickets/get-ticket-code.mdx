---
title: Get ticket QR code
description: Return an image of your ticket with scannable QR code. Used for event entry.
full: true
_openapi:
  method: GET
  route: /tickets/{ticketId}/qr
  toc: []
  structuredData:
    headings: []
    contents:
      - content: >-
          Return an image of your ticket with scannable QR code. Used for event
          entry.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

<APIPage document={"./openapi.yml"} operations={[{"path":"/tickets/{ticketId}/qr","method":"get"}]} webhooks={[]} hasHead={false} />