---
title: Delete special event
description: >-
  Delete a special event from the collection. Allows museum to cancel planned
  events.
full: true
_openapi:
  method: DELETE
  route: /special-events/{eventId}
  toc: []
  structuredData:
    headings: []
    contents:
      - content: >-
          Delete a special event from the collection. Allows museum to cancel
          planned events.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

<APIPage document={"./openapi.yml"} operations={[{"path":"/special-events/{eventId}","method":"delete"}]} webhooks={[]} hasHead={false} />