---
title: Update special event
description: Update the details of a special event.
full: true
_openapi:
  method: PATCH
  route: /special-events/{eventId}
  toc: []
  structuredData:
    headings: []
    contents:
      - content: Update the details of a special event.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

<APIPage document={"./openapi.yml"} operations={[{"path":"/special-events/{eventId}","method":"patch"}]} webhooks={[]} hasHead={false} />