---
title: List special events
description: Return a list of upcoming special events at the museum.
full: true
_openapi:
  method: GET
  route: /special-events
  toc: []
  structuredData:
    headings: []
    contents:
      - content: Return a list of upcoming special events at the museum.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

<APIPage document={"./openapi.yml"} operations={[{"path":"/special-events","method":"get"}]} webhooks={[]} hasHead={false} />