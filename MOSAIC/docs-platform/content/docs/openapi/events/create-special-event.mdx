---
title: Create special events
description: Creates a new special event for the museum.
full: true
_openapi:
  method: POST
  route: /special-events
  toc: []
  structuredData:
    headings: []
    contents:
      - content: Creates a new special event for the museum.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

<APIPage document={"./openapi.yml"} operations={[{"path":"/special-events","method":"post"}]} webhooks={[]} hasHead={false} />