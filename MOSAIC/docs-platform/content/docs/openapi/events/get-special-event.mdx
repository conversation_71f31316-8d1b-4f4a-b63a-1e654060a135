---
title: Get special event
description: Get details about a special event.
full: true
_openapi:
  method: GET
  route: /special-events/{eventId}
  toc: []
  structuredData:
    headings: []
    contents:
      - content: Get details about a special event.
---

{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

<APIPage document={"./openapi.yml"} operations={[{"path":"/special-events/{eventId}","method":"get"}]} webhooks={[]} hasHead={false} />