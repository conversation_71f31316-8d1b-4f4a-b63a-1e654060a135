{"name": "mosaic-docs", "version": "0.0.1", "private": true, "scripts": {"build": "pnpm build:pre && next build && pnpm build:post", "build:pre": "tsx ./scripts/pre-build.mts", "build:post": "tsx ./scripts/post-build.mts", "dev": "next dev --turbopack", "start": "next start", "postinstall": "fumadocs-mdx", "lint": "fumadocs-mdx && tsx ./scripts/lint.mts && next lint", "build:docs": "tsx ./scripts/generate-docs.mts", "docs:watch": "./.devcontainer/build_auto.sh", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@ai-sdk/openai": "^1.3.0", "@ai-sdk/react": "^1.2.1", "@ai-sdk/ui-utils": "^1.2.1", "@inkeep/ai-api": "^0.8.0", "@next/bundle-analyzer": "^15.2.3", "@next/env": "^15.2.3", "@oramacloud/client": "^2.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@shikijs/rehype": "^3.2.1", "ai": "^4.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fumadocs-core": "15.1.1", "fumadocs-docgen": "^2.0.0", "fumadocs-mdx": "11.5.7", "fumadocs-openapi": "7.0.4", "fumadocs-twoslash": "^3.1.0", "fumadocs-typescript": "^3.1.0", "fumadocs-ui": "15.1.1", "hast-util-to-jsx-runtime": "^2.3.6", "lucide-react": "^0.479.0", "next": "15.2.3", "oxc-transform": "^0.56.5", "react": "^19.0.0", "react-dom": "^19.0.0", "rehype-katex": "^7.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-mdx": "^3.1.0", "remark-rehype": "^11.1.1", "remark-stringify": "^11.0.0", "rimraf": "^6.0.1", "shiki": "^3.2.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@fumadocs/cli": "^0.0.8", "@tailwindcss/postcss": "^4.0.15", "@types/hast": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "22.13.9", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "chokidar": "^4.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.22.0", "eslint-config-next": "15.2.3", "fast-glob": "^3.3.3", "gray-matter": "^4.0.3", "next-validate-link": "^1.5.2", "postcss": "^8.5.3", "source-map-support": "^0.5.21", "tailwindcss": "^4.0.15", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "sharp"]}}