# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MOSAIC Documentation Platform is a Next.js 15-based documentation site built with Fumadocs, featuring AI-powered chat and search capabilities. It documents the MOSAIC Platform - an "AI-Driven Development Platform for Work-Life Synthesis" with the PRISM-ICL Framework and 11 Lifecycles.

## Development Commands

### Build & Development
```bash
# Development server with Turbopack
pnpm dev

# Full build process (includes pre/post processing)
pnpm build

# Individual build steps
pnpm build:pre    # TypeScript pre-build processing
pnpm build:post   # Post-build optimization

# Generate documentation
pnpm build:docs

# Linting (includes MDX validation and link checking)
pnpm lint
```

### Testing & Validation
```bash
# Comprehensive linting including:
# - MDX processing via fumadocs-mdx
# - Link validation across all documentation
# - URL validation with table of contents
pnpm lint

# Watch for changes (development container)
pnpm docs:watch
```

## Architecture Overview

### Technology Stack
- **Framework**: Next.js 15.2.3 with App Router
- **Documentation**: Fumadocs with MDX support
- **Styling**: Tailwind CSS 4.0
- **AI Integration**: Vercel AI SDK with OpenAI
- **Search**: Orama Cloud and Inkeep AI
- **Type Safety**: Full TypeScript coverage

### Directory Structure
```
app/
├── (home)/          # Home page layout group
├── docs/            # Documentation pages with [[...slug]] routing
├── api/             # API routes (chat, search, proxy)
└── layout.config.tsx # Shared navigation and branding

content/docs/        # MDX documentation content
├── meta.json        # Navigation structure for all sections
├── getting-started/ # Onboarding documentation
├── core/           # PRISM-ICL Framework concepts
├── agents/         # AI agents documentation
├── integrations/   # Platform integrations
├── lifecycles/     # 11 Lifecycle stages
├── enterprise/     # Enterprise features
├── api/           # API reference
└── examples/      # Implementation examples

components/
├── fumadocs/      # Documentation-specific components
└── ui/            # Reusable UI components with Radix UI

scripts/           # Build automation and tooling
```

### Key Files
- `content/docs/meta.json`: Defines complete navigation structure
- `app/layout.config.tsx`: Shared navigation links and branding
- `scripts/lint.mts`: Comprehensive link validation across documentation
- `app/api/chat/route.ts`: AI chat with MCP client integration

## AI Integration Architecture

### Chat System
The platform features multiple AI engines:
- **AI SDK Integration**: Primary chat via Vercel AI SDK with OpenAI
- **MCP Client**: Model Context Protocol integration at `https://mcp-on-vercel.vercel.app/sse`
- **Inkeep AI**: Alternative AI search provider
- **Orama Cloud**: Search indexing and AI-powered search

### API Routes
- `/api/chat`: Streaming AI chat with MCP tool integration
- `/api/search`: Multi-engine documentation search
- `/api/proxy`: External API proxy for integrations

## Content Management

### Documentation Structure
Content is organized around MOSAIC Platform concepts:
- **PRISM-ICL Framework**: 8 cognitive domains quantum meta-architecture
- **11 Lifecycles**: Discovery → Conception → Planning → Development → Testing → Deployment → Monitoring → Scaling → Optimization → Evolution → Sunset
- **AI Agents**: Flow Guardian, Code Reviewer, Wellness Advocate
- **Integrations**: Home Assistant, Claude Code, DotAI, HAKit Dashboards

### MDX Processing
- Uses Fumadocs MDX with custom extensions
- Supports React components within documentation
- Automatic table of contents generation
- Code highlighting with Shiki
- Math support with KaTeX

## Build Process

### Pre-build (`scripts/pre-build.mts`)
TypeScript processing for content preparation

### Main Build
Next.js build with Fumadocs MDX processing and optimization

### Post-build (`scripts/post-build.mts`)
Cleanup and optimization of generated assets

### Link Validation (`scripts/lint.mts`)
- Scans all MDX files in `content/docs/`
- Validates internal links and anchors
- Checks table of contents hash links
- Reports broken links and missing pages

## Development Guidelines

### Adding Documentation
1. Create MDX files in appropriate `content/docs/` subdirectory
2. Update `content/docs/meta.json` to include in navigation
3. Run `pnpm lint` to validate links and structure
4. Test AI search integration with new content

### Component Development
- Follow existing patterns in `components/fumadocs/` and `components/ui/`
- Use Radix UI primitives for accessibility
- Implement TypeScript interfaces for all props
- Use Tailwind CSS classes following existing conventions

### API Integration
- Follow MCP (Model Context Protocol) patterns for tool integration
- Implement streaming responses for AI interactions
- Use proper error handling for external service calls
- Maintain backward compatibility with existing AI engines

## External Integrations

### Links
- **GitHub**: https://github.com/alias-organization/mosaic
- **Discord**: https://discord.gg/mosaic-synthesis
- **Enterprise**: /enterprise section for business features

### Environment Setup
- Uses `dotenv` with `../../.env` for shared environment variables
- MCP integration requires external service configuration
- AI providers require proper API key configuration