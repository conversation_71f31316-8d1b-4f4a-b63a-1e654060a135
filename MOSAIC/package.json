{"name": "@alias/mosaic", "version": "1.0.0", "description": "Modular Architecture for Software & AI Integration Cohesion", "keywords": ["ai", "architecture", "automation", "agents", "mosaic", "prism-icl", "gitlab", "nextjs"], "homepage": "https://mosaic.alias.dev", "repository": {"type": "git", "url": "********************:alias/mosaic.git"}, "bugs": {"url": "https://gitlab.alias.dev/alias/mosaic/issues"}, "license": "MIT", "author": {"name": "ALIAS Team", "email": "<EMAIL>", "url": "https://alias.dev"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Founder & CTO"}], "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./agents": {"import": "./dist/agents/index.js", "types": "./dist/agents/index.d.ts"}, "./core": {"import": "./dist/core/index.js", "types": "./dist/core/index.d.ts"}, "./lifecycles": {"import": "./dist/lifecycles/index.js", "types": "./dist/lifecycles/index.d.ts"}}, "files": ["dist", "README.md", "LICENSE", "CHANGELOG.md"], "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "next dev", "build": "next build && npm run build:lib", "build:lib": "tsup", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "lint:check": "eslint . --ext .ts,.tsx,.js,.jsx", "type-check": "tsc --noEmit", "test": "npm run test:unit && npm run test:integration", "test:unit": "vitest run src", "test:unit:watch": "vitest src", "test:integration": "vitest run tests/integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:performance": "k6 run tests/performance/load-test.js", "test:security": "npm audit && npm run test:security:snyk", "test:security:snyk": "snyk test", "validate": "npm run lint:check && npm run type-check && npm run test", "validate:lifecycles": "node scripts/validate-lifecycles.js", "validate:agents": "node scripts/validate-agents.js", "validate:architecture": "node scripts/validate-architecture.js", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rimraf dist .next coverage playwright-report test-results", "postinstall": "husky install", "prepare": "npm run build:lib", "prepack": "npm run clean && npm run build:lib", "release": "semantic-release", "release:dry": "semantic-release --dry-run", "docs:dev": "fumadocs dev", "docs:build": "fumadocs build", "docs:start": "fumadocs start", "agents:orchestrate": "node scripts/agents/orchestrate.js", "agents:status": "node scripts/agents/status.js", "agents:deploy": "node scripts/agents/deploy.js", "lifecycle:init": "node scripts/lifecycle/init.js", "lifecycle:advance": "node scripts/lifecycle/advance.js", "lifecycle:status": "node scripts/lifecycle/status.js", "metrics:collect": "node scripts/metrics/collect.js", "metrics:report": "node scripts/metrics/report.js", "metrics:dashboard": "node scripts/metrics/dashboard.js", "deploy:staging": "node scripts/deployment/staging.js", "deploy:production": "node scripts/deployment/production.js", "deploy:rollback": "node scripts/deployment/rollback.js", "infrastructure:provision": "node scripts/infrastructure/provision.js", "infrastructure:status": "node scripts/infrastructure/status.js", "infrastructure:monitor": "node scripts/infrastructure/monitor.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@convex-dev/react": "^1.13.2", "@hono/node-server": "^1.11.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-slot": "^1.0.2", "@vercel/ai": "^3.3.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "convex": "^1.13.2", "fumadocs-core": "^13.2.1", "fumadocs-ui": "^13.2.1", "hono": "^4.4.12", "lucide-react": "^0.400.0", "next": "15.1.0", "openai": "^4.56.0", "react": "19.0.0", "react-dom": "19.0.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@eslint/js": "^9.7.0", "@playwright/test": "^1.45.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.0.3", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.0.11", "jsdom": "^24.1.0", "lint-staged": "^15.2.7", "postcss": "^8.4.39", "prettier": "^3.3.2", "rimraf": "^5.0.7", "semantic-release": "^24.0.0", "snyk": "^1.1292.0", "tailwindcss": "^3.4.4", "tsup": "^8.1.0", "typescript": "^5.5.3", "vitest": "^1.6.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [2, "always", ["feat", "fix", "docs", "style", "refactor", "perf", "test", "build", "ci", "chore", "revert", "agent", "lifecycle"]]}}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/gitlab", "@semantic-release/git"]}, "publishConfig": {"registry": "https://npm.alias.dev", "access": "restricted"}}