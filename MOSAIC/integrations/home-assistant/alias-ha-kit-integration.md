# ALIAS Home Assistant Kit Integration for MOSAIC
**Custom React Dashboard Components for Work-Life Synthesis**

## 🎯 Overview

The ALIAS Home Assistant Kit (`@hakit/core` + `@hakit/components`) provides the perfect foundation for creating custom React dashboards that seamlessly integrate with MOSAIC's work-life synthesis philosophy. This integration enables us to deploy specialized Home Assistant interfaces that understand developer workflows, biometric data, and MOSAIC agent states.

## 🏗️ Architecture Integration

### MOSAIC × HAKit Component Stack

```mermaid
graph TB
    A[MOSAIC Platform] --> B[Home Assistant Core]
    B --> C[HAKit React Components]
    C --> D[Custom MOSAIC Cards]
    
    E[PRISM-ICL Agents] --> F[Agent Status Cards]
    G[Lifecycle Management] --> H[Flow State Cards]
    I[Biometric Data] --> J[Wellness Cards]
    
    D --> K[Developer Dashboard]
    F --> K
    H --> K
    J --> K
    
    K --> L[Work-Life Synthesis UI]
```

### Custom MOSAIC Components

Based on HAKit's existing card library, we'll create specialized components:

```typescript
interface MOSAICCardLibrary {
  // Flow and Focus Management
  FlowStateCard: "Visual flow state indicator with protection controls",
  DeepWorkCard: "Deep work session timer and environment controls",
  InterruptionShieldCard: "Family-friendly interruption management",
  
  // Agent Integration
  AgentStatusCard: "Live MOSAIC agent status and controls",
  LifecycleProgressCard: "Current project lifecycle stage visualization",
  AgentChatCard: "Direct communication with MOSAIC agents",
  
  // Wellness and Biometrics
  DeveloperWellnessCard: "Comprehensive health tracking for developers",
  PostureMonitorCard: "Sitting/standing reminders and ergonomics",
  EnergyLevelCard: "Circadian rhythm and energy optimization",
  
  // Family Integration
  FamilyStatusCard: "Household coordination and work-life balance",
  WorkScheduleCard: "Shared calendar and availability indicator",
  AchievementCard: "Celebrate coding wins with family"
}
```

## 🚀 Implementation Strategy

### Phase 1: MOSAIC Core Cards

Create fundamental cards for developer workflow:

```typescript
// src/Cards/FlowStateCard/index.tsx
import { Card, useEntity, useService } from '@hakit/core';

interface FlowStateCardProps {
  flowGuardianEntity: string;
  workspaceScene: string;
  className?: string;
}

export function FlowStateCard({ 
  flowGuardianEntity, 
  workspaceScene,
  className 
}: FlowStateCardProps) {
  const flowGuardian = useEntity(flowGuardianEntity);
  const sceneService = useService();
  
  const activateDeepWork = () => {
    sceneService.callService('scene', 'turn_on', {
      entity_id: workspaceScene
    });
    
    // Trigger MOSAIC flow guardian
    sceneService.callService('automation', 'trigger', {
      entity_id: 'automation.mosaic_deep_work_protection'
    });
  };
  
  const currentState = flowGuardian?.state;
  const isDeepWork = currentState === 'deep_work';
  
  return (
    <Card
      className={className}
      header="🧠 Flow State"
      css={{
        background: isDeepWork 
          ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
          : 'var(--ha-card-background)'
      }}
    >
      <div css={{ padding: '1rem' }}>
        <div css={{ 
          fontSize: '2rem', 
          fontWeight: 'bold',
          color: isDeepWork ? 'white' : 'var(--primary-text-color)'
        }}>
          {isDeepWork ? 'Deep Work Active' : 'Available'}
        </div>
        
        {isDeepWork && (
          <div css={{ color: 'rgba(255,255,255,0.8)', marginTop: '0.5rem' }}>
            Protected until {flowGuardian?.attributes?.end_time}
          </div>
        )}
        
        <div css={{ marginTop: '1rem', display: 'flex', gap: '0.5rem' }}>
          <button 
            onClick={activateDeepWork}
            disabled={isDeepWork}
            css={{
              padding: '0.5rem 1rem',
              borderRadius: '8px',
              border: 'none',
              background: isDeepWork ? 'rgba(255,255,255,0.2)' : 'var(--primary-color)',
              color: isDeepWork ? 'white' : 'var(--text-primary-color)',
              cursor: isDeepWork ? 'not-allowed' : 'pointer'
            }}
          >
            {isDeepWork ? 'Protected' : 'Activate Deep Work'}
          </button>
        </div>
      </div>
    </Card>
  );
}
```

### Phase 2: Agent Integration Cards

```typescript
// src/Cards/AgentStatusCard/index.tsx
export function AgentStatusCard({ agents }: { agents: string[] }) {
  const agentStates = agents.map(agent => useEntity(agent));
  
  return (
    <Card header="🤖 MOSAIC Agents">
      <div css={{ display: 'grid', gap: '1rem', padding: '1rem' }}>
        {agentStates.map((agent, index) => (
          <div key={agents[index]} css={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '0.5rem',
            borderRadius: '8px',
            background: 'var(--secondary-background-color)'
          }}>
            <span>{agent?.attributes?.friendly_name}</span>
            <div css={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: agent?.state === 'active' 
                ? 'var(--success-color)' 
                : 'var(--warning-color)'
            }} />
          </div>
        ))}
      </div>
    </Card>
  );
}
```

### Phase 3: Wellness Integration

```typescript
// src/Cards/DeveloperWellnessCard/index.tsx
export function DeveloperWellnessCard({
  postureEntity,
  stressEntity,
  hydrationEntity
}: {
  postureEntity: string;
  stressEntity: string;
  hydrationEntity: string;
}) {
  const posture = useEntity(postureEntity);
  const stress = useEntity(stressEntity);
  const hydration = useEntity(hydrationEntity);
  
  const suggestions = [];
  
  if (parseInt(posture?.state) > 45) {
    suggestions.push("🚶 Time to stand and stretch");
  }
  
  if (parseInt(stress?.state) > 70) {
    suggestions.push("🧘 Consider a breathing break");
  }
  
  if (parseInt(hydration?.state) < 30) {
    suggestions.push("💧 Hydration reminder");
  }
  
  return (
    <Card header="💚 Developer Wellness">
      <div css={{ padding: '1rem' }}>
        <div css={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
          <WellnessMetric 
            label="Posture" 
            value={posture?.state} 
            unit="min sitting"
            color={parseInt(posture?.state) > 45 ? 'var(--error-color)' : 'var(--success-color)'}
          />
          <WellnessMetric 
            label="Stress" 
            value={stress?.state} 
            unit="%"
            color={parseInt(stress?.state) > 70 ? 'var(--error-color)' : 'var(--success-color)'}
          />
          <WellnessMetric 
            label="Hydration" 
            value={hydration?.state} 
            unit="%"
            color={parseInt(hydration?.state) < 30 ? 'var(--warning-color)' : 'var(--success-color)'}
          />
        </div>
        
        {suggestions.length > 0 && (
          <div css={{ 
            background: 'var(--info-color)',
            color: 'white',
            padding: '0.75rem',
            borderRadius: '8px'
          }}>
            <strong>Wellness Suggestions:</strong>
            <ul css={{ margin: '0.5rem 0 0 0', paddingLeft: '1.5rem' }}>
              {suggestions.map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </Card>
  );
}

function WellnessMetric({ label, value, unit, color }: {
  label: string;
  value: string;
  unit: string;
  color: string;
}) {
  return (
    <div css={{ textAlign: 'center' }}>
      <div css={{ fontSize: '1.5rem', fontWeight: 'bold', color }}>{value}</div>
      <div css={{ fontSize: '0.7rem', opacity: 0.7 }}>{unit}</div>
      <div css={{ fontSize: '0.8rem', marginTop: '0.25rem' }}>{label}</div>
    </div>
  );
}
```

## 📦 Project Structure

### MOSAIC HAKit Project Layout

```
mosaic-hakit-dashboard/
├── package.json                    # HAKit + MOSAIC dependencies
├── src/
│   ├── App.tsx                    # Main dashboard application
│   ├── layouts/
│   │   ├── DeveloperWorkspace.tsx # Primary work dashboard
│   │   ├── FamilyDashboard.tsx    # Family coordination view
│   │   └── WellnessDashboard.tsx  # Health and wellness focus
│   │
│   ├── cards/                     # Custom MOSAIC cards
│   │   ├── FlowStateCard/
│   │   ├── AgentStatusCard/
│   │   ├── LifecycleProgressCard/
│   │   ├── DeveloperWellnessCard/
│   │   ├── FamilyCoordinationCard/
│   │   └── AchievementCard/
│   │
│   ├── hooks/                     # Custom MOSAIC hooks
│   │   ├── useMOSAICAgent.ts     # Agent state management
│   │   ├── useLifecycleState.ts  # Lifecycle tracking
│   │   └── useWellnessMetrics.ts # Health data aggregation
│   │
│   ├── utils/
│   │   ├── mosaicApi.ts          # MOSAIC platform integration
│   │   └── biometricHelpers.ts   # Health data processing
│   │
│   └── themes/
│       ├── developer.ts          # Developer-focused theme
│       ├── family.ts            # Family-friendly theme
│       └── focus.ts             # Deep work theme
│
├── public/
│   ├── icons/                   # MOSAIC and custom icons
│   └── sounds/                  # Notification sounds
│
└── deploy/
    ├── home-assistant-addon/    # HA addon configuration
    └── docker/                  # Containerized deployment
```

## 🔧 Custom MOSAIC Hooks

### Agent Integration Hook

```typescript
// src/hooks/useMOSAICAgent.ts
import { useEntity, useService } from '@hakit/core';
import { useCallback } from 'react';

interface MOSAICAgent {
  id: string;
  name: string;
  domain: string;
  state: 'active' | 'idle' | 'busy' | 'error';
  capabilities: string[];
  lastAction?: string;
}

export function useMOSAICAgent(agentEntityId: string): {
  agent: MOSAICAgent | null;
  executeCommand: (command: string, params?: any) => Promise<void>;
  getStatus: () => string;
} {
  const entity = useEntity(agentEntityId);
  const service = useService();
  
  const agent: MOSAICAgent | null = entity ? {
    id: entity.entity_id,
    name: entity.attributes?.friendly_name || entity.entity_id,
    domain: entity.attributes?.domain || 'unknown',
    state: entity.state as MOSAICAgent['state'],
    capabilities: entity.attributes?.capabilities || [],
    lastAction: entity.attributes?.last_action
  } : null;
  
  const executeCommand = useCallback(async (command: string, params?: any) => {
    await service.callService('script', 'mosaic_agent_command', {
      agent_id: agentEntityId,
      command,
      parameters: params
    });
  }, [agentEntityId, service]);
  
  const getStatus = useCallback(() => {
    if (!agent) return 'Unknown';
    
    switch (agent.state) {
      case 'active': return '🟢 Active';
      case 'busy': return '🟡 Processing';
      case 'idle': return '⚪ Idle';
      case 'error': return '🔴 Error';
      default: return '❓ Unknown';
    }
  }, [agent]);
  
  return { agent, executeCommand, getStatus };
}
```

### Wellness Metrics Hook

```typescript
// src/hooks/useWellnessMetrics.ts
export function useWellnessMetrics() {
  const heartRate = useEntity('sensor.user_heart_rate');
  const stressLevel = useEntity('sensor.user_stress_level');
  const sittingTime = useEntity('sensor.sitting_time_today');
  const hydrationLevel = useEntity('sensor.hydration_level');
  
  const getWellnessScore = useCallback(() => {
    const metrics = [
      parseInt(heartRate?.state) || 70,
      100 - (parseInt(stressLevel?.state) || 30),
      Math.max(0, 100 - (parseInt(sittingTime?.state) || 0) / 5),
      parseInt(hydrationLevel?.state) || 70
    ];
    
    return Math.round(metrics.reduce((a, b) => a + b, 0) / metrics.length);
  }, [heartRate, stressLevel, sittingTime, hydrationLevel]);
  
  const getSuggestions = useCallback(() => {
    const suggestions: string[] = [];
    
    if (parseInt(sittingTime?.state) > 45) {
      suggestions.push("🚶 Take a walking break");
    }
    
    if (parseInt(stressLevel?.state) > 70) {
      suggestions.push("🧘 Practice deep breathing");
    }
    
    if (parseInt(hydrationLevel?.state) < 30) {
      suggestions.push("💧 Drink some water");
    }
    
    return suggestions;
  }, [sittingTime, stressLevel, hydrationLevel]);
  
  return {
    metrics: {
      heartRate: parseInt(heartRate?.state) || 0,
      stressLevel: parseInt(stressLevel?.state) || 0,
      sittingTime: parseInt(sittingTime?.state) || 0,
      hydrationLevel: parseInt(hydrationLevel?.state) || 0
    },
    wellnessScore: getWellnessScore(),
    suggestions: getSuggestions()
  };
}
```

## 🎨 Custom Themes for MOSAIC

### Developer Focus Theme

```typescript
// src/themes/developer.ts
export const developerTheme = {
  '--primary-color': '#667eea',
  '--accent-color': '#764ba2',
  '--card-background-color': '#1e1e1e',
  '--primary-text-color': '#ffffff',
  '--secondary-text-color': '#b3b3b3',
  '--success-color': '#4caf50',
  '--warning-color': '#ff9800',
  '--error-color': '#f44336',
  '--info-color': '#2196f3',
  
  // Flow state colors
  '--flow-active': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  '--flow-inactive': 'var(--card-background-color)',
  
  // Agent status colors
  '--agent-active': '#4caf50',
  '--agent-busy': '#ff9800',
  '--agent-idle': '#9e9e9e',
  '--agent-error': '#f44336',
  
  // Wellness colors
  '--wellness-excellent': '#4caf50',
  '--wellness-good': '#8bc34a',
  '--wellness-fair': '#ff9800',
  '--wellness-poor': '#f44336'
};
```

## 🚀 Deployment Options

### Option 1: Home Assistant Add-on

```yaml
# deploy/home-assistant-addon/config.yaml
name: "MOSAIC Dashboard"
description: "Custom MOSAIC work-life synthesis dashboard"
version: "1.0.0"
slug: "mosaic_dashboard"
init: false
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
ports:
  3000/tcp: 3000
webui: "http://[HOST]:[PORT:3000]"
panel_icon: "mdi:brain"
panel_title: "MOSAIC"
startup: application
boot: auto
options:
  mosaic_api_url: "https://mosaic.internal.company.com"
  theme: "developer"
  enable_biometrics: true
  enable_family_features: true
schema:
  mosaic_api_url: "str"
  theme: "list(developer|family|minimal)"
  enable_biometrics: "bool"
  enable_family_features: "bool"
```

### Option 2: Docker Deployment

```dockerfile
# deploy/docker/Dockerfile
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY dist/ ./dist/
COPY public/ ./public/

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

EXPOSE 3000

CMD ["npm", "start"]
```

### Option 3: Static Site Deployment

```bash
# Build for static deployment
npm run build

# Deploy to nginx/apache/cloudflare pages
# Dashboard accessible at: https://mosaic.yourdomain.com
```

## 📋 Installation Guide

### Quick Start with Create HAKit

```bash
# Create new MOSAIC dashboard
npm create hakit@latest mosaic-dashboard

cd mosaic-dashboard

# Install MOSAIC-specific dependencies
npm install @mosaic/hakit-cards @mosaic/wellness-hooks

# Configure for MOSAIC integration
npm run setup:mosaic
```

### Manual Installation

```bash
# Create React project with HAKit
git clone https://github.com/danmarauda/alias-ha-kit.git
cd alias-ha-kit

# Install dependencies
npm install

# Add MOSAIC components
npm install @hakit/core @hakit/components
npm install @emotion/react @emotion/styled

# Create MOSAIC configuration
cp .env.example .env
# Edit .env with your Home Assistant and MOSAIC details
```

### Environment Configuration

```bash
# .env
VITE_HA_URL=http://homeassistant.local:8123
VITE_HA_TOKEN=your_home_assistant_token

# MOSAIC Integration
VITE_MOSAIC_API_URL=https://mosaic.internal.company.com
VITE_MOSAIC_API_TOKEN=your_mosaic_token

# Feature Flags
VITE_ENABLE_BIOMETRICS=true
VITE_ENABLE_AGENT_CHAT=true
VITE_ENABLE_FAMILY_FEATURES=true

# Theme Configuration
VITE_DEFAULT_THEME=developer
VITE_ENABLE_THEME_SWITCHING=true
```

## 🔮 Future Enhancements

### Planned Features

1. **Voice Integration**
   - "Hey MOSAIC, activate deep work mode"
   - Voice status updates for flow state
   - Family-friendly voice commands

2. **Mobile Companion**
   - React Native version for mobile devices
   - Push notifications for flow state changes
   - Mobile wellness tracking

3. **AR/VR Integration**
   - Virtual workspace overlays
   - 3D visualization of agent states
   - Immersive flow environments

4. **Advanced Analytics**
   - Developer productivity correlations
   - Wellness trend analysis
   - Family harmony metrics

## 📞 Support and Resources

### Getting Help
- **HAKit Documentation**: [Documentation](https://shannonhochkins.github.io/ha-component-kit)
- **MOSAIC Community**: #mosaic-hakit on Discord
- **GitHub Issues**: Custom component requests and bug reports

### Contributing
- Create custom cards for the MOSAIC ecosystem
- Share themes and layouts with the community
- Contribute to the HAKit upstream project

---

**MOSAIC × HAKit Integration Status**: 🟢 Ready for Development

This integration transforms Home Assistant from a simple home automation platform into a comprehensive work-life synthesis command center, perfectly aligned with MOSAIC's philosophy of seamless human-AI collaboration.

*"Your home becomes an extension of your mind, and your mind becomes an extension of MOSAIC."*