# MOSAIC × Home Assistant Installation Guide
**Your Complete Setup Guide for Work-Life Synthesis**

## 🎯 Prerequisites

### Hardware Requirements

#### Minimum Setup
```yaml
home_assistant:
  device: "Raspberry Pi 4 (4GB)"
  storage: "32GB Class 10 SD Card"
  network: "Ethernet recommended"
  
mosaic_bridge:
  memory: "2GB RAM"
  cpu: "ARM Cortex-A72 or equivalent"
  network: "Local network access to MOSAIC platform"

essential_devices:
  lighting: "At least 3 smart bulbs/switches"
  climate: "Smart thermostat or AC control"
  presence: "Motion sensors for main work areas"
  audio: "Smart speaker or media player"
```

#### Recommended Setup
```yaml
home_assistant:
  device: "Intel NUC or Home Assistant Yellow"
  storage: "128GB NVMe SSD"
  network: "Gigabit Ethernet"
  backup: "External USB storage"

extended_devices:
  biometric: "Smart watch or fitness tracker"
  environmental: "Air quality, light, noise sensors"
  security: "Door locks, cameras (privacy-first)"
  ergonomics: "Smart desk, chair sensors"
  family: "Tablets for status displays"
```

### Software Prerequisites

```bash
# Home Assistant Core 2024.1+
# MOSAIC Platform access (internal company deployment)
# Mosquitto MQTT broker (for real-time communication)
# ESPHome (for custom sensors, optional)
```

## 🚀 Installation Process

### Step 1: Home Assistant Base Setup

#### Option A: Home Assistant OS (Recommended)
```bash
# Download Home Assistant OS image
wget https://github.com/home-assistant/operating-system/releases/latest/download/haos_rpi4-64-11.1.img.xz

# Flash to SD card using Raspberry Pi Imager
# Insert SD card and power on device
# Navigate to http://homeassistant.local:8123
# Complete initial setup wizard
```

#### Option B: Docker Installation
```bash
# Create directory structure
mkdir -p ~/homeassistant/config

# Run Home Assistant container
docker run -d \
  --name homeassistant \
  --privileged \
  --restart=unless-stopped \
  -e TZ=America/New_York \
  -v ~/homeassistant/config:/config \
  -p 8123:8123 \
  ghcr.io/home-assistant/home-assistant:stable
```

### Step 2: Install HACS (Home Assistant Community Store)

```bash
# Download HACS
wget -O - https://get.hacs.xyz | bash -

# Restart Home Assistant
# Go to Configuration > Integrations
# Add HACS integration
# Authenticate with GitHub
```

### Step 3: Install MOSAIC Bridge Add-on

#### Add MOSAIC Repository
```yaml
# In Home Assistant:
# Configuration > Add-ons > Add-on Store
# Three dots menu > Repositories
# Add: https://github.com/alias/mosaic-home-assistant
```

#### Install MOSAIC Bridge
```bash
# Through UI or via CLI:
ha addons install mosaic_bridge

# Configure the add-on
ha addons config mosaic_bridge \
  --options '{
    "mosaic_url": "https://mosaic.internal.company.com",
    "api_token": "your_mosaic_api_token",
    "sync_interval": 30,
    "enabled_agents": ["flow_guardian", "wellness_advocate", "rhythm_keeper"]
  }'

# Start the add-on
ha addons start mosaic_bridge
```

### Step 4: Configure Core Integrations

#### MQTT Broker Setup
```yaml
# configuration.yaml
mqtt:
  broker: core-mosquitto
  port: 1883
  username: !secret mqtt_username
  password: !secret mqtt_password
  discovery: true
  birth_message:
    topic: "homeassistant/status"
    payload: "online"
  will_message:
    topic: "homeassistant/status"
    payload: "offline"
```

#### MOSAIC Integration Configuration
```yaml
# configuration.yaml
mosaic:
  api:
    url: !secret mosaic_api_url
    token: !secret mosaic_api_token
    timeout: 30
  
  agents:
    sync_interval: 30
    enabled_agents:
      - flow_guardian
      - wellness_advocate
      - rhythm_keeper
      - joy_catalyst
    
    entity_mapping:
      flow_guardian:
        - current_state
        - energy_level
        - next_transition
      wellness_advocate:
        - posture_score
        - break_needed
        - stress_level
```

## 🏠 Device Setup and Configuration

### Smart Lighting Setup

#### Philips Hue Integration
```yaml
# configuration.yaml
hue:
  bridges:
    - host: *************
      allow_unreachable: true
      allow_hue_groups: true

# Create lighting scenes
scene:
  - name: "Deep Work"
    entities:
      light.office_desk_lamp:
        state: on
        brightness: 200
        color_temp: 4000
      light.office_ceiling:
        state: on
        brightness: 150
        color_temp: 4000
  
  - name: "Video Call"
    entities:
      light.office_desk_lamp:
        state: on
        brightness: 255
        color_temp: 5000
      light.office_ceiling:
        state: on
        brightness: 100
        color_temp: 5000
```

#### Circadian Rhythm Lighting
```yaml
# Install Adaptive Lighting via HACS
# Configuration > Integrations > Add Integration > Adaptive Lighting

adaptive_lighting:
  - name: "Office Circadian"
    lights:
      - light.office_desk_lamp
      - light.office_ceiling
    initial_transition: 1
    transition: 30
    prefer_rgb_color: false
    separate_turn_on_commands: true
```

### Climate Control Integration

#### Smart Thermostat Setup
```yaml
# For Nest, Ecobee, or generic thermostat
climate:
  - platform: generic_thermostat
    name: "Office Climate"
    heater: switch.office_heater
    target_sensor: sensor.office_temperature
    min_temp: 65
    max_temp: 80
    ac_mode: false
    target_temp: 70
    cold_tolerance: 0.3
    hot_tolerance: 0

# MOSAIC-controlled climate automation
automation:
  - alias: "MOSAIC Climate Control"
    trigger:
      - platform: state
        entity_id: mosaic.wellness_advocate
        attribute: "comfort_temperature"
    action:
      - service: climate.set_temperature
        target:
          entity_id: climate.office_climate
        data:
          temperature: "{{ trigger.to_state.attributes.comfort_temperature }}"
```

### Presence and Motion Detection

#### ESPHome Motion Sensor
```yaml
# office_sensor.yaml for ESPHome
esphome:
  name: office-sensor
  platform: ESP32
  board: esp32dev

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

api:
  password: !secret api_password

ota:
  password: !secret ota_password

binary_sensor:
  - platform: gpio
    pin: GPIO14
    name: "Office Motion"
    device_class: motion
    
sensor:
  - platform: bme280
    temperature:
      name: "Office Temperature"
    humidity:
      name: "Office Humidity"
    pressure:
      name: "Office Pressure"
    address: 0x76
    update_interval: 60s
```

### Biometric Integration

#### Apple Health Integration
```yaml
# Via iOS Companion App
# Configuration > Companion App > Sensors
# Enable: Heart Rate, Steps, Sleep Analysis

sensor:
  - platform: template
    sensors:
      stress_level:
        friendly_name: "User Stress Level"
        value_template: >
          {% set hr = states('sensor.heart_rate') | int(0) %}
          {% set hrv = states('sensor.heart_rate_variability') | int(50) %}
          {% if hr > 0 %}
            {{ ((hr - 60) / 60 * 50 + (50 - hrv) / 50 * 50) | round(0) }}
          {% else %}
            50
          {% endif %}
        unit_of_measurement: "%"
```

#### Smart Watch Integration
```yaml
# For Fitbit, Garmin, or other devices via API
rest:
  - resource: https://api.fitbit.com/1/user/-/activities/heart/date/today/1d.json
    headers:
      Authorization: !secret fitbit_bearer_token
    sensor:
      - name: "Fitbit Heart Rate"
        value_template: "{{ value_json.activities-heart[0].value.restingHeartRate }}"
      - name: "Fitbit Active Minutes"
        value_template: "{{ value_json.activities-heart[0].value.fatBurnMinutes }}"
```

## 🤖 Agent Integration Setup

### Flow Guardian Configuration

```yaml
# Create input helpers for Flow Guardian
input_boolean:
  flow_protection_active:
    name: "Flow Protection Active"
    icon: mdi:brain
  
  natural_flow_detected:
    name: "Natural Flow State Detected"
    icon: mdi:meditation

input_select:
  flow_mode:
    name: "Flow Mode"
    options:
      - "Available"
      - "Focused"
      - "Deep Work"
      - "Creative"
      - "Break"
    initial: "Available"

# MOSAIC Flow Guardian entity
mosaic:
  sensors:
    - name: "Flow Guardian State"
      state_topic: "mosaic/agents/flow_guardian/state"
      value_template: "{{ value_json.current_state }}"
      json_attributes_topic: "mosaic/agents/flow_guardian/attributes"
```

### Wellness Advocate Setup

```yaml
# Health tracking sensors
sensor:
  - platform: template
    sensors:
      sitting_time_today:
        friendly_name: "Sitting Time Today"
        value_template: >
          {% set start = now().replace(hour=0, minute=0, second=0) %}
          {% set sitting_states = states.binary_sensor.chair_occupancy.history(start) %}
          {% set sitting_time = sitting_states | selectattr('state', 'eq', 'on') | list | length * 5 %}
          {{ sitting_time }}
        unit_of_measurement: "minutes"

# Wellness reminders
automation:
  - alias: "MOSAIC Wellness Check"
    trigger:
      - platform: time_pattern
        minutes: "/30"
    condition:
      - condition: state
        entity_id: binary_sensor.chair_occupancy
        state: "on"
        for: "00:45:00"
    action:
      - service: notify.user
        data:
          title: "Wellness Check"
          message: "You've been sitting for 45 minutes. Time for a movement break?"
          data:
            actions:
              - action: "take_break"
                title: "Take Break"
              - action: "remind_later"
                title: "5 More Minutes"
```

### Rhythm Keeper Integration

```yaml
# Circadian rhythm tracking
sensor:
  - platform: template
    sensors:
      current_circadian_phase:
        friendly_name: "Current Circadian Phase"
        value_template: >
          {% set hour = now().hour %}
          {% if hour >= 6 and hour < 10 %}
            Morning Rise
          {% elif hour >= 10 and hour < 14 %}
            Peak Performance
          {% elif hour >= 14 and hour < 18 %}
            Afternoon Dip
          {% elif hour >= 18 and hour < 22 %}
            Evening Recovery
          {% else %}
            Night Rest
          {% endif %}

# Rhythm-based automations
automation:
  - alias: "MOSAIC Rhythm Optimization"
    trigger:
      - platform: state
        entity_id: sensor.current_circadian_phase
    action:
      - choose:
          - conditions:
              - condition: template
                value_template: "{{ trigger.to_state.state == 'Peak Performance' }}"
            sequence:
              - service: notify.user
                data:
                  title: "Peak Performance Time"
                  message: "Your circadian rhythm suggests now is optimal for challenging tasks!"
```

## 🔧 Automation Setup

### Import MOSAIC Automations

```bash
# Download automation packages
cd /config
wget https://github.com/alias/mosaic-home-assistant/releases/latest/download/mosaic-automations.zip
unzip mosaic-automations.zip

# Restart Home Assistant to load new automations
ha core restart
```

### Custom Automation Examples

#### Morning Work Transition
```yaml
automation:
  - alias: "MOSAIC Morning Work Transition"
    trigger:
      - platform: state
        entity_id: person.user
        to: "home"
      - platform: time
        at: input_datetime.work_start_time
    condition:
      - condition: time
        weekday:
          - mon
          - tue
          - wed
          - thu
          - fri
    action:
      - service: scene.turn_on
        target:
          entity_id: scene.morning_work_mode
      
      - service: media_player.play_media
        target:
          entity_id: media_player.office_speakers
        data:
          media_content_id: "spotify:playlist:37i9dQZF1DX0XUsuxWHRQd"
          media_content_type: "music"
      
      - service: notify.user
        data:
          title: "Good Morning!"
          message: "Your workspace is ready. Let's create something amazing today!"
```

## 🔒 Security and Privacy Configuration

### Network Security
```yaml
# Secure MQTT configuration
mqtt:
  broker: core-mosquitto
  port: 8883
  certificate: /ssl/ca.crt
  client_cert: /ssl/client.crt
  client_key: /ssl/client.key
  tls_insecure: false

# API security
http:
  use_x_forwarded_for: true
  trusted_proxies:
    - ***********/24
  ip_ban_enabled: true
  login_attempts_threshold: 5
```

### Privacy Controls
```yaml
# Guest mode for privacy
input_boolean:
  guest_mode:
    name: "Guest Mode"
    icon: mdi:account-multiple

automation:
  - alias: "Activate Guest Mode"
    trigger:
      - platform: state
        entity_id: input_boolean.guest_mode
        to: "on"
    action:
      - service: camera.turn_off
        target:
          entity_id: all
      - service: automation.turn_off
        target:
          entity_id:
            - automation.mosaic_presence_tracking
            - automation.mosaic_biometric_monitoring
```

## 📱 Mobile App Configuration

### iOS/Android Companion App Setup

```yaml
# Enable location tracking
device_tracker:
  - platform: icloud
    username: !secret icloud_username
    password: !secret icloud_password

# Mobile app notifications
notify:
  - platform: group
    name: "all_phones"
    services:
      - service: mobile_app_user_iphone
      - service: mobile_app_partner_android

# Actionable notifications
automation:
  - alias: "MOSAIC Mobile Interactions"
    trigger:
      - platform: event
        event_type: mobile_app_notification_action
    action:
      - choose:
          - conditions:
              - condition: template
                value_template: "{{ trigger.event.data.action == 'start_deep_work' }}"
            sequence:
              - service: input_select.select_option
                target:
                  entity_id: input_select.flow_mode
                data:
                  option: "Deep Work"
```

## 🎯 Testing and Validation

### Test Your Installation

```bash
# Check MOSAIC Bridge status
ha addons logs mosaic_bridge

# Test agent communication
ha states get mosaic.flow_guardian

# Validate automations
ha automation trigger automation.mosaic_deep_work_protection

# Test mobile notifications
ha services call notify.mobile_app_user_phone \
  --data '{"title": "Test", "message": "MOSAIC integration test"}'
```

### Validation Checklist

- [ ] MOSAIC Bridge connects successfully
- [ ] Agent states sync every 30 seconds
- [ ] Deep work mode activates environment changes
- [ ] Mobile notifications work bidirectionally
- [ ] Biometric data feeds into wellness decisions
- [ ] Circadian lighting adjusts throughout day
- [ ] Family status indicators function properly
- [ ] Emergency override works immediately

## 🚨 Troubleshooting

### Common Issues

#### MOSAIC Bridge Won't Connect
```bash
# Check network connectivity
ha network info

# Verify API token
ha addons config mosaic_bridge

# Check logs
ha addons logs mosaic_bridge
```

#### Automations Not Triggering
```yaml
# Enable automation debugging
logger:
  default: info
  logs:
    homeassistant.components.automation: debug
    custom_components.mosaic: debug
```

#### Device Not Responding
```bash
# Restart specific integration
ha integrations reload hue

# Restart entire system
ha core restart
```

## 📞 Support and Community

### Getting Help

- **Documentation**: [mosaic-ha.docs.alias.dev](https://mosaic-ha.docs.alias.dev)
- **Community Forum**: [community.mosaic.alias.dev](https://community.mosaic.alias.dev)
- **Discord**: #mosaic-home-assistant
- **GitHub Issues**: [github.com/alias/mosaic-home-assistant](https://github.com/alias/mosaic-home-assistant)

### Contributing

We welcome contributions to improve the integration:

1. Fork the repository
2. Create a feature branch
3. Test your changes thoroughly
4. Submit a pull request

---

**Installation Complete!** 🎉

Your home is now an extension of MOSAIC, ready to support your work-life synthesis journey. Start with basic automations and gradually add more sophisticated integrations as you discover your unique patterns.

*"Your environment shapes your creativity. Let MOSAIC and Home Assistant create the perfect canvas for your masterpiece."*