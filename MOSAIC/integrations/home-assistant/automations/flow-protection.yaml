# MOSAIC Flow Protection Automations
# Intelligent interruption management and flow state preservation

# Deep Work Flow Protection
- alias: "MOSAIC Deep Work Protection"
  description: "Protect flow state when Flow Guardian activates deep work mode"
  trigger:
    - platform: state
      entity_id: mosaic.flow_guardian
      to: "deep_work"
    - platform: state
      entity_id: input_boolean.manual_deep_work
      to: "on"
  condition:
    - condition: time
      after: "06:00:00"
      before: "22:00:00"
  action:
    # Immediate environment optimization
    - parallel:
        - service: scene.turn_on
          target:
            entity_id: scene.deep_work_lighting
        - service: climate.set_temperature
          target:
            entity_id: climate.office_hvac
          data:
            temperature: 68
        - service: media_player.play_media
          target:
            entity_id: media_player.office_speakers
          data:
            media_content_id: "focus_playlist"
            media_content_type: "music"
    
    # Interruption shields
    - service: input_boolean.turn_on
      target:
        entity_id: input_boolean.flow_protection_active
    
    # Family notification system
    - service: light.turn_on
      target:
        entity_id: light.hallway_status_indicator
      data:
        color_name: "red"
        brightness: 80
        effect: "breathe"
    
    - service: notify.family_tablets
      data:
        title: "Deep Work in Progress"
        message: >
          {{ trigger.to_state.attributes.person_name | default('Someone') }} 
          is in deep work mode until 
          {{ (now() + timedelta(minutes=90)) | timestamp_custom('%H:%M') }}.
          Please minimize interruptions unless urgent.
        data:
          priority: "high"
          persistent: true
          tag: "deep_work_active"
    
    # Smart doorbell management
    - service: switch.turn_off
      target:
        entity_id: switch.doorbell_chime
    
    - service: camera.turn_on
      target:
        entity_id: camera.front_door
    
    # Phone/device management
    - service: automation.trigger
      target:
        entity_id: automation.phone_do_not_disturb
    
    # Environmental fine-tuning
    - delay: "00:02:00"  # Allow initial settling
    - service: automation.trigger
      target:
        entity_id: automation.adaptive_lighting_adjustment

# Flow State Monitoring
- alias: "MOSAIC Flow State Detection"
  description: "Detect natural flow states and protect them"
  trigger:
    - platform: state
      entity_id: binary_sensor.chair_occupancy
      to: "on"
      for: "00:15:00"
    - platform: state
      entity_id: sensor.keyboard_activity
      attribute: "typing_intensity"
  condition:
    - condition: and
      conditions:
        - condition: state
          entity_id: mosaic.flow_guardian
          state: "focused"
        - condition: numeric_state
          entity_id: sensor.user_stress_level
          below: 50
        - condition: template
          value_template: >
            {{ states('sensor.keyboard_activity') | int > 200 }}
  action:
    - service: input_boolean.turn_on
      target:
        entity_id: input_boolean.natural_flow_detected
    
    - service: notify.user
      data:
        title: "Flow State Detected"
        message: "Activating protective measures for optimal focus"
        data:
          tag: "flow_protection"
          actions:
            - action: "extend_flow"
              title: "Extend Protection"
            - action: "normal_mode"
              title: "Return to Normal"
    
    # Gentle environmental optimization
    - service: light.turn_on
      target:
        entity_id: light.office_desk_lamp
      data:
        brightness: >
          {{ max(80, states('light.office_desk_lamp') | int) }}
        transition: 30
    
    - service: climate.set_temperature
      target:
        entity_id: climate.office_hvac
      data:
        temperature: >
          {{ max(66, min(70, state_attr('climate.office_hvac', 'temperature'))) }}

# Interruption Management
- alias: "MOSAIC Smart Interruption Filter"
  description: "Intelligently filter interruptions during work periods"
  trigger:
    - platform: state
      entity_id: binary_sensor.front_door_motion
      to: "on"
    - platform: state
      entity_id: binary_sensor.office_door_knock
      to: "on"
    - platform: event
      event_type: "mobile_app_notification_action"
      event_data:
        action: "urgent_interruption"
  condition:
    - condition: state
      entity_id: input_boolean.flow_protection_active
      state: "on"
  action:
    - choose:
        # Family member at office door
        - conditions:
            - condition: template
              value_template: >
                {{ trigger.entity_id == 'binary_sensor.office_door_knock' }}
          sequence:
            - service: media_player.play_media
              target:
                entity_id: media_player.office_speakers
              data:
                media_content_id: "media-source://tts/tts.piper?message=Someone is at your door. Type 'come in' if you're available."
                media_content_type: "music"
            
            - wait_for_trigger:
                - platform: event
                  event_type: "mosaic_voice_command"
                  event_data:
                    command: "come in"
                - platform: state
                  entity_id: sensor.keyboard_last_input
                  to: "come in"
              timeout: "00:01:00"
            
            - if:
                - condition: template
                  value_template: "{{ wait.completed }}"
              then:
                - service: lock.unlock
                  target:
                    entity_id: lock.office_door
                - service: light.turn_on
                  target:
                    entity_id: light.office_accent
                  data:
                    color_name: "green"
                    brightness: 60
              else:
                - service: notify.family_tablets
                  data:
                    message: "Please try again later - still in deep focus"
        
        # Delivery/visitor management
        - conditions:
            - condition: template
              value_template: >
                {{ trigger.entity_id == 'binary_sensor.front_door_motion' }}
          sequence:
            - service: camera.snapshot
              target:
                entity_id: camera.front_door
              data:
                filename: "/tmp/front_door_visitor.jpg"
            
            - service: notify.user
              data:
                title: "Visitor During Deep Work"
                message: "Someone is at the front door"
                data:
                  image: "/tmp/front_door_visitor.jpg"
                  actions:
                    - action: "answer_door"
                      title: "Handle Now"
                    - action: "ignore_visitor"
                      title: "Ignore"
                    - action: "family_handle"
                      title: "Ask Family"

# Energy Level Adaptation
- alias: "MOSAIC Energy-Based Environment"
  description: "Adapt environment based on user energy levels"
  trigger:
    - platform: state
      entity_id: mosaic.wellness_advocate
      attribute: "energy_level"
    - platform: time_pattern
      minutes: "/30"
  condition:
    - condition: state
      entity_id: person.user
      state: "home"
    - condition: time
      after: "07:00:00"
      before: "21:00:00"
  action:
    - choose:
        # High energy - optimize for productivity
        - conditions:
            - condition: template
              value_template: >
                {{ states.mosaic.wellness_advocate.attributes.energy_level | int > 80 }}
          sequence:
            - service: light.turn_on
              target:
                entity_id: 
                  - light.office_ceiling
                  - light.office_desk_lamp
              data:
                brightness: 100
                color_temp: 5000
            
            - service: climate.set_temperature
              target:
                entity_id: climate.office_hvac
              data:
                temperature: 66  # Cooler for alertness
            
            - service: notify.user
              data:
                title: "High Energy Detected"
                message: "Perfect time for challenging tasks!"
                data:
                  tag: "energy_optimization"
        
        # Medium energy - balanced environment
        - conditions:
            - condition: template
              value_template: >
                {% set energy = states.mosaic.wellness_advocate.attributes.energy_level | int %}
                {{ energy >= 50 and energy <= 80 }}
          sequence:
            - service: light.turn_on
              target:
                entity_id:
                  - light.office_ceiling
                  - light.office_desk_lamp
              data:
                brightness: 80
                color_temp: 4000
            
            - service: climate.set_temperature
              target:
                entity_id: climate.office_hvac
              data:
                temperature: 68
        
        # Low energy - comfort and support
        - conditions:
            - condition: template
              value_template: >
                {{ states.mosaic.wellness_advocate.attributes.energy_level | int < 50 }}
          sequence:
            - service: light.turn_on
              target:
                entity_id: light.office_accent
              data:
                brightness: 60
                color_temp: 2700
            
            - service: light.turn_off
              target:
                entity_id: light.office_ceiling
            
            - service: climate.set_temperature
              target:
                entity_id: climate.office_hvac
              data:
                temperature: 72  # Warmer for comfort
            
            - service: switch.turn_on
              target:
                entity_id: switch.essential_oil_diffuser
            
            - service: notify.user
              data:
                title: "Low Energy - Comfort Mode"
                message: "Environment optimized for restoration. Consider a break?"
                data:
                  actions:
                    - action: "start_break"
                      title: "Take Break"
                    - action: "light_tasks"
                      title: "Light Tasks Only"

# Biometric Stress Response
- alias: "MOSAIC Stress Detection Response"
  description: "Respond to elevated stress levels with environmental adjustments"
  trigger:
    - platform: numeric_state
      entity_id: sensor.user_stress_level
      above: 70
      for: "00:05:00"
  condition:
    - condition: state
      entity_id: person.user
      state: "home"
  action:
    - service: scene.turn_on
      target:
        entity_id: scene.stress_relief
    
    # Immediate calming environment
    - parallel:
        - service: light.turn_on
          target:
            entity_id: light.office_accent
          data:
            color_name: "blue"
            brightness: 40
            effect: "breathe"
        
        - service: media_player.play_media
          target:
            entity_id: media_player.office_speakers
          data:
            media_content_id: "breathing_exercise_guided"
            media_content_type: "music"
        
        - service: switch.turn_on
          target:
            entity_id: switch.essential_oil_diffuser
        
        - service: climate.set_temperature
          target:
            entity_id: climate.office_hvac
          data:
            temperature: 70
    
    # Gentle intervention notification
    - service: notify.user
      data:
        title: "Stress Level Alert"
        message: "High stress detected. Taking a 5-minute breathing break is recommended."
        data:
          tag: "stress_response"
          persistent: true
          actions:
            - action: "breathing_exercise"
              title: "Start Breathing Exercise"
            - action: "nature_break"
              title: "Go Outside"
            - action: "dismiss_stress"
              title: "I'm Fine"
    
    # Automatic meeting protection
    - if:
        - condition: state
          entity_id: calendar.work_calendar
          state: "on"
      then:
        - service: notify.user
          data:
            title: "Meeting Stress Support"
            message: "Stress detected during meeting. Activating discrete support."
        
        - service: light.turn_on
          target:
            entity_id: light.office_status_led
          data:
            color_name: "green"
            brightness: 20
      else:
        - service: automation.trigger
          target:
            entity_id: automation.suggest_immediate_break

# Meeting Environment Optimization
- alias: "MOSAIC Meeting Auto-Setup"
  description: "Automatically optimize environment for video calls"
  trigger:
    - platform: calendar
      event: start
      entity_id: calendar.work_calendar
      offset: "-00:02:00"
  condition:
    - condition: template
      value_template: >
        {{ 'video' in trigger.calendar_event.summary.lower() or 
           'call' in trigger.calendar_event.summary.lower() or
           'meeting' in trigger.calendar_event.summary.lower() }}
  action:
    # Pre-meeting notification
    - service: notify.user
      data:
        title: "Meeting Starting Soon"
        message: >
          {{ trigger.calendar_event.summary }} in 2 minutes. 
          Optimizing environment...
    
    # Environment optimization
    - parallel:
        - service: scene.turn_on
          target:
            entity_id: scene.video_call_lighting
        
        - service: switch.turn_on
          target:
            entity_id: switch.ring_light
        
        - service: switch.turn_off
          target:
            entity_id: switch.office_fan  # Reduce noise
        
        - service: cover.set_cover_position
          target:
            entity_id: cover.office_blinds
          data:
            position: 25  # Nice natural light background
        
        - service: media_player.set_volume_level
          target:
            entity_id: media_player.office_speakers
          data:
            volume_level: 0.8
    
    # Family notification
    - service: notify.family_tablets
      data:
        title: "Video Call in Progress"
        message: >
          {{ trigger.calendar_event.summary }} from 
          {{ trigger.calendar_event.start_time }} to 
          {{ trigger.calendar_event.end_time }}
        data:
          tag: "meeting_notification"
    
    # Status indicators
    - service: light.turn_on
      target:
        entity_id: light.hallway_status_indicator
      data:
        color_name: "blue"
        brightness: 60
    
    # Wait for meeting end
    - wait_for_trigger:
        - platform: calendar
          event: end
          entity_id: calendar.work_calendar
      timeout: "04:00:00"  # Max 4 hour meeting
    
    # Post-meeting restoration
    - service: scene.turn_on
      target:
        entity_id: scene.post_meeting_restoration
    
    - service: notify.user
      data:
        title: "Meeting Ended"
        message: "Environment restored. How did it go?"
        data:
          actions:
            - action: "meeting_productive"
              title: "Great Meeting!"
            - action: "meeting_tiring"
              title: "Need a Break"

# Transition Periods
- alias: "MOSAIC Work Day Transitions"
  description: "Smooth transitions between work and life modes"
  trigger:
    - platform: state
      entity_id: mosaic.rhythm_keeper
      attribute: "phase"
      to: "work_day_start"
    - platform: state
      entity_id: mosaic.rhythm_keeper
      attribute: "phase"
      to: "work_day_end"
    - platform: time
      at: input_datetime.work_start_time
    - platform: time
      at: input_datetime.work_end_time
  action:
    - choose:
        # Work day beginning
        - conditions:
            - condition: or
              conditions:
                - condition: template
                  value_template: >
                    {{ trigger.to_state.state == 'work_day_start' }}
                - condition: time
                  after: input_datetime.work_start_time
                  before: "09:00:00"
          sequence:
            - service: scene.turn_on
              target:
                entity_id: scene.morning_work_transition
            
            - service: media_player.play_media
              target:
                entity_id: media_player.office_speakers
              data:
                media_content_id: "morning_motivation_playlist"
                media_content_type: "music"
            
            - service: notify.user
              data:
                title: "Good Morning!"
                message: >
                  Ready to create something amazing today? 
                  Your workspace is optimized and waiting.
        
        # Work day ending
        - conditions:
            - condition: or
              conditions:
                - condition: template
                  value_template: >
                    {{ trigger.to_state.state == 'work_day_end' }}
                - condition: time
                  after: input_datetime.work_end_time
          sequence:
            - service: automation.trigger
              target:
                entity_id: automation.work_day_shutdown_sequence
            
            - service: scene.turn_on
              target:
                entity_id: scene.evening_family_mode
            
            - service: notify.user
              data:
                title: "Work Day Complete"
                message: >
                  Time to transition to family/personal time. 
                  Great work today!
                data:
                  actions:
                    - action: "review_achievements"
                      title: "Review Today"
                    - action: "family_time"
                      title: "Family Mode"

# Emergency Override
- alias: "MOSAIC Emergency Override"
  description: "Family emergency override for all automations"
  trigger:
    - platform: state
      entity_id: input_button.family_emergency
      to: "on"
    - platform: event
      event_type: "mobile_app_notification_action"
      event_data:
        action: "emergency_override"
  action:
    # Immediate full override
    - service: input_boolean.turn_off
      target:
        entity_id:
          - input_boolean.flow_protection_active
          - input_boolean.do_not_disturb
          - input_boolean.meeting_mode
    
    # Emergency lighting
    - service: light.turn_on
      target:
        entity_id: light.all_lights
      data:
        brightness: 100
        color_name: "white"
    
    # Unlock all doors
    - service: lock.unlock
      target:
        entity_id: lock.all_locks
    
    # Emergency notification
    - service: notify.all_devices
      data:
        title: "EMERGENCY OVERRIDE ACTIVATED"
        message: "All MOSAIC automations suspended. Full access restored."
        data:
          priority: "high"
          tag: "emergency_override"
    
    # Log emergency activation
    - service: logbook.log
      data:
        name: "MOSAIC Emergency Override"
        message: "Emergency override activated - all automations suspended"