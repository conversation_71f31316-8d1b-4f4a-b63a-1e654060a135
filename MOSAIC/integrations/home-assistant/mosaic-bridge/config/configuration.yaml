# MOSAIC × Home Assistant Bridge Configuration
# Complete integration configuration for seamless work-life synthesis

# Core MOSAIC Integration
mosaic:
  # Connection to MOSAIC Platform
  api:
    url: "https://mosaic.internal.company.com/api/v1"
    token: !secret mosaic_api_token
    timeout: 30
    retry_attempts: 3
  
  # Agent synchronization
  agents:
    sync_interval: 30  # seconds
    enabled_agents:
      - flow_guardian
      - wellness_advocate
      - rhythm_keeper
      - joy_catalyst
      - dream_weaver
      - social_harmonizer
    
    # Agent state entities
    entities:
      flow_guardian:
        - current_state
        - energy_level
        - next_transition
        - interruption_shield
        - optimal_conditions
      
      wellness_advocate:
        - posture_score
        - break_needed
        - hydration_level
        - stress_indicators
        - movement_target
      
      rhythm_keeper:
        - chronotype
        - circadian_phase
        - optimal_work_window
        - sleep_debt
        - seasonal_adjustment

# Workspace Optimization
workspace:
  # Primary workspace definition
  primary_workspace:
    name: "Home Office"
    location: "office"
    entities:
      lighting:
        - light.office_desk_lamp
        - light.office_ceiling
        - light.office_accent
      
      climate:
        - climate.office_hvac
        - fan.office_ceiling_fan
        - sensor.office_humidity
      
      technology:
        - switch.office_desk_power
        - sensor.office_wifi_strength
        - media_player.office_speakers
      
      ergonomics:
        - cover.office_blinds
        - sensor.desk_height
        - binary_sensor.chair_occupancy
  
  # Alternative workspaces
  flexible_spaces:
    kitchen_table:
      name: "Morning Creative Space"
      optimal_times: ["06:00", "09:00"]
      entities:
        - light.kitchen_pendant
        - media_player.kitchen_sonos
        - sensor.kitchen_temperature
    
    living_room:
      name: "Evening Review Space"
      optimal_times: ["17:00", "21:00"]
      entities:
        - light.living_room_floor_lamp
        - media_player.living_room_tv
        - climate.main_floor
    
    garden_office:
      name: "Outdoor Inspiration"
      weather_dependent: true
      entities:
        - sensor.outdoor_temperature
        - sensor.outdoor_humidity
        - switch.garden_wifi_extender

# Lifecycle Automations
lifecycles:
  # 1. Discovery Phase
  discovery:
    pattern_learning:
      motion_tracking:
        - binary_sensor.office_motion
        - binary_sensor.kitchen_motion
        - binary_sensor.living_room_motion
      
      presence_correlation:
        devices:
          - device_tracker.phone
          - device_tracker.laptop
          - device_tracker.smartwatch
        
        time_windows:
          work_start: "auto_detect"
          peak_productivity: "auto_detect"
          break_patterns: "auto_detect"
          work_end: "auto_detect"
  
  # 2. Qualification Phase
  qualification:
    space_evaluation:
      lighting_assessment:
        natural_light: sensor.office_lux
        artificial_options:
          - light.office_desk_lamp
          - light.office_ceiling
        optimal_levels:
          reading: 500
          computer_work: 300
          creative_work: 200
      
      acoustic_analysis:
        ambient_noise: sensor.office_noise_level
        noise_sources:
          - hvac_system
          - outdoor_traffic
          - family_activity
        mitigation:
          - media_player.white_noise
          - switch.noise_cancellation
  
  # 3. Architecture Phase
  architecture:
    intelligent_scenes:
      deep_work:
        description: "Maximum focus environment"
        lighting:
          - light.office_desk_lamp: {brightness: 80, color_temp: 4000}
          - light.office_ceiling: {brightness: 60, color_temp: 4000}
        
        climate:
          - climate.office_hvac: {temperature: 68}
          - fan.office_ceiling_fan: {speed: "low"}
        
        technology:
          - switch.office_desk_power: "on"
          - media_player.office_speakers: {volume: 0.3, source: "focus_playlist"}
        
        isolation:
          - cover.office_blinds: "closed"
          - lock.office_door: "locked"
          - light.hallway_status: {color: "red", brightness: 30}
      
      collaboration:
        description: "Video call optimized"
        lighting:
          - light.office_desk_lamp: {brightness: 100, color_temp: 5000}
          - light.office_ceiling: {brightness: 40, color_temp: 5000}
        
        technology:
          - switch.ring_light: "on"
          - media_player.office_speakers: {volume: 0.8}
          - switch.office_fan: "off"  # Reduce noise
        
        aesthetics:
          - cover.office_blinds: "25_percent"  # Nice background
          - switch.background_plants_light: "on"
      
      restoration:
        description: "Break and recovery"
        lighting:
          - light.office_accent: {brightness: 40, color: "warm_white"}
          - light.office_ceiling: "off"
        
        environment:
          - cover.office_blinds: "open"
          - climate.office_hvac: {temperature: 72}
        
        wellness:
          - switch.essential_oil_diffuser: "on"
          - media_player.office_speakers: {source: "nature_sounds"}

# Biometric Integration
biometrics:
  devices:
    smartwatch:
      platform: "apple_health"  # or "google_fit", "samsung_health"
      entities:
        - sensor.heart_rate
        - sensor.heart_rate_variability
        - sensor.stress_level
        - sensor.activity_level
      
      automations:
        high_stress:
          threshold: 80
          actions:
            - scene.turn_on: "scene.restoration"
            - notify.user: "High stress detected - initiating calm mode"
            - automation.trigger: "breathing_exercise_prompt"
        
        flow_state_detected:
          conditions:
            - low_stress_consistent: "10_minutes"
            - high_focus_activity: true
          actions:
            - input_boolean.turn_on: "flow_state_protection"
            - notify.family: "Flow state detected - minimizing interruptions"
    
    sleep_tracker:
      platform: "withings"  # or "oura", "eight_sleep"
      entities:
        - sensor.sleep_score
        - sensor.sleep_debt
        - sensor.rem_percentage
        - sensor.deep_sleep_duration
      
      morning_optimization:
        conditions:
          poor_sleep: {sleep_score: {below: 70}}
          good_sleep: {sleep_score: {above: 85}}
        
        adjustments:
          poor_sleep:
            - light_therapy: {duration: "30_minutes", intensity: "high"}
            - work_schedule: {delay_start: "30_minutes"}
            - break_frequency: {increase: "25_percent"}
          
          good_sleep:
            - morning_boost: {early_start_suggest: true}
            - challenging_tasks: {schedule_early: true}

# Family Coordination
family:
  members:
    partner:
      device_tracker: device_tracker.partner_phone
      work_schedule: 
        monday: ["09:00", "17:00"]
        tuesday: ["09:00", "17:00"]
        # ... schedule per day
      
      coordination:
        mutual_deep_work: "coordinate_focus_times"
        meeting_overlap: "avoid_simultaneous_calls"
        break_sync: "suggest_shared_breaks"
    
    children:
      school_aged:
        device_tracker: device_tracker.child_tablet
        school_schedule:
          weekdays: ["08:00", "15:30"]
        
        quiet_hours:
          nap_time: ["13:00", "15:00"]
          homework_time: ["16:00", "17:30"]
        
        coordination:
          parallel_focus: "sync_work_homework_time"
          achievement_sharing: "celebrate_together"
  
  shared_spaces:
    living_room:
      work_usage: "evening_review_only"
      family_priority: true
      
      conflict_resolution:
        family_movie: "work_relocates_to_office"
        important_call: "family_uses_playroom"
    
    kitchen:
      work_usage: "morning_creative_only"
      meal_times:
        - "07:00-08:00"  # Breakfast
        - "12:00-13:00"  # Lunch
        - "18:00-19:30"  # Dinner

# Environmental Intelligence
environment:
  circadian_lighting:
    morning_simulation:
      start_time: "sunrise - 30min"
      color_progression:
        - {time: "start", color_temp: 2000, brightness: 1}
        - {time: "start + 15min", color_temp: 2700, brightness: 20}
        - {time: "start + 30min", color_temp: 4000, brightness: 60}
    
    daytime_optimization:
      natural_light_sensor: sensor.office_lux
      supplement_threshold: 300
      color_temp_schedule:
        - {time: "09:00", color_temp: 5000}  # Alert morning
        - {time: "12:00", color_temp: 5500}  # Peak focus
        - {time: "15:00", color_temp: 4500}  # Afternoon comfort
        - {time: "17:00", color_temp: 3500}  # Wind down
    
    evening_transition:
      trigger_time: "sunset"
      blue_light_reduction:
        gradual: true
        duration: "2_hours"
        final_color_temp: 2200
  
  air_quality:
    monitoring:
      - sensor.office_co2
      - sensor.office_pm25
      - sensor.office_voc
    
    automation:
      high_co2:
        threshold: 800
        actions:
          - notify.user: "Air quality poor - opening windows"
          - cover.office_window: "open"
          - switch.air_purifier: "on"
      
      poor_air_quality:
        pm25_threshold: 35
        actions:
          - switch.air_purifier: "on"
          - climate.office_hvac: {mode: "recirculate"}

# Notification Management
notifications:
  channels:
    user_phone:
      platform: "mobile_app"
      device: "mobile_app_user_phone"
      
      priority_levels:
        critical: "Always deliver"
        high: "Deliver unless in deep work"
        medium: "Batch and deliver during breaks"
        low: "Daily summary only"
    
    family_display:
      platform: "persistent_notification"
      display: "kitchen_tablet"
      
      messages:
        work_status: "Current work mode and availability"
        schedule_updates: "Meeting changes and conflicts"
        celebration: "Achievement and milestone alerts"
    
    smart_lights:
      platform: "light_notification"
      entities:
        - light.hallway_status
        - light.kitchen_indicator
      
      status_codes:
        available: {color: "green", brightness: 50}
        busy: {color: "red", brightness: 30}
        in_meeting: {color: "blue", brightness: 40}
        on_break: {color: "yellow", brightness: 60}

# Security and Privacy
security:
  data_handling:
    local_processing: true
    cloud_sync: "encrypted_only"
    retention: "90_days"
    
    sensitive_data:
      biometrics: "device_only"
      family_patterns: "local_only"
      work_content: "never_stored"
  
  access_control:
    guest_mode:
      trigger: input_boolean.guest_mode
      actions:
        - camera.disable: "all_indoor_cameras"
        - sensor.disable: "presence_tracking"
        - automation.disable: "personal_automations"
    
    work_mode_privacy:
      meeting_privacy:
        - camera.block: "office_camera"
        - microphone.mute: "smart_speakers"
        - display.turn_off: "family_tablets"

# Voice Control
voice:
  wake_words:
    - "Hey MOSAIC"
    - "Flow mode"
    - "Break time"
    - "Work mode"
  
  commands:
    flow_control:
      "Deep work mode": scene.deep_work
      "Collaboration mode": scene.collaboration
      "Break time": scene.restoration
      "End work day": automation.work_day_end
    
    environment:
      "Brighter": light.increase_brightness
      "Warmer light": light.warm_temperature
      "Quieter": automation.reduce_noise
      "Fresh air": automation.improve_air_quality
    
    family:
      "Family time": automation.family_mode
      "Do not disturb": automation.dnd_mode
      "Available": automation.available_mode

# Integration APIs
apis:
  calendar:
    google_calendar:
      calendars:
        - "primary"
        - "<EMAIL>"
        - "<EMAIL>"
      
      meeting_detection:
        prep_time: "5_minutes"
        cleanup_time: "2_minutes"
        focus_blocks: "auto_detect"
  
  weather:
    openweathermap:
      location: !secret location_home
      
      seasonal_adjustments:
        winter: "increase_indoor_light"
        summer: "optimize_cooling_efficiency"
        rainy: "boost_mood_lighting"
  
  fitness:
    strava:
      workout_detection: true
      recovery_mode: "post_workout_30min"
    
    apple_health:
      metrics: ["steps", "heart_rate", "sleep"]
      privacy: "aggregated_only"

# Backup and Recovery
backup:
  configurations:
    frequency: "daily"
    retention: "30_days"
    location: "local_nas"
  
  state_preservation:
    learning_data: "backup_weekly"
    personal_patterns: "backup_daily"
    family_schedules: "backup_daily"
  
  disaster_recovery:
    offline_mode: "essential_automations_only"
    network_outage: "local_processing_continue"
    device_failure: "graceful_degradation"