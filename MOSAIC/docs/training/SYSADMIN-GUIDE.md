# MOSAIC System Administrator Guide

## Overview

This guide provides comprehensive information for system administrators responsible for deploying, maintaining, and monitoring MOSAIC systems in production environments.

## System Requirements

### Minimum Requirements
- **CPU**: 4 cores, 2.4GHz
- **RAM**: 16GB
- **Storage**: 100GB SSD
- **Network**: 1Gbps connection
- **OS**: Ubuntu 20.04+ / CentOS 8+ / macOS 12+

### Recommended Production Setup
- **CPU**: 8+ cores, 3.0GHz+
- **RAM**: 32GB+
- **Storage**: 500GB+ NVMe SSD
- **Network**: 10Gbps connection
- **Load Balancer**: HAProxy/Nginx
- **Monitoring**: Prometheus + Grafana

### Infrastructure Components
- **Apache Kafka**: Event bus (3+ node cluster)
- **Redis Cluster**: Context store (3+ nodes)
- **PostgreSQL**: Persistent storage (primary + replicas)
- **Docker/Kubernetes**: Container orchestration
- **Schema Registry**: Event schema management

## Installation and Setup

### 1. Infrastructure Deployment

#### Docker Compose (Development/Small Production)
```bash
# Clone repository
git clone https://github.com/alias/mosaic.git
cd mosaic/infrastructure

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Deploy infrastructure
docker-compose up -d

# Verify deployment
docker-compose ps
```

#### Kubernetes (Production)
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/kafka/
kubectl apply -f k8s/redis/
kubectl apply -f k8s/postgresql/
kubectl apply -f k8s/mosaic/

# Verify deployment
kubectl get pods -n mosaic
```

### 2. MOSAIC System Deployment

#### Build and Deploy
```bash
# Build all components
cd mosaic
npm run build:prod

# Deploy using CLI
mosaic deploy --environment production --config production.yaml

# Verify deployment
mosaic status --detailed
mosaic health --deep
```

#### Configuration Management
```yaml
# production.yaml
environment: production
infrastructure:
  kafka:
    brokers: ["kafka-1:9092", "kafka-2:9092", "kafka-3:9092"]
    replication_factor: 3
    min_insync_replicas: 2
  redis:
    cluster: true
    nodes: ["redis-1:6379", "redis-2:6379", "redis-3:6379"]
    password: "${REDIS_PASSWORD}"
  postgresql:
    host: "postgres-primary"
    port: 5432
    database: "mosaic"
    username: "${POSTGRES_USER}"
    password: "${POSTGRES_PASSWORD}"
    replicas: ["postgres-replica-1", "postgres-replica-2"]

lifecycles:
  apex-lc:
    enabled: true
    replicas: 3
    resources:
      cpu: "1000m"
      memory: "2Gi"
  prism-lc:
    enabled: true
    replicas: 2
    resources:
      cpu: "500m"
      memory: "1Gi"
  aurora-lc:
    enabled: true
    replicas: 2
    resources:
      cpu: "500m"
      memory: "1Gi"
  pulse-lc:
    enabled: true
    replicas: 1
    resources:
      cpu: "1000m"
      memory: "2Gi"

monitoring:
  enabled: true
  prometheus:
    endpoint: "http://prometheus:9090"
  grafana:
    endpoint: "http://grafana:3000"
  alerts:
    enabled: true
    webhook: "${ALERT_WEBHOOK_URL}"
```

## Operations and Maintenance

### Daily Operations

#### System Health Monitoring
```bash
# Check overall system health
mosaic health --deep

# Monitor specific components
mosaic status --component kafka
mosaic status --component redis
mosaic status --component postgresql

# View system metrics
mosaic metrics --format prometheus
```

#### Log Management
```bash
# View aggregated logs
mosaic logs --follow --level error

# View lifecycle-specific logs
mosaic logs apex-lc --tail 1000

# Search logs
mosaic logs --pattern "error|exception" --since "1h"

# Export logs
mosaic logs --export --format json --output /var/log/mosaic/
```

#### Event Monitoring
```bash
# Monitor event flow
mosaic events monitor --pattern "*"

# View event statistics
mosaic events stats --interval 1h

# Process dead letter queue
mosaic events dlq process --max-retries 3
```

### Performance Tuning

#### Resource Optimization
```bash
# Monitor resource usage
mosaic status --resources

# Scale lifecycles
mosaic scale apex-lc --replicas 5
mosaic scale prism-lc --cpu 1000m --memory 2Gi

# Optimize event processing
mosaic config set eventBus.batchSize 1000
mosaic config set eventBus.flushInterval 100ms
```

#### Database Optimization
```sql
-- PostgreSQL optimization
-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM contexts WHERE user_id = 'user-123';

-- Update statistics
ANALYZE;

-- Vacuum and reindex
VACUUM ANALYZE;
REINDEX DATABASE mosaic;
```

#### Cache Optimization
```bash
# Redis cluster optimization
redis-cli --cluster info
redis-cli --cluster check redis-1:6379

# Clear caches if needed
mosaic exec pulse-lc "context.clearCache()"

# Monitor cache hit rates
mosaic metrics redis.cache_hit_rate
```

### Backup and Recovery

#### Database Backups
```bash
# PostgreSQL backup
pg_dump -h postgres-primary -U postgres mosaic > mosaic_backup_$(date +%Y%m%d).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/var/backups/mosaic"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
pg_dump -h postgres-primary -U postgres mosaic | gzip > "$BACKUP_DIR/mosaic_$DATE.sql.gz"

# Cleanup old backups (keep 30 days)
find "$BACKUP_DIR" -name "mosaic_*.sql.gz" -mtime +30 -delete
```

#### Configuration Backups
```bash
# Backup MOSAIC configuration
mosaic config backup --output /var/backups/mosaic/config_$(date +%Y%m%d).yaml

# Backup Kafka topics
kafka-topics --bootstrap-server kafka-1:9092 --list > /var/backups/mosaic/topics_$(date +%Y%m%d).txt
```

#### Disaster Recovery
```bash
# Restore from backup
pg_restore -h postgres-primary -U postgres -d mosaic mosaic_backup.sql

# Restore configuration
mosaic config restore --input /var/backups/mosaic/config_20240101.yaml

# Verify system after restore
mosaic health --deep
mosaic status --detailed
```

### Security Management

#### Access Control
```bash
# Manage user access
mosaic <NAME_EMAIL> --role admin
mosaic <NAME_EMAIL> --role developer
mosaic user list

# API key management
mosaic api-key generate --name "monitoring-system" --permissions read
mosaic api-key revoke --key-id abc123
```

#### Security Monitoring
```bash
# Monitor security events
mosaic logs --pattern "auth|security" --level warn

# Check for vulnerabilities
npm audit
docker scan mosaic/apex-lc:latest

# Update security configurations
mosaic config set security.encryption.enabled true
mosaic config set security.audit.enabled true
```

#### SSL/TLS Configuration
```yaml
# Enable TLS for all components
security:
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/mosaic.crt"
    key_file: "/etc/ssl/private/mosaic.key"
    ca_file: "/etc/ssl/certs/ca.crt"
```

### Monitoring and Alerting

#### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'mosaic'
    static_configs:
      - targets: ['mosaic:9090']
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka-1:9308', 'kafka-2:9308', 'kafka-3:9308']
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-1:9121', 'redis-2:9121', 'redis-3:9121']
```

#### Grafana Dashboards
- **System Overview**: Overall health and performance
- **Lifecycle Metrics**: Individual lifecycle performance
- **Infrastructure**: Kafka, Redis, PostgreSQL metrics
- **Event Flow**: Event processing and routing metrics
- **Error Tracking**: Error rates and patterns

#### Alert Rules
```yaml
# alert-rules.yml
groups:
  - name: mosaic
    rules:
      - alert: LifecycleDown
        expr: up{job="mosaic"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "MOSAIC lifecycle is down"
          
      - alert: HighErrorRate
        expr: rate(mosaic_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          
      - alert: EventProcessingLag
        expr: kafka_consumer_lag_sum > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Event processing lag is high"
```

### Troubleshooting

#### Common Issues

##### Lifecycle Not Starting
```bash
# Check configuration
mosaic config validate

# Check resource availability
kubectl describe pod mosaic-apex-lc-xxx

# Check logs
mosaic logs apex-lc --tail 100 --level error

# Restart lifecycle
mosaic restart apex-lc
```

##### Event Processing Issues
```bash
# Check Kafka cluster health
kafka-topics --bootstrap-server kafka-1:9092 --list

# Check consumer lag
kafka-consumer-groups --bootstrap-server kafka-1:9092 --describe --all-groups

# Process dead letter queue
mosaic events dlq process --topic mosaic.lifecycle.events.dlq
```

##### Database Connection Issues
```bash
# Check PostgreSQL connectivity
pg_isready -h postgres-primary -p 5432

# Check connection pool
mosaic exec pulse-lc "db.pool.status()"

# Reset connections
mosaic exec pulse-lc "db.pool.reset()"
```

##### Memory Issues
```bash
# Check memory usage
mosaic status --resources

# Generate heap dump
mosaic exec apex-lc "process.generateHeapDump()"

# Restart with more memory
mosaic scale apex-lc --memory 4Gi
```

#### Performance Issues
```bash
# Profile system performance
mosaic profile --duration 60s --output /tmp/profile.json

# Analyze slow queries
mosaic exec pulse-lc "db.slowQueries()"

# Optimize event processing
mosaic config set eventBus.batchSize 500
mosaic config set eventBus.parallelism 4
```

### Maintenance Procedures

#### Regular Maintenance Tasks
1. **Daily**: Health checks, log review, backup verification
2. **Weekly**: Performance review, security updates, capacity planning
3. **Monthly**: Full system backup, disaster recovery testing
4. **Quarterly**: Security audit, performance optimization, capacity upgrade

#### Update Procedures
```bash
# Update MOSAIC system
mosaic update --version 2.1.0 --strategy rolling

# Update infrastructure components
docker-compose pull
docker-compose up -d

# Verify update
mosaic health --deep
mosaic status --detailed
```

#### Capacity Planning
```bash
# Monitor growth trends
mosaic metrics --export --format csv --period 30d

# Analyze resource usage patterns
mosaic analyze capacity --forecast 90d

# Plan scaling
mosaic plan scale --target-load 80% --horizon 6m
```

## Best Practices

### 1. High Availability
- Deploy across multiple availability zones
- Use load balancers for all external endpoints
- Implement proper health checks
- Maintain hot standby systems

### 2. Security
- Regular security updates
- Principle of least privilege
- Network segmentation
- Audit logging enabled

### 3. Monitoring
- Comprehensive metrics collection
- Proactive alerting
- Regular performance reviews
- Capacity planning

### 4. Backup and Recovery
- Regular automated backups
- Tested recovery procedures
- Offsite backup storage
- Recovery time objectives defined

## Emergency Procedures

### System Outage Response
1. **Assess**: Determine scope and impact
2. **Communicate**: Notify stakeholders
3. **Investigate**: Identify root cause
4. **Resolve**: Implement fix or workaround
5. **Verify**: Confirm system recovery
6. **Document**: Record incident details
7. **Review**: Post-incident analysis

### Contact Information
- **On-call Engineer**: +1-555-MOSAIC
- **Escalation Manager**: <EMAIL>
- **Vendor Support**: <EMAIL>

This guide provides the foundation for effective MOSAIC system administration. Regular updates and team training ensure optimal system performance and reliability.
