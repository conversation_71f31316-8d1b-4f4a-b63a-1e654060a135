# MOSAIC Developer Onboarding Guide

## Welcome to MOSAIC Development

This guide will help you get up to speed with MOSAIC development, from initial setup to contributing to the codebase.

## Prerequisites

### Required Knowledge
- **TypeScript/JavaScript**: Advanced proficiency required
- **Node.js**: Experience with modern Node.js development
- **React**: Understanding of React 19+ features and patterns
- **Event-Driven Architecture**: Familiarity with pub/sub patterns
- **Docker**: Basic containerization knowledge
- **Git**: Version control and collaboration workflows

### Development Environment
- **Node.js**: Version 18+ (recommend using nvm)
- **Docker**: Latest stable version
- **VS Code**: Recommended IDE with extensions
- **Git**: Latest version with SSH keys configured

## Day 1: Environment Setup

### 1. Repository Setup
```bash
# Clone the repository
git clone https://github.com/alias/mosaic.git
cd mosaic

# Install dependencies
npm install

# Set up development environment
cp .env.example .env.local
```

### 2. Infrastructure Setup
```bash
# Start local infrastructure
cd infrastructure
docker-compose up -d

# Verify services are running
docker-compose ps
```

### 3. Build and Test
```bash
# Build all components
npm run build

# Run tests
npm run test

# Start development server
npm run dev
```

### 4. CLI Setup
```bash
# Build and link CLI
cd cli
npm run build
npm link

# Test CLI
mosaic --help
mosaic status
```

## Day 2: Architecture Deep Dive

### MOSAIC Core Concepts

#### 1. Lifecycle Architecture
Each lifecycle is an autonomous component with:
- **Configuration**: YAML-based configuration
- **Implementation**: TypeScript class with standard interface
- **Events**: Pub/sub communication with other lifecycles
- **Context**: Shared state through context store

#### 2. Event-Driven Communication
```typescript
// Publishing events
await eventBus.publish({
  type: 'feature_completed',
  source: 'apex-lc',
  data: { featureId: 'user-auth', version: '1.0.0' }
});

// Subscribing to events
eventBus.subscribe('feature_completed', async (event) => {
  // Handle event
  console.log('Feature completed:', event.data.featureId);
});
```

#### 3. Context Store Usage
```typescript
// Store context
await contextStore.setContext('user-123', {
  preferences: { theme: 'dark' },
  session: { lastActivity: new Date() }
});

// Retrieve context
const userContext = await contextStore.getContext('user-123');
```

### Code Organization
```
mosaic/
├── apex-lc/           # Development lifecycle
├── prism-lc/          # Knowledge management
├── aurora-lc/         # Customer success
├── pulse-lc/          # Meta-orchestration
├── shared/            # Shared types and utilities
├── infrastructure/    # Event bus, context store
├── cli/              # Command-line tools
├── tests/            # Integration tests
└── docs/             # Documentation
```

## Day 3: Development Workflow

### 1. Creating a New Lifecycle

#### Step 1: Configuration
```yaml
# mosaic/config/mosaic.yaml
lifecycles:
  my-new-lc:
    enabled: true
    port: 3005
    config:
      custom_setting: value
```

#### Step 2: Implementation
```typescript
// mosaic/my-new-lc/index.ts
import { BaseLifecycle, LifecycleConfig } from '../shared/types';

export class MyNewLC extends BaseLifecycle {
  constructor(config: LifecycleConfig) {
    super('my-new-lc', config);
  }

  async initialize(): Promise<void> {
    // Initialization logic
    await this.setupEventHandlers();
  }

  private async setupEventHandlers(): Promise<void> {
    this.eventBus.subscribe('relevant_event', this.handleEvent.bind(this));
  }

  private async handleEvent(event: MosaicEvent): Promise<void> {
    // Event handling logic
  }
}
```

#### Step 3: Integration
```typescript
// mosaic/index.ts
import { MyNewLC } from './my-new-lc';

// Add to orchestrator
this.lifecycles.set('my-new-lc', new MyNewLC(config.lifecycles['my-new-lc']));
```

### 2. Adding New Features

#### Feature Development Process
1. **Design**: Create feature specification
2. **Implementation**: Write code with tests
3. **Integration**: Add event handlers and context updates
4. **Testing**: Write integration tests
5. **Documentation**: Update relevant docs
6. **Review**: Submit PR for review

#### Example: Adding a New Event Type
```typescript
// 1. Define event type
interface NewFeatureEvent extends MosaicEvent {
  type: 'new_feature_event';
  data: {
    featureId: string;
    metadata: Record<string, any>;
  };
}

// 2. Add to schema registry
const newFeatureSchema = {
  type: 'record',
  name: 'NewFeatureEvent',
  fields: [
    { name: 'featureId', type: 'string' },
    { name: 'metadata', type: { type: 'map', values: 'string' } }
  ]
};

// 3. Register schema
await schemaRegistry.registerSchema('new_feature_event', newFeatureSchema);

// 4. Implement handlers
eventBus.subscribe('new_feature_event', async (event: NewFeatureEvent) => {
  // Handle the new event
});
```

## Day 4: Testing and Quality

### Testing Strategy

#### 1. Unit Tests
```typescript
// Example unit test
import { describe, it, expect } from 'vitest';
import { MyNewLC } from '../my-new-lc';

describe('MyNewLC', () => {
  it('should initialize correctly', async () => {
    const lc = new MyNewLC(mockConfig);
    await lc.initialize();
    expect(lc.isInitialized()).toBe(true);
  });
});
```

#### 2. Integration Tests
```typescript
// Example integration test
describe('Cross-Lifecycle Communication', () => {
  it('should handle event flow between lifecycles', async () => {
    // Setup
    const orchestrator = new MosaicOrchestrator(testConfig);
    await orchestrator.initialize();

    // Test event flow
    await apexLC.triggerEvent({ type: 'test_event', data: {} });
    
    // Verify
    const events = await eventBus.getEventHistory();
    expect(events).toContainEqual(expect.objectContaining({
      type: 'test_event'
    }));
  });
});
```

#### 3. Running Tests
```bash
# Run all tests
npm run test

# Run specific test suite
npm run test integration/

# Run with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

### Code Quality Standards

#### 1. TypeScript Configuration
- Strict mode enabled
- No implicit any
- Proper type definitions for all functions
- Interface-based design

#### 2. Code Style
- ESLint configuration enforced
- Prettier for formatting
- Consistent naming conventions
- Comprehensive JSDoc comments

#### 3. Error Handling
```typescript
// Proper error handling pattern
try {
  const result = await riskyOperation();
  return { success: true, data: result };
} catch (error) {
  this.logger.error('Operation failed', { error, context });
  return { success: false, error: error.message };
}
```

## Day 5: Deployment and Operations

### Local Development
```bash
# Start full development environment
npm run dev:full

# Start specific lifecycles
npm run dev:apex
npm run dev:prism

# Monitor system health
mosaic health --watch
```

### Production Deployment
```bash
# Build for production
npm run build:prod

# Deploy to staging
mosaic deploy --environment staging

# Run deployment tests
npm run test:deployment

# Deploy to production
mosaic deploy --environment production --confirm
```

### Monitoring and Debugging
```bash
# View system status
mosaic status --detailed

# Monitor events
mosaic events monitor --pattern "*"

# View logs
mosaic logs --follow --level error

# Debug specific lifecycle
mosaic exec apex-lc "debug.dumpState()"
```

## Best Practices

### 1. Event Design
- Use descriptive event names
- Include all necessary context in event data
- Design for idempotency
- Handle event ordering carefully

### 2. Error Handling
- Always handle errors gracefully
- Log errors with sufficient context
- Implement retry logic where appropriate
- Use circuit breakers for external services

### 3. Performance
- Use async/await properly
- Implement proper caching strategies
- Monitor resource usage
- Optimize database queries

### 4. Security
- Validate all inputs
- Use proper authentication/authorization
- Sanitize data before storage
- Follow security best practices

## Common Patterns

### 1. Lifecycle Communication
```typescript
// Request-response pattern
const response = await this.requestFromLifecycle('prism-lc', {
  type: 'knowledge_request',
  query: 'TypeScript best practices'
});

// Fire-and-forget pattern
await this.notifyLifecycle('aurora-lc', {
  type: 'customer_update',
  customerId: 'user-123',
  update: { status: 'active' }
});
```

### 2. Context Management
```typescript
// Context-aware operations
const userContext = await this.getContext(userId);
const personalizedResult = await this.processWithContext(data, userContext);
await this.updateContext(userId, { lastOperation: personalizedResult });
```

### 3. Configuration Management
```typescript
// Dynamic configuration
const config = await this.getConfig('feature.enabled');
if (config) {
  await this.enableFeature();
}
```

## Resources and Support

### Documentation
- [MOSAIC Complete Guide](../MOSAIC-COMPLETE-GUIDE.md)
- [API Reference](../api/)
- [Architecture Documentation](../architecture/)

### Development Tools
- [VS Code Extensions](./vscode-extensions.md)
- [Debug Configuration](./debug-setup.md)
- [Performance Profiling](./performance-guide.md)

### Getting Help
- **Slack**: #mosaic-development
- **GitHub Issues**: For bugs and feature requests
- **Code Reviews**: Required for all changes
- **Pair Programming**: Available for complex features

## Next Steps

After completing this onboarding:
1. **Pick your first task**: Start with good first issues
2. **Join the team**: Attend daily standups and planning meetings
3. **Contribute**: Submit your first PR
4. **Learn continuously**: Stay updated with MOSAIC evolution
5. **Share knowledge**: Help onboard future developers

Welcome to the MOSAIC development team! 🚀
