# MOSAIC API Documentation

## Overview

The MOSAIC API provides programmatic access to all MOSAIC lifecycle components, enabling integration with external systems and custom application development.

## Base URL
```
Production: https://api.mosaic.yourdomain.com/v1
Staging: https://api-staging.mosaic.yourdomain.com/v1
Development: http://localhost:3000/api/v1
```

## Authentication

### API Key Authentication
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.mosaic.yourdomain.com/v1/status
```

### JWT Token Authentication
```bash
# Get token
curl -X POST https://api.mosaic.yourdomain.com/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password"}'

# Use token
curl -H "Authorization: Bearer JWT_TOKEN" \
     https://api.mosaic.yourdomain.com/v1/user/profile
```

## Core Endpoints

### System Status
```http
GET /status
```
Returns overall system health and status.

**Response:**
```json
{
  "status": "healthy",
  "version": "2.1.0",
  "uptime": 86400,
  "lifecycles": {
    "apex-lc": { "status": "running", "health": "healthy" },
    "prism-lc": { "status": "running", "health": "healthy" },
    "aurora-lc": { "status": "running", "health": "healthy" },
    "pulse-lc": { "status": "running", "health": "healthy" }
  },
  "infrastructure": {
    "kafka": { "status": "connected", "lag": 0 },
    "redis": { "status": "connected", "memory_usage": "45%" },
    "postgresql": { "status": "connected", "connections": 12 }
  }
}
```

### Health Check
```http
GET /health
GET /health/deep
```
Performs basic or comprehensive health checks.

## Lifecycle APIs

### APEX-LC (Development Lifecycle)

#### Get Development Status
```http
GET /apex/status
```

#### Create New Project
```http
POST /apex/projects
Content-Type: application/json

{
  "name": "My New Project",
  "description": "Project description",
  "template": "react-app",
  "personas": ["architect", "developer", "tester"]
}
```

#### Get Project Details
```http
GET /apex/projects/{projectId}
```

#### Trigger Development Pipeline
```http
POST /apex/projects/{projectId}/pipeline
Content-Type: application/json

{
  "stage": "development",
  "features": ["user-auth", "dashboard"],
  "deadline": "2024-01-15T10:00:00Z"
}
```

### PRISM-LC (Knowledge Management)

#### Search Knowledge Base
```http
GET /prism/search?q={query}&limit={limit}&offset={offset}
```

#### Add Knowledge Item
```http
POST /prism/knowledge
Content-Type: application/json

{
  "title": "API Best Practices",
  "content": "Content here...",
  "tags": ["api", "development", "best-practices"],
  "source": "documentation"
}
```

#### Get Insights
```http
GET /prism/insights?category={category}&timeframe={timeframe}
```

### AURORA-LC (Customer Success)

#### Get Customer Health Score
```http
GET /aurora/customers/{customerId}/health
```

#### Update Customer Journey
```http
PUT /aurora/customers/{customerId}/journey
Content-Type: application/json

{
  "stage": "onboarding",
  "progress": 75,
  "next_actions": ["complete_profile", "first_project"]
}
```

#### Process Customer Feedback
```http
POST /aurora/feedback
Content-Type: application/json

{
  "customer_id": "cust_123",
  "type": "feature_request",
  "content": "Would love to see dark mode",
  "priority": "medium"
}
```

### PULSE-LC (Meta-Orchestration)

#### Get System Metrics
```http
GET /pulse/metrics?timeframe={timeframe}&format={format}
```

#### Resource Allocation
```http
POST /pulse/resources/allocate
Content-Type: application/json

{
  "lifecycle": "apex-lc",
  "resources": {
    "cpu": "2000m",
    "memory": "4Gi",
    "replicas": 3
  }
}
```

#### Conflict Resolution
```http
POST /pulse/conflicts/resolve
Content-Type: application/json

{
  "conflict_id": "conf_123",
  "resolution": "prioritize_apex",
  "reason": "Critical deployment in progress"
}
```

## Event API

### Subscribe to Events
```http
GET /events/stream
Accept: text/event-stream
```

### Publish Event
```http
POST /events
Content-Type: application/json

{
  "type": "custom_event",
  "source": "external_system",
  "data": {
    "key": "value"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Get Event History
```http
GET /events/history?type={type}&source={source}&limit={limit}
```

## Context API

### Get User Context
```http
GET /context/users/{userId}
```

### Update User Context
```http
PUT /context/users/{userId}
Content-Type: application/json

{
  "preferences": {
    "theme": "dark",
    "notifications": true
  },
  "session": {
    "last_activity": "2024-01-01T12:00:00Z",
    "active_project": "proj_123"
  }
}
```

### Query Context
```http
POST /context/query
Content-Type: application/json

{
  "query": "users with active projects",
  "filters": {
    "last_activity": "> 1 day ago"
  }
}
```

## Configuration API

### Get Configuration
```http
GET /config
GET /config/{key}
```

### Update Configuration
```http
PUT /config/{key}
Content-Type: application/json

{
  "value": "new_value"
}
```

### Validate Configuration
```http
POST /config/validate
Content-Type: application/json

{
  "config": {
    "lifecycles": {
      "apex-lc": {
        "enabled": true,
        "port": 3001
      }
    }
  }
}
```

## WebSocket API

### Real-time Updates
```javascript
const ws = new WebSocket('wss://api.mosaic.yourdomain.com/v1/ws');

ws.onopen = function() {
  // Subscribe to specific events
  ws.send(JSON.stringify({
    type: 'subscribe',
    channels: ['system.health', 'apex.deployments']
  }));
};

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};
```

## GraphQL API

### Endpoint
```
POST /graphql
```

### Example Query
```graphql
query GetProjectStatus($projectId: ID!) {
  project(id: $projectId) {
    id
    name
    status
    pipeline {
      stage
      progress
      estimatedCompletion
    }
    team {
      members {
        name
        role
      }
    }
  }
}
```

### Example Mutation
```graphql
mutation CreateProject($input: CreateProjectInput!) {
  createProject(input: $input) {
    id
    name
    status
    createdAt
  }
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    },
    "request_id": "req_123456789"
  }
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED` (401)
- `AUTHORIZATION_FAILED` (403)
- `RESOURCE_NOT_FOUND` (404)
- `VALIDATION_ERROR` (400)
- `RATE_LIMIT_EXCEEDED` (429)
- `INTERNAL_SERVER_ERROR` (500)
- `SERVICE_UNAVAILABLE` (503)

## Rate Limiting

### Limits
- **Standard**: 1000 requests/hour
- **Premium**: 10000 requests/hour
- **Enterprise**: Custom limits

### Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @mosaic/api-client
```

```javascript
import { MosaicClient } from '@mosaic/api-client';

const client = new MosaicClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.mosaic.yourdomain.com/v1'
});

const status = await client.getSystemStatus();
```

### Python
```bash
pip install mosaic-api-client
```

```python
from mosaic_client import MosaicClient

client = MosaicClient(api_key='your-api-key')
status = client.get_system_status()
```

### Go
```bash
go get github.com/mosaic/go-client
```

```go
import "github.com/mosaic/go-client"

client := mosaic.NewClient("your-api-key")
status, err := client.GetSystemStatus()
```

## Webhooks

### Configuration
```http
POST /webhooks
Content-Type: application/json

{
  "url": "https://your-app.com/webhooks/mosaic",
  "events": ["project.completed", "customer.health_changed"],
  "secret": "webhook-secret"
}
```

### Payload Example
```json
{
  "id": "evt_123456789",
  "type": "project.completed",
  "data": {
    "project_id": "proj_123",
    "completion_time": "2024-01-01T12:00:00Z"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Testing

### Sandbox Environment
```
Base URL: https://api-sandbox.mosaic.yourdomain.com/v1
API Key: sandbox_key_123456789
```

### Test Data
The sandbox environment includes:
- Sample projects and customers
- Mock event streams
- Simulated infrastructure metrics
- Test webhook endpoints

## Support

### API Status
- Status Page: https://status.mosaic.yourdomain.com
- API Changelog: https://docs.mosaic.yourdomain.com/changelog

### Getting Help
- Documentation: https://docs.mosaic.yourdomain.com
- Support Email: <EMAIL>
- Developer Forum: https://forum.mosaic.yourdomain.com
- GitHub Issues: https://github.com/mosaic/api-issues

## Version History

### v2.1.0 (Current)
- Added AURORA-LC customer success endpoints
- Enhanced event streaming capabilities
- Improved error handling and validation
- Added GraphQL API support

### v2.0.0
- Complete API redesign with lifecycle-based organization
- Added WebSocket support for real-time updates
- Implemented comprehensive authentication system
- Added rate limiting and security enhancements

### v1.0.0
- Initial API release
- Basic CRUD operations
- Simple authentication
- Core system status endpoints
