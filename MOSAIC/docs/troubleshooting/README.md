# MOSAIC Troubleshooting Guide

## Quick Diagnostics

### System Health Check
```bash
# Quick system overview
mosaic status

# Detailed health check
mosaic health --deep

# Component-specific checks
mosaic status --component kafka
mosaic status --component redis
mosaic status --component postgresql
```

### Common Issues Quick Fix
```bash
# Restart all lifecycles
mosaic restart --all

# Clear caches
mosaic exec pulse-lc "system.clearCaches()"

# Process dead letter queue
mosaic events dlq process

# Validate configuration
mosaic config validate
```

## Lifecycle Issues

### APEX-LC (Development Lifecycle)

#### Issue: APEX-LC Not Starting
**Symptoms:**
- Service fails to start
- Port binding errors
- Configuration validation failures

**Diagnosis:**
```bash
# Check configuration
mosaic config get lifecycles.apex-lc

# Check port availability
netstat -tulpn | grep 3001

# Check logs
mosaic logs apex-lc --tail 100 --level error
```

**Solutions:**
```bash
# Fix port conflicts
mosaic config set lifecycles.apex-lc.port 3011

# Restart with clean state
mosaic stop apex-lc
mosaic start apex-lc --clean

# Reset configuration
mosaic config reset lifecycles.apex-lc
```

#### Issue: AI Personas Not Responding
**Symptoms:**
- Slow or no responses from AI personas
- Timeout errors in development pipeline
- Incomplete code generation

**Diagnosis:**
```bash
# Check AI service connectivity
mosaic exec apex-lc "ai.testConnection()"

# Monitor resource usage
mosaic status --resources apex-lc

# Check API rate limits
mosaic logs apex-lc --pattern "rate.limit"
```

**Solutions:**
```bash
# Increase timeout settings
mosaic config set lifecycles.apex-lc.ai.timeout 60000

# Scale up resources
mosaic scale apex-lc --memory 4Gi --cpu 2000m

# Reset AI service connections
mosaic exec apex-lc "ai.reconnect()"
```

### PRISM-LC (Knowledge Management)

#### Issue: Search Not Working
**Symptoms:**
- Empty search results
- Slow search performance
- Index corruption errors

**Diagnosis:**
```bash
# Check search index status
mosaic exec prism-lc "search.indexStatus()"

# Test search functionality
mosaic exec prism-lc "search.test('test query')"

# Check storage usage
mosaic status --storage prism-lc
```

**Solutions:**
```bash
# Rebuild search index
mosaic exec prism-lc "search.rebuildIndex()"

# Clear search cache
mosaic exec prism-lc "search.clearCache()"

# Optimize index
mosaic exec prism-lc "search.optimize()"
```

#### Issue: Knowledge Ingestion Failing
**Symptoms:**
- Documents not being processed
- Extraction errors
- Missing metadata

**Diagnosis:**
```bash
# Check ingestion queue
mosaic exec prism-lc "ingestion.queueStatus()"

# Test document processing
mosaic exec prism-lc "ingestion.testDocument('/path/to/test.pdf')"

# Check supported formats
mosaic exec prism-lc "ingestion.supportedFormats()"
```

**Solutions:**
```bash
# Process failed documents
mosaic exec prism-lc "ingestion.retryFailed()"

# Update document processors
mosaic exec prism-lc "ingestion.updateProcessors()"

# Clear ingestion queue
mosaic exec prism-lc "ingestion.clearQueue()"
```

### AURORA-LC (Customer Success)

#### Issue: Customer Health Scores Incorrect
**Symptoms:**
- Inaccurate health scores
- Missing customer data
- Stale metrics

**Diagnosis:**
```bash
# Check health calculation
mosaic exec aurora-lc "health.debugScore('customer-123')"

# Verify data sources
mosaic exec aurora-lc "health.dataSources()"

# Check update frequency
mosaic exec aurora-lc "health.lastUpdate()"
```

**Solutions:**
```bash
# Recalculate all scores
mosaic exec aurora-lc "health.recalculateAll()"

# Update data sources
mosaic exec aurora-lc "health.refreshData()"

# Reset scoring algorithm
mosaic exec aurora-lc "health.resetAlgorithm()"
```

### PULSE-LC (Meta-Orchestration)

#### Issue: Resource Allocation Problems
**Symptoms:**
- Lifecycles running out of resources
- Uneven resource distribution
- Performance degradation

**Diagnosis:**
```bash
# Check resource allocation
mosaic exec pulse-lc "resources.currentAllocation()"

# Monitor resource usage
mosaic status --resources --watch

# Check allocation history
mosaic exec pulse-lc "resources.allocationHistory()"
```

**Solutions:**
```bash
# Rebalance resources
mosaic exec pulse-lc "resources.rebalance()"

# Set resource limits
mosaic scale apex-lc --cpu 1000m --memory 2Gi

# Enable auto-scaling
mosaic config set lifecycles.auto_scaling.enabled true
```

## Infrastructure Issues

### Event Bus (Kafka)

#### Issue: Event Processing Lag
**Symptoms:**
- High consumer lag
- Delayed event processing
- Timeout errors

**Diagnosis:**
```bash
# Check consumer lag
kafka-consumer-groups --bootstrap-server localhost:9092 --describe --all-groups

# Monitor event throughput
mosaic events stats --interval 1m

# Check topic configuration
kafka-topics --bootstrap-server localhost:9092 --describe --topic mosaic.lifecycle.events
```

**Solutions:**
```bash
# Increase consumer instances
mosaic config set eventBus.consumers.parallelism 8

# Optimize batch processing
mosaic config set eventBus.batchSize 1000
mosaic config set eventBus.flushInterval 100ms

# Process dead letter queue
mosaic events dlq process --max-retries 3
```

#### Issue: Schema Registry Problems
**Symptoms:**
- Schema validation errors
- Incompatible schema versions
- Registration failures

**Diagnosis:**
```bash
# Check schema registry health
curl http://localhost:8081/subjects

# List all schemas
curl http://localhost:8081/subjects

# Check specific schema
curl http://localhost:8081/subjects/mosaic.lifecycle.events-value/versions/latest
```

**Solutions:**
```bash
# Re-register schemas
mosaic exec pulse-lc "schema.registerAll()"

# Check schema compatibility
mosaic exec pulse-lc "schema.checkCompatibility('event-type')"

# Reset schema registry
docker-compose restart schema-registry
```

### Context Store (Redis + PostgreSQL)

#### Issue: Redis Connection Problems
**Symptoms:**
- Connection timeouts
- Memory issues
- Cluster node failures

**Diagnosis:**
```bash
# Check Redis connectivity
redis-cli ping

# Check memory usage
redis-cli info memory

# Check cluster status
redis-cli cluster info
```

**Solutions:**
```bash
# Restart Redis cluster
docker-compose restart redis-1 redis-2 redis-3

# Clear Redis cache
redis-cli flushall

# Optimize memory usage
redis-cli config set maxmemory-policy allkeys-lru
```

#### Issue: PostgreSQL Performance
**Symptoms:**
- Slow queries
- Connection pool exhaustion
- Lock contention

**Diagnosis:**
```bash
# Check active connections
psql -c "SELECT count(*) FROM pg_stat_activity;"

# Find slow queries
psql -c "SELECT query, mean_time FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Check locks
psql -c "SELECT * FROM pg_locks WHERE NOT granted;"
```

**Solutions:**
```bash
# Optimize queries
psql -c "ANALYZE;"
psql -c "VACUUM ANALYZE;"

# Increase connection pool
mosaic config set contextStore.postgresql.pool.max 20

# Kill long-running queries
psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'active' AND query_start < now() - interval '5 minutes';"
```

## Performance Issues

### High Memory Usage
**Symptoms:**
- Out of memory errors
- Slow performance
- System instability

**Diagnosis:**
```bash
# Check memory usage by lifecycle
mosaic status --resources

# Generate heap dump
mosaic exec apex-lc "process.generateHeapDump()"

# Monitor memory over time
mosaic metrics memory --watch --interval 30s
```

**Solutions:**
```bash
# Increase memory limits
mosaic scale apex-lc --memory 4Gi

# Enable garbage collection optimization
mosaic config set lifecycles.apex-lc.gc.optimization true

# Restart with clean state
mosaic restart apex-lc --clean
```

### High CPU Usage
**Symptoms:**
- Slow response times
- High system load
- Timeout errors

**Diagnosis:**
```bash
# Check CPU usage
mosaic status --resources --detailed

# Profile CPU usage
mosaic profile apex-lc --duration 60s

# Check for CPU-intensive operations
mosaic logs --pattern "cpu|performance" --level warn
```

**Solutions:**
```bash
# Scale horizontally
mosaic scale apex-lc --replicas 3

# Optimize processing
mosaic config set eventBus.batchSize 500
mosaic config set eventBus.parallelism 4

# Enable CPU throttling
mosaic config set lifecycles.apex-lc.cpu.throttle true
```

## Network Issues

### Connectivity Problems
**Symptoms:**
- Connection timeouts
- Intermittent failures
- DNS resolution errors

**Diagnosis:**
```bash
# Test network connectivity
ping kafka-1
ping redis-1
ping postgres-primary

# Check DNS resolution
nslookup kafka-1
nslookup redis-1

# Test port connectivity
telnet kafka-1 9092
telnet redis-1 6379
```

**Solutions:**
```bash
# Restart networking
docker-compose restart

# Update DNS configuration
echo "127.0.0.1 kafka-1 redis-1 postgres-primary" >> /etc/hosts

# Check firewall rules
sudo ufw status
sudo iptables -L
```

## Security Issues

### Authentication Problems
**Symptoms:**
- Login failures
- Token expiration errors
- Permission denied

**Diagnosis:**
```bash
# Check authentication service
mosaic exec pulse-lc "auth.status()"

# Validate tokens
mosaic exec pulse-lc "auth.validateToken('token')"

# Check user permissions
mosaic exec pulse-lc "auth.userPermissions('user-id')"
```

**Solutions:**
```bash
# Reset authentication
mosaic exec pulse-lc "auth.reset()"

# Refresh tokens
mosaic exec pulse-lc "auth.refreshAllTokens()"

# Update permissions
mosaic exec pulse-lc "auth.updatePermissions('user-id', ['read', 'write'])"
```

## Data Issues

### Data Corruption
**Symptoms:**
- Invalid data formats
- Missing records
- Inconsistent state

**Diagnosis:**
```bash
# Check data integrity
mosaic exec pulse-lc "data.integrityCheck()"

# Validate data formats
mosaic exec pulse-lc "data.validateFormats()"

# Check for missing data
mosaic exec pulse-lc "data.findMissing()"
```

**Solutions:**
```bash
# Repair corrupted data
mosaic exec pulse-lc "data.repair()"

# Restore from backup
mosaic config restore --input /var/backups/mosaic/config_20240101.yaml

# Rebuild data indexes
mosaic exec prism-lc "search.rebuildIndex()"
```

## Emergency Procedures

### System Recovery
```bash
# Emergency stop all services
mosaic stop --all --force

# Check system resources
df -h
free -h
ps aux | grep mosaic

# Start in safe mode
mosaic start --safe-mode

# Verify recovery
mosaic health --deep
```

### Data Recovery
```bash
# Create emergency backup
mosaic config backup --output /tmp/emergency_backup.yaml

# Restore from last known good backup
mosaic config restore --input /var/backups/mosaic/config_latest.yaml

# Verify data integrity
mosaic exec pulse-lc "data.integrityCheck()"
```

## Getting Help

### Log Collection
```bash
# Collect all logs
mosaic logs --export --format json --output /tmp/mosaic-logs/

# Create support bundle
mosaic support bundle --output /tmp/mosaic-support.tar.gz
```

### Support Channels
- **Emergency**: +1-555-MOSAIC-1
- **Email**: <EMAIL>
- **Chat**: Available in MOSAIC dashboard
- **Community**: https://community.mosaic.yourdomain.com

### Information to Include
- MOSAIC version: `mosaic --version`
- System configuration: `mosaic config export`
- Error logs: `mosaic logs --level error --since 1h`
- System status: `mosaic status --detailed`
- Resource usage: `mosaic status --resources`
