# MOSAIC Examples and Use Cases

## Overview

This directory contains practical examples, use cases, and integration patterns for MOSAIC implementation across different scenarios and organizational sizes.

## Directory Structure

```
examples/
├── basic-setup/           # Getting started examples
├── integrations/          # Third-party integrations
├── workflows/             # Common workflow patterns
├── automation/            # Automation examples
├── api-usage/             # API integration examples
├── deployment/            # Deployment configurations
└── use-cases/             # Real-world scenarios
```

## Quick Start Examples

### 1. Solo Developer Setup
Perfect for individual developers or small projects.

**Configuration:**
```yaml
# solo-developer.yaml
environment: development
scale: minimal

lifecycles:
  apex-lc:
    enabled: true
    replicas: 1
    personas: ["developer", "tester"]
  prism-lc:
    enabled: true
    replicas: 1
    knowledge_sources: ["local_docs", "code_repos"]
  pulse-lc:
    enabled: true
    replicas: 1

infrastructure:
  kafka:
    single_node: true
  redis:
    single_instance: true
  postgresql:
    embedded: true
```

**Setup Commands:**
```bash
# Initialize MOSAIC for solo development
mosaic init --template solo-developer
mosaic deploy --config solo-developer.yaml
mosaic status
```

### 2. Small Team Setup (5-10 people)
Ideal for startups and small development teams.

**Configuration:**
```yaml
# small-team.yaml
environment: production
scale: small

lifecycles:
  apex-lc:
    enabled: true
    replicas: 2
    personas: ["architect", "developer", "tester", "devops"]
  prism-lc:
    enabled: true
    replicas: 1
    knowledge_sources: ["documentation", "meeting_notes", "code_repos"]
  aurora-lc:
    enabled: true
    replicas: 1
    customer_success: true
  pulse-lc:
    enabled: true
    replicas: 1

infrastructure:
  kafka:
    cluster_size: 3
  redis:
    cluster: true
    nodes: 3
  postgresql:
    primary_replica: true
```

### 3. Enterprise Setup (100+ people)
Full-scale enterprise deployment with all features.

**Configuration:**
```yaml
# enterprise.yaml
environment: production
scale: enterprise

lifecycles:
  apex-lc:
    enabled: true
    replicas: 10
    personas: ["architect", "developer", "tester", "devops", "security"]
  prism-lc:
    enabled: true
    replicas: 5
    knowledge_sources: ["all"]
  aurora-lc:
    enabled: true
    replicas: 3
    customer_success: true
    advanced_analytics: true
  nexus-lc:
    enabled: true
    replicas: 2
  flux-lc:
    enabled: true
    replicas: 2
  spark-lc:
    enabled: true
    replicas: 2
  shield-lc:
    enabled: true
    replicas: 3
  quantum-lc:
    enabled: true
    replicas: 2
  echo-lc:
    enabled: true
    replicas: 2
  pulse-lc:
    enabled: true
    replicas: 2
  flow-lc:
    enabled: true
    replicas: 2

infrastructure:
  kafka:
    cluster_size: 9
    replication_factor: 3
  redis:
    cluster: true
    nodes: 9
  postgresql:
    cluster: true
    primary: 1
    replicas: 3
```

## Integration Examples

### 1. GitHub Integration
Automatic project creation and management.

```typescript
// github-integration.ts
import { MosaicClient } from '@mosaic/api-client';
import { Octokit } from '@octokit/rest';

class GitHubMosaicIntegration {
  constructor(
    private mosaic: MosaicClient,
    private github: Octokit
  ) {}

  async onRepositoryCreated(repo: any) {
    // Create MOSAIC project when GitHub repo is created
    const project = await this.mosaic.apex.createProject({
      name: repo.name,
      description: repo.description,
      repository: repo.clone_url,
      template: 'github-repo'
    });

    // Set up automated workflows
    await this.mosaic.apex.setupWorkflows(project.id, {
      ci_cd: true,
      code_review: true,
      deployment: repo.private ? 'staging' : 'production'
    });

    return project;
  }

  async onPullRequest(pr: any) {
    // Trigger APEX-LC code review
    await this.mosaic.apex.triggerCodeReview({
      repository: pr.base.repo.name,
      pull_request: pr.number,
      files: pr.changed_files
    });

    // Update PRISM-LC with new knowledge
    await this.mosaic.prism.addKnowledge({
      title: `PR: ${pr.title}`,
      content: pr.body,
      source: 'github_pr',
      tags: ['pull_request', 'code_review']
    });
  }
}
```

### 2. Slack Integration
Team communication and notifications.

```typescript
// slack-integration.ts
import { App } from '@slack/bolt';
import { MosaicClient } from '@mosaic/api-client';

class SlackMosaicIntegration {
  private app: App;
  private mosaic: MosaicClient;

  constructor(signingSecret: string, token: string, mosaicApiKey: string) {
    this.app = new App({
      signingSecret,
      token
    });
    this.mosaic = new MosaicClient({ apiKey: mosaicApiKey });
    this.setupCommands();
  }

  private setupCommands() {
    // /mosaic status command
    this.app.command('/mosaic-status', async ({ command, ack, respond }) => {
      await ack();
      
      const status = await this.mosaic.getSystemStatus();
      await respond({
        text: `MOSAIC System Status: ${status.status}`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*System Status:* ${status.status}\n*Uptime:* ${status.uptime}s`
            }
          },
          {
            type: 'section',
            fields: Object.entries(status.lifecycles).map(([name, lc]) => ({
              type: 'mrkdwn',
              text: `*${name}:* ${lc.status}`
            }))
          }
        ]
      });
    });

    // /mosaic deploy command
    this.app.command('/mosaic-deploy', async ({ command, ack, respond }) => {
      await ack();
      
      const deployment = await this.mosaic.apex.triggerDeployment({
        environment: command.text || 'staging',
        notify_slack: true,
        channel: command.channel_id
      });

      await respond(`Deployment started: ${deployment.id}`);
    });
  }

  async notifyDeployment(deployment: any, channel: string) {
    await this.app.client.chat.postMessage({
      channel,
      text: `Deployment ${deployment.status}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Deployment ${deployment.id}*\n*Status:* ${deployment.status}\n*Environment:* ${deployment.environment}`
          }
        }
      ]
    });
  }
}
```

### 3. Home Assistant Integration
Smart home and workspace automation.

```typescript
// home-assistant-integration.ts
import { MosaicClient } from '@mosaic/api-client';
import { HomeAssistant } from '@hakit/core';

class HomeAssistantMosaicIntegration {
  constructor(
    private mosaic: MosaicClient,
    private ha: HomeAssistant
  ) {
    this.setupAutomations();
  }

  private setupAutomations() {
    // Focus mode automation
    this.mosaic.events.subscribe('user.focus_mode_started', async (event) => {
      await this.ha.callService('light', 'turn_on', {
        entity_id: 'light.office_lights',
        brightness: 180,
        color_temp: 250
      });

      await this.ha.callService('climate', 'set_temperature', {
        entity_id: 'climate.office',
        temperature: 22
      });

      await this.ha.callService('media_player', 'play_media', {
        entity_id: 'media_player.office_speaker',
        media_content_id: 'focus_playlist',
        media_content_type: 'playlist'
      });
    });

    // Meeting mode automation
    this.mosaic.events.subscribe('user.meeting_started', async (event) => {
      await this.ha.callService('light', 'turn_on', {
        entity_id: 'light.office_lights',
        brightness: 255,
        color_temp: 200
      });

      await this.ha.callService('switch', 'turn_on', {
        entity_id: 'switch.do_not_disturb'
      });
    });

    // Health monitoring
    this.ha.subscribe('sensor.heart_rate', async (state) => {
      if (parseInt(state.state) > 100) {
        await this.mosaic.aurora.recordHealthMetric({
          user_id: 'current_user',
          metric: 'heart_rate',
          value: parseInt(state.state),
          timestamp: new Date(),
          alert: state.state > 120
        });
      }
    });
  }

  async setupWorkspaceScene(mode: 'focus' | 'meeting' | 'break' | 'away') {
    const scenes = {
      focus: {
        lights: { brightness: 180, color_temp: 250 },
        climate: { temperature: 22 },
        audio: 'focus_playlist'
      },
      meeting: {
        lights: { brightness: 255, color_temp: 200 },
        climate: { temperature: 21 },
        audio: 'off'
      },
      break: {
        lights: { brightness: 120, color_temp: 300 },
        climate: { temperature: 23 },
        audio: 'relaxing_playlist'
      },
      away: {
        lights: 'off',
        climate: { temperature: 18 },
        audio: 'off'
      }
    };

    const scene = scenes[mode];
    
    // Apply scene settings
    if (scene.lights !== 'off') {
      await this.ha.callService('light', 'turn_on', {
        entity_id: 'light.office_lights',
        ...scene.lights
      });
    } else {
      await this.ha.callService('light', 'turn_off', {
        entity_id: 'light.office_lights'
      });
    }

    await this.ha.callService('climate', 'set_temperature', {
      entity_id: 'climate.office',
      ...scene.climate
    });

    if (scene.audio !== 'off') {
      await this.ha.callService('media_player', 'play_media', {
        entity_id: 'media_player.office_speaker',
        media_content_id: scene.audio,
        media_content_type: 'playlist'
      });
    }
  }
}
```

## Workflow Examples

### 1. Idea to MVP Pipeline
Complete 8-hour development workflow.

```typescript
// idea-to-mvp-workflow.ts
export class IdeaToMVPWorkflow {
  async execute(idea: string): Promise<string> {
    console.log('🚀 Starting Idea to MVP Pipeline');
    
    // Hour 1-2: Architecture and Planning
    const architecture = await this.mosaic.apex.generateArchitecture({
      idea,
      constraints: ['8_hour_timeline', 'mvp_scope'],
      personas: ['architect']
    });

    // Hour 2-4: Core Development
    const codebase = await this.mosaic.apex.generateCode({
      architecture,
      features: architecture.core_features,
      personas: ['developer']
    });

    // Hour 4-5: Testing
    const tests = await this.mosaic.apex.generateTests({
      codebase,
      coverage_target: 80,
      personas: ['tester']
    });

    // Hour 5-6: Integration and Deployment
    const deployment = await this.mosaic.apex.deploy({
      codebase,
      tests,
      environment: 'staging',
      personas: ['devops']
    });

    // Hour 6-7: Documentation
    const documentation = await this.mosaic.prism.generateDocumentation({
      project: codebase.project_id,
      include: ['api', 'user_guide', 'deployment']
    });

    // Hour 7-8: Customer Feedback Setup
    await this.mosaic.aurora.setupFeedbackCollection({
      project: codebase.project_id,
      channels: ['in_app', 'email', 'analytics']
    });

    return deployment.url;
  }
}
```

### 2. Customer Onboarding Automation
Automated customer success workflow.

```typescript
// customer-onboarding-workflow.ts
export class CustomerOnboardingWorkflow {
  async onNewCustomer(customer: any) {
    // Create customer profile
    const profile = await this.mosaic.aurora.createCustomerProfile({
      id: customer.id,
      email: customer.email,
      plan: customer.subscription_plan,
      signup_date: new Date()
    });

    // Set up onboarding journey
    const journey = await this.mosaic.aurora.createJourney({
      customer_id: customer.id,
      type: 'onboarding',
      steps: [
        'welcome_email',
        'account_setup',
        'first_project',
        'feature_tour',
        'success_metrics'
      ]
    });

    // Schedule follow-up touchpoints
    await this.mosaic.aurora.scheduleFollowUps({
      customer_id: customer.id,
      touchpoints: [
        { day: 1, type: 'welcome_call' },
        { day: 7, type: 'progress_check' },
        { day: 30, type: 'success_review' }
      ]
    });

    return journey;
  }

  async trackOnboardingProgress(customer_id: string, step: string) {
    await this.mosaic.aurora.updateJourneyProgress({
      customer_id,
      step,
      completed_at: new Date()
    });

    // Trigger next step
    const nextStep = await this.mosaic.aurora.getNextJourneyStep(customer_id);
    if (nextStep) {
      await this.mosaic.aurora.triggerJourneyStep({
        customer_id,
        step: nextStep.id
      });
    }
  }
}
```

## API Usage Examples

### 1. Custom Dashboard
Building a custom monitoring dashboard.

```typescript
// custom-dashboard.ts
import React, { useState, useEffect } from 'react';
import { MosaicClient } from '@mosaic/api-client';

export const MosaicDashboard: React.FC = () => {
  const [status, setStatus] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const mosaic = new MosaicClient({ apiKey: process.env.MOSAIC_API_KEY });

  useEffect(() => {
    const fetchData = async () => {
      const [statusData, metricsData] = await Promise.all([
        mosaic.getSystemStatus(),
        mosaic.pulse.getMetrics({ timeframe: '1h' })
      ]);
      
      setStatus(statusData);
      setMetrics(metricsData);
    };

    fetchData();
    const interval = setInterval(fetchData, 30000); // Update every 30s
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="dashboard">
      <div className="status-grid">
        {status?.lifecycles && Object.entries(status.lifecycles).map(([name, lc]) => (
          <div key={name} className={`status-card ${lc.health}`}>
            <h3>{name}</h3>
            <p>Status: {lc.status}</p>
            <p>Health: {lc.health}</p>
          </div>
        ))}
      </div>
      
      <div className="metrics-section">
        {metrics && (
          <div className="metrics-grid">
            <div className="metric">
              <h4>Events/min</h4>
              <p>{metrics.events_per_minute}</p>
            </div>
            <div className="metric">
              <h4>Response Time</h4>
              <p>{metrics.avg_response_time}ms</p>
            </div>
            <div className="metric">
              <h4>Error Rate</h4>
              <p>{metrics.error_rate}%</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
```

### 2. CLI Extension
Custom CLI commands for specific workflows.

```typescript
// custom-cli-commands.ts
import { Command } from 'commander';
import { MosaicClient } from '@mosaic/api-client';

const program = new Command();
const mosaic = new MosaicClient({ apiKey: process.env.MOSAIC_API_KEY });

program
  .command('deploy-feature <feature-name>')
  .description('Deploy a specific feature')
  .option('-e, --environment <env>', 'deployment environment', 'staging')
  .action(async (featureName, options) => {
    console.log(`Deploying feature: ${featureName} to ${options.environment}`);
    
    const deployment = await mosaic.apex.deployFeature({
      feature: featureName,
      environment: options.environment,
      wait: true
    });
    
    console.log(`Deployment completed: ${deployment.url}`);
  });

program
  .command('customer-health <customer-id>')
  .description('Check customer health score')
  .action(async (customerId) => {
    const health = await mosaic.aurora.getCustomerHealth(customerId);
    
    console.log(`Customer Health Score: ${health.score}/100`);
    console.log(`Status: ${health.status}`);
    console.log(`Last Activity: ${health.last_activity}`);
    
    if (health.risks.length > 0) {
      console.log('\nRisk Factors:');
      health.risks.forEach(risk => console.log(`- ${risk}`));
    }
  });

program.parse();
```

## Best Practices

### 1. Error Handling
```typescript
// error-handling-example.ts
export class RobustMosaicIntegration {
  private async safeApiCall<T>(
    operation: () => Promise<T>,
    fallback?: T,
    retries = 3
  ): Promise<T | null> {
    for (let i = 0; i < retries; i++) {
      try {
        return await operation();
      } catch (error) {
        console.error(`Attempt ${i + 1} failed:`, error);
        
        if (i === retries - 1) {
          if (fallback !== undefined) {
            return fallback;
          }
          throw error;
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
      }
    }
    
    return null;
  }

  async getSystemStatusSafely() {
    return this.safeApiCall(
      () => this.mosaic.getSystemStatus(),
      { status: 'unknown', message: 'Unable to fetch status' }
    );
  }
}
```

### 2. Performance Optimization
```typescript
// performance-optimization.ts
export class OptimizedMosaicClient {
  private cache = new Map();
  private batchQueue = [];
  private batchTimer: NodeJS.Timeout | null = null;

  async getCachedData(key: string, fetcher: () => Promise<any>, ttl = 60000) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data;
    }

    const data = await fetcher();
    this.cache.set(key, { data, timestamp: Date.now() });
    return data;
  }

  async batchRequest(request: any) {
    return new Promise((resolve, reject) => {
      this.batchQueue.push({ request, resolve, reject });
      
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => this.processBatch(), 100);
      }
    });
  }

  private async processBatch() {
    const batch = this.batchQueue.splice(0);
    this.batchTimer = null;

    try {
      const results = await this.mosaic.batch(batch.map(item => item.request));
      batch.forEach((item, index) => item.resolve(results[index]));
    } catch (error) {
      batch.forEach(item => item.reject(error));
    }
  }
}
```

This examples directory provides comprehensive patterns and use cases for implementing MOSAIC across different scenarios, from solo developers to enterprise deployments.
