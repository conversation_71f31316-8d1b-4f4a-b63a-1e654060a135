#!/usr/bin/with-contenv bashio
# ==============================================================================
# MOSAIC Home Assistant Add-on
# Main execution script for MOSAIC lifecycle management
# ==============================================================================

set -e

# Configuration
CONFIG_PATH="/data/options.json"
MOSAIC_CONFIG_PATH="/config/mosaic"
MOSAIC_DATA_PATH="/data/mosaic"
LOG_LEVEL=$(bashio::config 'log_level')
DEBUG=$(bashio::config 'debug')

# Set up logging
bashio::log.info "Starting MOSAIC Lifecycle Manager..."
bashio::log.info "Log level: ${LOG_LEVEL}"

if bashio::config.true 'debug'; then
    bashio::log.info "Debug mode enabled"
    set -x
fi

# Create necessary directories
bashio::log.info "Creating MOSAIC directories..."
mkdir -p "${MOSAIC_CONFIG_PATH}"
mkdir -p "${MOSAIC_DATA_PATH}"
mkdir -p "${MOSAIC_DATA_PATH}/logs"
mkdir -p "${MOSAIC_DATA_PATH}/backups"
mkdir -p "${MOSAIC_DATA_PATH}/exports"

# Set permissions
chmod -R 755 "${MOSAIC_CONFIG_PATH}"
chmod -R 755 "${MOSAIC_DATA_PATH}"

# Generate MOSAIC configuration
bashio::log.info "Generating MOSAIC configuration..."
cat > "${MOSAIC_CONFIG_PATH}/config.yaml" << EOF
# MOSAIC Configuration Generated by Home Assistant Add-on
mosaic:
  version: "1.0.0"
  environment: "home_assistant"
  config_path: "${MOSAIC_CONFIG_PATH}"
  data_path: "${MOSAIC_DATA_PATH}"
  log_level: "${LOG_LEVEL}"
  debug: $(bashio::config 'debug')

# Home Assistant Integration
home_assistant:
  url: "${HOME_ASSISTANT_URL:-http://supervisor/core}"
  token: "${SUPERVISOR_TOKEN}"
  websocket_url: "ws://supervisor/core/api/websocket"

# Lifecycle Configuration
lifecycles:
$(for lifecycle in apex_lc prism_lc aurora_lc pulse_lc flow_lc; do
    if bashio::config.true "lifecycles.${lifecycle}.enabled"; then
        echo "  ${lifecycle}:"
        echo "    enabled: true"
        echo "    priority: \"$(bashio::config "lifecycles.${lifecycle}.priority")\""
        echo "    auto_start: $(bashio::config "lifecycles.${lifecycle}.auto_start")"
    fi
done)

# Dashboard Configuration
dashboard:
  enabled: $(bashio::config 'dashboard.enabled')
  port: $(bashio::config 'dashboard.port')
  ssl: $(bashio::config 'dashboard.ssl')
$(if bashio::config.true 'dashboard.ssl'; then
    echo "  ssl_cert: \"$(bashio::config 'dashboard.ssl_cert')\""
    echo "  ssl_key: \"$(bashio::config 'dashboard.ssl_key')\""
fi)
  cors_allowed_origins:
$(bashio::config 'dashboard.cors_allowed_origins' | jq -r '.[] | "    - \"" + . + "\""')

# API Configuration
api:
  enabled: $(bashio::config 'api.enabled')
  port: $(bashio::config 'api.port')
  authentication: $(bashio::config 'api.authentication')
  rate_limiting: $(bashio::config 'api.rate_limiting')
  max_requests_per_minute: $(bashio::config 'api.max_requests_per_minute')

# WebSocket Configuration
websocket:
  enabled: $(bashio::config 'websocket.enabled')
  port: $(bashio::config 'websocket.port')
  heartbeat_interval: $(bashio::config 'websocket.heartbeat_interval')
  max_connections: $(bashio::config 'websocket.max_connections')

# Flow State Configuration
flow_state:
  enabled: $(bashio::config 'flow_state.enabled')
  monitoring_interval: $(bashio::config 'flow_state.monitoring_interval')
  deep_work_threshold: $(bashio::config 'flow_state.deep_work_threshold')
  break_reminder_interval: $(bashio::config 'flow_state.break_reminder_interval')
  environment_optimization: $(bashio::config 'flow_state.environment_optimization')
  biometric_integration: $(bashio::config 'flow_state.biometric_integration')

# Agent Monitoring Configuration
agent_monitoring:
  enabled: $(bashio::config 'agent_monitoring.enabled')
  health_check_interval: $(bashio::config 'agent_monitoring.health_check_interval')
  performance_metrics: $(bashio::config 'agent_monitoring.performance_metrics')
  resource_monitoring: $(bashio::config 'agent_monitoring.resource_monitoring')
  auto_restart_on_failure: $(bashio::config 'agent_monitoring.auto_restart_on_failure')

# Workspace Automation Configuration
workspace_automation:
  enabled: $(bashio::config 'workspace_automation.enabled')
  deep_work_protection: $(bashio::config 'workspace_automation.deep_work_protection')
  meeting_mode_automation: $(bashio::config 'workspace_automation.meeting_mode_automation')
  break_time_optimization: $(bashio::config 'workspace_automation.break_time_optimization')
  environment_presets: $(bashio::config 'workspace_automation.environment_presets')

# Biometric Integration Configuration
biometric:
  enabled: $(bashio::config 'biometric.enabled')
  apple_watch: $(bashio::config 'biometric.apple_watch')
  fitbit: $(bashio::config 'biometric.fitbit')
  manual_input: $(bashio::config 'biometric.manual_input')
  health_thresholds:
    heart_rate_max: $(bashio::config 'biometric.health_thresholds.heart_rate_max')
    stress_level_high: $(bashio::config 'biometric.health_thresholds.stress_level_high')
    activity_level_low: $(bashio::config 'biometric.health_thresholds.activity_level_low')

# Notification Configuration
notifications:
  enabled: $(bashio::config 'notifications.enabled')
  channels:
$(bashio::config 'notifications.channels' | jq -r '.[] | "    - \"" + . + "\""')
  flow_state_changes: $(bashio::config 'notifications.flow_state_changes')
  agent_status_alerts: $(bashio::config 'notifications.agent_status_alerts')
  environment_alerts: $(bashio::config 'notifications.environment_alerts')
  break_reminders: $(bashio::config 'notifications.break_reminders')

# Integration Configuration
integrations:
  calendar: $(bashio::config 'integrations.calendar')
  weather: $(bashio::config 'integrations.weather')
  security: $(bashio::config 'integrations.security')
  energy: $(bashio::config 'integrations.energy')
  media: $(bashio::config 'integrations.media')

# Storage Configuration
storage:
  retention_days: $(bashio::config 'storage.retention_days')
  backup_enabled: $(bashio::config 'storage.backup_enabled')
  backup_interval: "$(bashio::config 'storage.backup_interval')"
  export_format: "$(bashio::config 'storage.export_format')"
EOF

bashio::log.info "MOSAIC configuration generated successfully"

# Set up environment variables
export MOSAIC_CONFIG_PATH="${MOSAIC_CONFIG_PATH}"
export MOSAIC_DATA_PATH="${MOSAIC_DATA_PATH}"
export MOSAIC_LOG_LEVEL="${LOG_LEVEL}"
export HOME_ASSISTANT_URL="${HOME_ASSISTANT_URL:-http://supervisor/core}"
export SUPERVISOR_TOKEN="${SUPERVISOR_TOKEN}"

# Function to start MOSAIC services
start_mosaic_services() {
    bashio::log.info "Starting MOSAIC services..."
    
    # Start MOSAIC Core
    bashio::log.info "Starting MOSAIC Core..."
    cd /app/mosaic
    node index.js &
    MOSAIC_CORE_PID=$!
    
    # Start API Server if enabled
    if bashio::config.true 'api.enabled'; then
        bashio::log.info "Starting MOSAIC API Server..."
        cd /app/api
        node server.js &
        API_SERVER_PID=$!
    fi
    
    # Start WebSocket Server if enabled
    if bashio::config.true 'websocket.enabled'; then
        bashio::log.info "Starting MOSAIC WebSocket Server..."
        cd /app/websocket
        node server.js &
        WEBSOCKET_SERVER_PID=$!
    fi
    
    # Start React Dashboard if enabled
    if bashio::config.true 'dashboard.enabled'; then
        bashio::log.info "Starting MOSAIC React Dashboard..."
        cd /app/dashboard
        npm start &
        DASHBOARD_PID=$!
    fi
    
    bashio::log.info "All MOSAIC services started successfully"
}

# Function to stop MOSAIC services
stop_mosaic_services() {
    bashio::log.info "Stopping MOSAIC services..."
    
    if [ ! -z "${MOSAIC_CORE_PID}" ]; then
        kill -TERM "${MOSAIC_CORE_PID}" 2>/dev/null || true
    fi
    
    if [ ! -z "${API_SERVER_PID}" ]; then
        kill -TERM "${API_SERVER_PID}" 2>/dev/null || true
    fi
    
    if [ ! -z "${WEBSOCKET_SERVER_PID}" ]; then
        kill -TERM "${WEBSOCKET_SERVER_PID}" 2>/dev/null || true
    fi
    
    if [ ! -z "${DASHBOARD_PID}" ]; then
        kill -TERM "${DASHBOARD_PID}" 2>/dev/null || true
    fi
    
    bashio::log.info "All MOSAIC services stopped"
}

# Function to handle cleanup on exit
cleanup() {
    bashio::log.info "Received termination signal, cleaning up..."
    stop_mosaic_services
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Health check function
health_check() {
    local retries=0
    local max_retries=30
    
    while [ $retries -lt $max_retries ]; do
        if curl -f -s "http://localhost:$(bashio::config 'api.port')/health" > /dev/null 2>&1; then
            bashio::log.info "MOSAIC services are healthy"
            return 0
        fi
        
        retries=$((retries + 1))
        bashio::log.info "Waiting for MOSAIC services to be ready... (${retries}/${max_retries})"
        sleep 2
    done
    
    bashio::log.error "MOSAIC services failed to start properly"
    return 1
}

# Backup function
backup_data() {
    if bashio::config.true 'storage.backup_enabled'; then
        bashio::log.info "Creating backup..."
        local backup_file="${MOSAIC_DATA_PATH}/backups/mosaic-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
        tar -czf "${backup_file}" -C "${MOSAIC_DATA_PATH}" . --exclude="backups" --exclude="logs"
        bashio::log.info "Backup created: ${backup_file}"
        
        # Clean up old backups (keep last 7 days)
        find "${MOSAIC_DATA_PATH}/backups" -name "mosaic-backup-*.tar.gz" -mtime +7 -delete
    fi
}

# Schedule backup based on interval
schedule_backup() {
    local interval=$(bashio::config 'storage.backup_interval')
    case $interval in
        "hourly")
            echo "0 * * * * /usr/bin/backup_data" | crontab -
            ;;
        "daily")
            echo "0 2 * * * /usr/bin/backup_data" | crontab -
            ;;
        "weekly")
            echo "0 2 * * 0 /usr/bin/backup_data" | crontab -
            ;;
    esac
}

# Main execution
main() {
    bashio::log.info "Initializing MOSAIC Lifecycle Manager..."
    
    # Validate configuration
    if ! bashio::config.exists 'mosaic.enabled' || ! bashio::config.true 'mosaic.enabled'; then
        bashio::log.error "MOSAIC is not enabled in configuration"
        exit 1
    fi
    
    # Set up backup scheduling
    if bashio::config.true 'storage.backup_enabled'; then
        schedule_backup
        crond -b
    fi
    
    # Start MOSAIC services
    start_mosaic_services
    
    # Wait for services to be ready
    if ! health_check; then
        bashio::log.error "Failed to start MOSAIC services"
        stop_mosaic_services
        exit 1
    fi
    
    bashio::log.info "MOSAIC Lifecycle Manager is running successfully"
    bashio::log.info "Dashboard available at: http://localhost:$(bashio::config 'dashboard.port')"
    bashio::log.info "API available at: http://localhost:$(bashio::config 'api.port')"
    
    # Keep the script running
    while true; do
        sleep 30
        
        # Basic health monitoring
        if ! curl -f -s "http://localhost:$(bashio::config 'api.port')/health" > /dev/null 2>&1; then
            bashio::log.warning "MOSAIC API health check failed"
            
            if bashio::config.true 'agent_monitoring.auto_restart_on_failure'; then
                bashio::log.info "Auto-restarting MOSAIC services..."
                stop_mosaic_services
                sleep 5
                start_mosaic_services
                
                if ! health_check; then
                    bashio::log.error "Failed to restart MOSAIC services"
                    exit 1
                fi
            fi
        fi
    done
}

# Export functions for cron
export -f backup_data

# Run main function
main "$@"
