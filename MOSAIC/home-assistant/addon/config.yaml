# MOSAIC Home Assistant Add-on Configuration
name: "MOSAIC Lifecycle Manager"
version: "1.0.0"
slug: "mosaic-lifecycle-manager"
description: "Comprehensive lifecycle management and work-life synthesis automation for MOSAIC framework"
url: "https://github.com/alias-org/mosaic-home-assistant"
arch:
  - armhf
  - armv7
  - aarch64
  - amd64
  - i386
startup: services
boot: auto
init: false
hassio_api: true
hassio_role: manager
homeassistant_api: true
host_network: false
host_pid: false
host_ipc: false
privileged:
  - SYS_ADMIN
devices:
  - /dev/ttyUSB0
  - /dev/ttyACM0
ports:
  3000/tcp: 3000
  8080/tcp: 8080
  9090/tcp: 9090
ports_description:
  3000/tcp: "MOSAIC React Dashboard"
  8080/tcp: "MOSAIC API Server"
  9090/tcp: "MOSAIC WebSocket Server"

# Add-on options
options:
  # Core Configuration
  log_level: "info"
  debug: false
  
  # MOSAIC Configuration
  mosaic:
    enabled: true
    config_path: "/config/mosaic"
    data_path: "/data/mosaic"
    
  # Lifecycle Configuration
  lifecycles:
    apex_lc:
      enabled: true
      priority: "high"
      auto_start: true
    prism_lc:
      enabled: true
      priority: "medium"
      auto_start: true
    aurora_lc:
      enabled: true
      priority: "high"
      auto_start: true
    pulse_lc:
      enabled: true
      priority: "critical"
      auto_start: true
    flow_lc:
      enabled: true
      priority: "high"
      auto_start: true
  
  # Dashboard Configuration
  dashboard:
    enabled: true
    port: 3000
    ssl: false
    ssl_cert: "/ssl/fullchain.pem"
    ssl_key: "/ssl/privkey.pem"
    cors_allowed_origins:
      - "http://localhost:8123"
      - "https://your-domain.com"
  
  # API Configuration
  api:
    enabled: true
    port: 8080
    authentication: true
    rate_limiting: true
    max_requests_per_minute: 100
  
  # WebSocket Configuration
  websocket:
    enabled: true
    port: 9090
    heartbeat_interval: 30
    max_connections: 100
  
  # Flow State Monitoring
  flow_state:
    enabled: true
    monitoring_interval: 30
    deep_work_threshold: 80
    break_reminder_interval: 90
    environment_optimization: true
    biometric_integration: true
    
  # Agent Monitoring
  agent_monitoring:
    enabled: true
    health_check_interval: 60
    performance_metrics: true
    resource_monitoring: true
    auto_restart_on_failure: true
    
  # Smart Workspace Automation
  workspace_automation:
    enabled: true
    deep_work_protection: true
    meeting_mode_automation: true
    break_time_optimization: true
    environment_presets: true
    
  # Biometric Integration
  biometric:
    enabled: false
    apple_watch: false
    fitbit: false
    manual_input: true
    health_thresholds:
      heart_rate_max: 180
      stress_level_high: 80
      activity_level_low: 20
      
  # Notification Settings
  notifications:
    enabled: true
    channels:
      - "mobile_app"
      - "persistent_notification"
      - "email"
    flow_state_changes: true
    agent_status_alerts: true
    environment_alerts: true
    break_reminders: true
    
  # Integration Settings
  integrations:
    calendar: true
    weather: true
    security: true
    energy: true
    media: true
    
  # Data Storage
  storage:
    retention_days: 30
    backup_enabled: true
    backup_interval: "daily"
    export_format: "json"

# Schema for options validation
schema:
  log_level: "list(trace|debug|info|notice|warning|error|fatal)"
  debug: "bool"
  mosaic:
    enabled: "bool"
    config_path: "str"
    data_path: "str"
  lifecycles:
    apex_lc:
      enabled: "bool"
      priority: "list(low|medium|high|critical)"
      auto_start: "bool"
    prism_lc:
      enabled: "bool"
      priority: "list(low|medium|high|critical)"
      auto_start: "bool"
    aurora_lc:
      enabled: "bool"
      priority: "list(low|medium|high|critical)"
      auto_start: "bool"
    pulse_lc:
      enabled: "bool"
      priority: "list(low|medium|high|critical)"
      auto_start: "bool"
    flow_lc:
      enabled: "bool"
      priority: "list(low|medium|high|critical)"
      auto_start: "bool"
  dashboard:
    enabled: "bool"
    port: "port"
    ssl: "bool"
    ssl_cert: "str?"
    ssl_key: "str?"
    cors_allowed_origins: ["str"]
  api:
    enabled: "bool"
    port: "port"
    authentication: "bool"
    rate_limiting: "bool"
    max_requests_per_minute: "int(1,1000)"
  websocket:
    enabled: "bool"
    port: "port"
    heartbeat_interval: "int(10,300)"
    max_connections: "int(1,1000)"
  flow_state:
    enabled: "bool"
    monitoring_interval: "int(10,300)"
    deep_work_threshold: "int(50,100)"
    break_reminder_interval: "int(30,180)"
    environment_optimization: "bool"
    biometric_integration: "bool"
  agent_monitoring:
    enabled: "bool"
    health_check_interval: "int(30,300)"
    performance_metrics: "bool"
    resource_monitoring: "bool"
    auto_restart_on_failure: "bool"
  workspace_automation:
    enabled: "bool"
    deep_work_protection: "bool"
    meeting_mode_automation: "bool"
    break_time_optimization: "bool"
    environment_presets: "bool"
  biometric:
    enabled: "bool"
    apple_watch: "bool"
    fitbit: "bool"
    manual_input: "bool"
    health_thresholds:
      heart_rate_max: "int(100,220)"
      stress_level_high: "int(50,100)"
      activity_level_low: "int(0,50)"
  notifications:
    enabled: "bool"
    channels: ["str"]
    flow_state_changes: "bool"
    agent_status_alerts: "bool"
    environment_alerts: "bool"
    break_reminders: "bool"
  integrations:
    calendar: "bool"
    weather: "bool"
    security: "bool"
    energy: "bool"
    media: "bool"
  storage:
    retention_days: "int(1,365)"
    backup_enabled: "bool"
    backup_interval: "list(hourly|daily|weekly)"
    export_format: "list(json|csv|yaml)"

# Image configuration
image: "ghcr.io/alias-org/mosaic-home-assistant-{arch}"

# Map configuration
map:
  - "config:rw"
  - "ssl:ro"
  - "addons_config:rw"
  - "backup:rw"

# Environment variables
environment:
  MOSAIC_CONFIG_PATH: "/config/mosaic"
  MOSAIC_DATA_PATH: "/data/mosaic"
  MOSAIC_LOG_LEVEL: "info"
  HOME_ASSISTANT_URL: "http://supervisor/core"
  SUPERVISOR_TOKEN: ""

# Services
services:
  - "mqtt:want"
  - "mysql:want"
  - "postgresql:want"

# Panel iframe configuration
panel_iframe:
  title: "MOSAIC Dashboard"
  icon: "mdi:brain"
  url: "http://localhost:3000"
  require_admin: false

# Documentation
documentation: "https://docs.alias.org/mosaic/home-assistant"
repository: "https://github.com/alias-org/mosaic-home-assistant"
codenotary: "<EMAIL>"

# Translations
translations:
  en:
    configuration:
      log_level:
        name: "Log Level"
        description: "Set the logging level for the add-on"
      mosaic:
        name: "MOSAIC Configuration"
        description: "Core MOSAIC framework settings"
      dashboard:
        name: "Dashboard Settings"
        description: "React dashboard configuration"
      flow_state:
        name: "Flow State Monitoring"
        description: "Configure flow state tracking and optimization"
