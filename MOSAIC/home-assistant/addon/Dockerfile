# MOSAIC Home Assistant Add-on Dockerfile
ARG BUILD_FROM=ghcr.io/hassio-addons/base:15.0.1
FROM $BUILD_FROM

# Set shell
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Set environment variables
ENV \
    DEBIAN_FRONTEND=noninteractive \
    LANG=C.UTF-8 \
    NODE_VERSION=20.10.0 \
    YARN_VERSION=1.22.19

# Install system dependencies
RUN \
    apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
        python3 \
        python3-pip \
        python3-dev \
        pkg-config \
        libcairo2-dev \
        libpango1.0-dev \
        libjpeg-dev \
        libgif-dev \
        librsvg2-dev \
        libffi-dev \
        libssl-dev \
        redis-server \
        postgresql-client \
        nginx \
        supervisor \
        cron \
        jq \
        ca-certificates \
        gnupg \
        lsb-release \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Node.js and npm
RUN \
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest yarn@${YARN_VERSION} pm2

# Install Python dependencies
RUN \
    pip3 install --no-cache-dir \
        paho-mqtt \
        requests \
        websockets \
        aiohttp \
        asyncio \
        pyyaml \
        psycopg2-binary \
        redis

# Create application directories
RUN \
    mkdir -p /app/mosaic \
    && mkdir -p /app/api \
    && mkdir -p /app/websocket \
    && mkdir -p /app/dashboard \
    && mkdir -p /config/mosaic \
    && mkdir -p /data/mosaic

# Copy MOSAIC core files
COPY mosaic/ /app/mosaic/
COPY infrastructure/ /app/mosaic/infrastructure/
COPY shared/ /app/mosaic/shared/

# Copy API server files
COPY api/ /app/api/

# Copy WebSocket server files
COPY websocket/ /app/websocket/

# Copy React dashboard files
COPY react-components/ /app/dashboard/

# Copy add-on specific files
COPY rootfs/ /

# Set up MOSAIC core
WORKDIR /app/mosaic
RUN \
    if [ -f package.json ]; then \
        npm ci --only=production \
        && npm cache clean --force; \
    fi

# Set up API server
WORKDIR /app/api
COPY api/package.json api/package-lock.json ./
RUN \
    npm ci --only=production \
    && npm cache clean --force

# Set up WebSocket server
WORKDIR /app/websocket
COPY websocket/package.json websocket/package-lock.json ./
RUN \
    npm ci --only=production \
    && npm cache clean --force

# Set up React dashboard
WORKDIR /app/dashboard
COPY react-components/package.json react-components/package-lock.json ./
RUN \
    npm ci \
    && npm run build \
    && npm cache clean --force

# Configure nginx for dashboard
COPY nginx.conf /etc/nginx/nginx.conf
COPY dashboard.conf /etc/nginx/sites-available/dashboard
RUN \
    ln -s /etc/nginx/sites-available/dashboard /etc/nginx/sites-enabled/dashboard \
    && rm -f /etc/nginx/sites-enabled/default

# Configure supervisor
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set up permissions
RUN \
    chmod +x /etc/services.d/*/run \
    && chmod +x /etc/services.d/*/finish \
    && chmod +x /etc/cont-init.d/* \
    && chmod +x /usr/bin/backup_data

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Labels
LABEL \
    io.hass.name="MOSAIC Lifecycle Manager" \
    io.hass.description="Comprehensive lifecycle management and work-life synthesis automation" \
    io.hass.arch="${BUILD_ARCH}" \
    io.hass.type="addon" \
    io.hass.version="${BUILD_VERSION}" \
    maintainer="ALIAS Organization <<EMAIL>>" \
    org.opencontainers.image.title="MOSAIC Home Assistant Add-on" \
    org.opencontainers.image.description="MOSAIC Lifecycle Manager for Home Assistant" \
    org.opencontainers.image.vendor="ALIAS Organization" \
    org.opencontainers.image.authors="ALIAS Organization <<EMAIL>>" \
    org.opencontainers.image.licenses="MIT" \
    org.opencontainers.image.url="https://github.com/alias-org/mosaic-home-assistant" \
    org.opencontainers.image.source="https://github.com/alias-org/mosaic-home-assistant" \
    org.opencontainers.image.documentation="https://docs.alias.org/mosaic/home-assistant" \
    org.opencontainers.image.created="${BUILD_DATE}" \
    org.opencontainers.image.revision="${BUILD_REF}" \
    org.opencontainers.image.version="${BUILD_VERSION}"

# Expose ports
EXPOSE 3000 8080 9090

# Set working directory
WORKDIR /app

# Default command
CMD ["/etc/services.d/mosaic/run"]
