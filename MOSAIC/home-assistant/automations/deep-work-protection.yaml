# Deep Work Protection Automation
# Automatically optimizes environment and blocks interruptions during deep work sessions

automation:
  - alias: "MOSAIC: Enter Deep Work Mode"
    description: "Activate deep work environment when flow state is detected"
    trigger:
      - platform: state
        entity_id: sensor.mosaic_flow_state
        to: "deep_work"
      - platform: state
        entity_id: input_boolean.manual_deep_work
        to: "on"
      - platform: event
        event_type: mosaic_lifecycle_event
        event_data:
          lifecycle: "FLOW_LC"
          event_type: "deep_work_started"
    condition:
      - condition: state
        entity_id: input_boolean.deep_work_protection_enabled
        state: "on"
      - condition: time
        after: "07:00:00"
        before: "22:00:00"
    action:
      # Lighting optimization
      - service: light.turn_on
        target:
          entity_id: light.office_main
        data:
          brightness: 180
          color_temp: 4000
          transition: 5
      
      # Disable secondary lights to reduce distractions
      - service: light.turn_off
        target:
          entity_id: 
            - light.office_accent
            - light.office_rgb_strip
        data:
          transition: 2
      
      # Climate optimization
      - service: climate.set_temperature
        target:
          entity_id: climate.office_ac
        data:
          temperature: 22
          hvac_mode: "cool"
      
      # Audio environment
      - service: media_player.volume_set
        target:
          entity_id: media_player.office_speakers
        data:
          volume_level: 0.3
      
      - service: media_player.play_media
        target:
          entity_id: media_player.office_speakers
        data:
          media_content_id: "spotify:playlist:37i9dQZF1DX0SM0LYsmbMT"
          media_content_type: "music"
      
      # Security and privacy
      - service: lock.lock
        target:
          entity_id: lock.office_door
      
      - service: cover.close_cover
        target:
          entity_id: cover.office_blinds
        data:
          position: 75  # Partial close for natural light
      
      # Notification blocking
      - service: switch.turn_on
        target:
          entity_id: switch.do_not_disturb_mode
      
      # Phone to silent mode (if integrated)
      - service: notify.mobile_app_iphone
        data:
          message: "command_dnd"
          data:
            command: "request_location_update"
      
      # Computer notifications (via MOSAIC API)
      - service: rest_command.mosaic_api_call
        data:
          endpoint: "/api/notifications/block"
          method: "POST"
          payload:
            duration: 3600  # 1 hour
            exceptions: ["critical", "emergency"]
      
      # Calendar integration - block meeting requests
      - service: rest_command.calendar_block_time
        data:
          start_time: "{{ now() }}"
          duration: 120  # 2 hours default
          title: "🎯 Deep Work Session"
          description: "Focused work time - please do not disturb"
      
      # Air quality optimization
      - service: fan.turn_on
        target:
          entity_id: fan.office_air_purifier
        data:
          speed: "medium"
      
      # Biometric monitoring alert
      - service: notify.mosaic_dashboard
        data:
          title: "Deep Work Mode Activated"
          message: "Environment optimized for focus. Monitoring biometrics for optimal performance."
          data:
            priority: "normal"
            lifecycle: "FLOW_LC"
            actions:
              - action: "extend_session"
                title: "Extend Session"
              - action: "end_session"
                title: "End Session"

  - alias: "MOSAIC: Exit Deep Work Mode"
    description: "Restore normal environment when deep work session ends"
    trigger:
      - platform: state
        entity_id: sensor.mosaic_flow_state
        from: "deep_work"
      - platform: state
        entity_id: input_boolean.manual_deep_work
        to: "off"
      - platform: event
        event_type: mosaic_lifecycle_event
        event_data:
          lifecycle: "FLOW_LC"
          event_type: "deep_work_ended"
      - platform: state
        entity_id: sensor.mosaic_focus_score
        below: 60
        for:
          minutes: 10
    action:
      # Restore normal lighting
      - service: light.turn_on
        target:
          entity_id: 
            - light.office_main
            - light.office_accent
        data:
          brightness: 150
          color_temp: 3000
          transition: 10
      
      # Restore climate to comfort settings
      - service: climate.set_temperature
        target:
          entity_id: climate.office_ac
        data:
          temperature: 24
      
      # Restore audio
      - service: media_player.volume_set
        target:
          entity_id: media_player.office_speakers
        data:
          volume_level: 0.5
      
      # Unlock office
      - service: lock.unlock
        target:
          entity_id: lock.office_door
      
      # Open blinds partially
      - service: cover.open_cover
        target:
          entity_id: cover.office_blinds
        data:
          position: 50
      
      # Disable do not disturb
      - service: switch.turn_off
        target:
          entity_id: switch.do_not_disturb_mode
      
      # Re-enable notifications
      - service: rest_command.mosaic_api_call
        data:
          endpoint: "/api/notifications/unblock"
          method: "POST"
      
      # Phone back to normal
      - service: notify.mobile_app_iphone
        data:
          message: "command_dnd_off"
      
      # Session summary notification
      - service: notify.mosaic_dashboard
        data:
          title: "Deep Work Session Complete"
          message: "Session duration: {{ states('sensor.mosaic_session_duration') }} minutes. Focus score: {{ states('sensor.mosaic_focus_score') }}%"
          data:
            priority: "normal"
            lifecycle: "FLOW_LC"
            actions:
              - action: "view_analytics"
                title: "View Analytics"
              - action: "schedule_break"
                title: "Schedule Break"

  - alias: "MOSAIC: Interruption Detection"
    description: "Handle interruptions during deep work sessions"
    trigger:
      - platform: state
        entity_id: binary_sensor.office_motion
        to: "on"
      - platform: state
        entity_id: binary_sensor.office_door
        to: "on"
      - platform: event
        event_type: call_service
        event_data:
          domain: "notify"
    condition:
      - condition: state
        entity_id: sensor.mosaic_flow_state
        state: "deep_work"
      - condition: state
        entity_id: input_boolean.interruption_tracking_enabled
        state: "on"
    action:
      # Log interruption
      - service: rest_command.mosaic_api_call
        data:
          endpoint: "/api/flow-state/interruption"
          method: "POST"
          payload:
            timestamp: "{{ now() }}"
            type: "{{ trigger.entity_id }}"
            source: "{{ trigger.platform }}"
      
      # Gentle notification to user
      - service: light.turn_on
        target:
          entity_id: light.office_accent
        data:
          color_name: "orange"
          brightness: 100
          flash: "short"
      
      # Update focus score
      - service: input_number.set_value
        target:
          entity_id: input_number.current_focus_score
        data:
          value: "{{ states('input_number.current_focus_score') | float - 5 }}"

  - alias: "MOSAIC: Break Reminder"
    description: "Remind user to take breaks during extended work sessions"
    trigger:
      - platform: template
        value_template: "{{ states('sensor.mosaic_session_duration') | int > states('input_number.break_reminder_interval') | int }}"
    condition:
      - condition: state
        entity_id: sensor.mosaic_flow_state
        state: "deep_work"
      - condition: state
        entity_id: input_boolean.break_reminders_enabled
        state: "on"
    action:
      # Gentle break reminder
      - service: light.turn_on
        target:
          entity_id: light.office_accent
        data:
          color_name: "blue"
          brightness: 150
          transition: 5
      
      - delay: "00:00:05"
      
      - service: light.turn_off
        target:
          entity_id: light.office_accent
        data:
          transition: 5
      
      # Audio reminder
      - service: tts.speak
        target:
          entity_id: tts.google_en
        data:
          entity_id: media_player.office_speakers
          message: "You've been in deep work for {{ states('sensor.mosaic_session_duration') }} minutes. Consider taking a short break to maintain optimal performance."
      
      # Dashboard notification
      - service: notify.mosaic_dashboard
        data:
          title: "Break Reminder"
          message: "Time for a short break to maintain peak performance"
          data:
            priority: "low"
            lifecycle: "FLOW_LC"
            actions:
              - action: "take_break"
                title: "Take Break"
              - action: "extend_session"
                title: "Continue (+30min)"
              - action: "snooze_reminder"
                title: "Remind in 15min"

  - alias: "MOSAIC: Biometric Alert"
    description: "Alert when biometric data indicates stress or fatigue"
    trigger:
      - platform: numeric_state
        entity_id: sensor.apple_watch_heart_rate
        above: 100
        for:
          minutes: 5
      - platform: numeric_state
        entity_id: sensor.mosaic_stress_level
        above: 80
        for:
          minutes: 3
    condition:
      - condition: state
        entity_id: sensor.mosaic_flow_state
        state: "deep_work"
      - condition: state
        entity_id: input_boolean.biometric_monitoring_enabled
        state: "on"
    action:
      # Immediate environment adjustment
      - service: climate.set_temperature
        target:
          entity_id: climate.office_ac
        data:
          temperature: 21  # Cooler temperature
      
      - service: fan.turn_on
        target:
          entity_id: fan.office_air_purifier
        data:
          speed: "high"
      
      # Lighting adjustment for stress relief
      - service: light.turn_on
        target:
          entity_id: light.office_main
        data:
          brightness: 120
          color_temp: 2700  # Warmer light
          transition: 10
      
      # Gentle audio transition
      - service: media_player.play_media
        target:
          entity_id: media_player.office_speakers
        data:
          media_content_id: "spotify:playlist:37i9dQZF1DWZqd5JICZI1i"  # Calm playlist
          media_content_type: "music"
      
      # Alert notification
      - service: notify.mosaic_dashboard
        data:
          title: "Biometric Alert"
          message: "Elevated stress detected. Environment adjusted for relaxation. Consider taking a break."
          data:
            priority: "high"
            lifecycle: "FLOW_LC"
            actions:
              - action: "force_break"
                title: "Take Break Now"
              - action: "breathing_exercise"
                title: "Breathing Exercise"
              - action: "dismiss_alert"
                title: "Continue Working"
