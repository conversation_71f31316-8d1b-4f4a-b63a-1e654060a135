/**
 * MOSAIC Home Assistant React Components
 * Main export file for the component library
 */

// Core Components
export { MosaicCard, QuickAction, StatusIndicator } from './components/core/MosaicCard';

// Widget Components
export { FlowStateMonitor } from './components/widgets/FlowStateMonitor';
export { AgentStatus } from './components/widgets/AgentStatus';
export { EnvironmentControl } from './components/widgets/EnvironmentControl';
export { BiometricTracker } from './components/widgets/BiometricTracker';
export { TaskManager } from './components/widgets/TaskManager';
export { CalendarIntegration } from './components/widgets/CalendarIntegration';
export { QuickActionsPanel } from './components/widgets/QuickActionsPanel';
export { NotificationCenter } from './components/widgets/NotificationCenter';

// Dashboard Layouts
export { DeveloperWorkspace } from './components/layouts/DeveloperWorkspace';
export { FamilyDashboard } from './components/layouts/FamilyDashboard';
export { WellnessDashboard } from './components/layouts/WellnessDashboard';

// Types
export type {
  // Core Types
  LifecycleId,
  Priority,
  MosaicEntity,
  MosaicComponentProps,
  
  // Dashboard Types
  DashboardLayout,
  DashboardConfig,
  WidgetConfig,
  WidgetType,
  
  // Flow State Types
  FlowStateData,
  FlowStateConfig,
  
  // Agent Types
  AgentStatus as AgentStatusType,
  
  // Automation Types
  WorkspaceAutomation,
  AutomationTrigger,
  AutomationCondition,
  AutomationAction,
  AutomationRule,
  
  // Biometric Types
  BiometricData,
  BiometricThresholds,
  
  // Environment Types
  EnvironmentState,
  EnvironmentPreset,
  
  // Component Props
  DashboardCardProps,
  CardAction,
  
  // Event Types
  MosaicEvent,
  HomeAssistantEvent,
  
  // Notification Types
  MosaicNotification,
  NotificationAction,
  
  // Theme Types
  MosaicTheme,
  
  // API Types
  ApiResponse,
  PaginatedResponse,
} from './types';

// Utilities
export { cn } from './utils/cn';

// Version
export const VERSION = '1.0.0';
