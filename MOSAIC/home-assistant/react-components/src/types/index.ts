/**
 * MOSAIC Home Assistant React Component Types
 * Comprehensive type definitions for Home Assistant integration
 */

import { HassEntity } from '@hakit/core';

// Core MOSAIC Types
export type LifecycleId = 
  | 'APEX_LC'
  | 'PRISM_LC' 
  | 'AURORA_LC'
  | 'NEXUS_LC'
  | 'FLUX_LC'
  | 'SPARK_LC'
  | 'SHIELD_LC'
  | 'QUANTUM_LC'
  | 'ECHO_LC'
  | 'PULSE_LC'
  | 'FLOW_LC';

export type Priority = 'low' | 'medium' | 'high' | 'critical';

// Home Assistant Entity Types
export interface MosaicEntity extends HassEntity {
  mosaic_lifecycle?: LifecycleId;
  mosaic_priority?: Priority;
  mosaic_metadata?: Record<string, any>;
}

// Dashboard Layout Types
export type DashboardLayout = 'developer' | 'family' | 'wellness';

export interface DashboardConfig {
  layout: DashboardLayout;
  user_id: string;
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    density: 'compact' | 'comfortable' | 'spacious';
    animations: boolean;
    notifications: boolean;
  };
  widgets: WidgetConfig[];
  automation_rules: AutomationRule[];
}

export interface WidgetConfig {
  id: string;
  type: WidgetType;
  position: { x: number; y: number; w: number; h: number };
  config: Record<string, any>;
  lifecycle?: LifecycleId;
  visible: boolean;
  permissions: string[];
}

export type WidgetType = 
  | 'flow_state_monitor'
  | 'agent_status'
  | 'environment_control'
  | 'biometric_tracker'
  | 'task_manager'
  | 'calendar_integration'
  | 'weather_display'
  | 'security_panel'
  | 'energy_monitor'
  | 'media_control'
  | 'notification_center'
  | 'quick_actions';

// Flow State Monitoring
export interface FlowStateData {
  current_state: 'deep_work' | 'collaboration' | 'break' | 'transition' | 'offline';
  focus_score: number; // 0-100
  interruption_count: number;
  session_duration: number; // minutes
  productivity_metrics: {
    tasks_completed: number;
    code_commits: number;
    meetings_attended: number;
    documents_created: number;
  };
  environmental_factors: {
    noise_level: number;
    lighting_level: number;
    temperature: number;
    air_quality: number;
  };
  biometric_data?: {
    heart_rate: number;
    stress_level: number;
    activity_level: number;
  };
}

export interface FlowStateConfig {
  deep_work_threshold: number;
  break_reminder_interval: number;
  environment_optimization: boolean;
  biometric_integration: boolean;
  notification_settings: {
    interruption_blocking: boolean;
    focus_mode_alerts: boolean;
    break_reminders: boolean;
  };
}

// Agent Status Types
export interface AgentStatus {
  agent_id: string;
  name: string;
  lifecycle: LifecycleId;
  status: 'active' | 'idle' | 'busy' | 'error' | 'offline';
  current_task?: string;
  performance_metrics: {
    tasks_completed: number;
    success_rate: number;
    average_response_time: number;
    uptime_percentage: number;
  };
  resource_usage: {
    cpu_percent: number;
    memory_percent: number;
    network_usage: number;
  };
  last_activity: Date;
  health_score: number; // 0-100
}

// Smart Workspace Automation
export interface WorkspaceAutomation {
  id: string;
  name: string;
  description: string;
  trigger: AutomationTrigger;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  enabled: boolean;
  lifecycle?: LifecycleId;
  priority: Priority;
}

export interface AutomationTrigger {
  type: 'time' | 'state' | 'event' | 'webhook' | 'calendar' | 'biometric';
  config: Record<string, any>;
}

export interface AutomationCondition {
  type: 'state' | 'time' | 'template' | 'zone' | 'device';
  entity_id?: string;
  condition: string;
  value?: any;
}

export interface AutomationAction {
  type: 'service' | 'scene' | 'script' | 'notification' | 'mosaic_event';
  target?: string;
  data?: Record<string, any>;
  lifecycle_event?: {
    lifecycle: LifecycleId;
    event_type: string;
    payload: Record<string, any>;
  };
}

export interface AutomationRule {
  id: string;
  name: string;
  automation: WorkspaceAutomation;
  schedule?: {
    enabled: boolean;
    cron_expression?: string;
    time_range?: { start: string; end: string };
    days_of_week?: number[];
  };
  user_preferences: {
    notification_level: 'none' | 'minimal' | 'normal' | 'verbose';
    auto_execute: boolean;
    confirmation_required: boolean;
  };
}

// Biometric Integration
export interface BiometricData {
  user_id: string;
  timestamp: Date;
  heart_rate?: number;
  blood_pressure?: { systolic: number; diastolic: number };
  stress_level?: number; // 0-100
  activity_level?: number; // 0-100
  sleep_quality?: number; // 0-100
  mood_score?: number; // 0-100
  energy_level?: number; // 0-100
  focus_score?: number; // 0-100
  source: 'apple_watch' | 'fitbit' | 'manual' | 'estimated';
}

export interface BiometricThresholds {
  heart_rate: { min: number; max: number; optimal: number };
  stress_level: { low: number; moderate: number; high: number };
  activity_level: { sedentary: number; light: number; moderate: number; vigorous: number };
  sleep_quality: { poor: number; fair: number; good: number; excellent: number };
}

// Environment Control
export interface EnvironmentState {
  lighting: {
    brightness: number; // 0-100
    color_temperature: number; // Kelvin
    rgb_color?: [number, number, number];
    scene?: string;
  };
  climate: {
    temperature: number;
    humidity: number;
    air_quality: number; // 0-100
    ventilation_level: number; // 0-100
  };
  audio: {
    volume: number; // 0-100
    source?: string;
    ambient_noise_level: number;
    noise_cancellation: boolean;
  };
  security: {
    doors_locked: boolean;
    windows_status: 'open' | 'closed' | 'partial';
    motion_detected: boolean;
    presence_detected: boolean;
  };
}

export interface EnvironmentPreset {
  id: string;
  name: string;
  description: string;
  icon: string;
  settings: Partial<EnvironmentState>;
  triggers: {
    flow_state?: 'deep_work' | 'collaboration' | 'break';
    time_of_day?: string;
    calendar_event?: string;
    biometric_threshold?: string;
  };
  lifecycle?: LifecycleId;
}

// Component Props Types
export interface MosaicComponentProps {
  className?: string;
  lifecycle?: LifecycleId;
  priority?: Priority;
  onError?: (error: Error) => void;
  onUpdate?: (data: any) => void;
}

export interface DashboardCardProps extends MosaicComponentProps {
  title: string;
  subtitle?: string;
  icon?: string;
  actions?: CardAction[];
  loading?: boolean;
  error?: string;
  children: React.ReactNode;
}

export interface CardAction {
  id: string;
  label: string;
  icon?: string;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'danger';
}

// Event Types
export interface MosaicEvent {
  id: string;
  type: string;
  source: LifecycleId;
  target?: LifecycleId;
  payload: Record<string, any>;
  metadata: Record<string, any>;
  timestamp: Date;
}

export interface HomeAssistantEvent {
  event_type: string;
  data: Record<string, any>;
  origin: string;
  time_fired: Date;
  context: {
    id: string;
    parent_id?: string;
    user_id?: string;
  };
}

// Notification Types
export interface MosaicNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  priority: Priority;
  lifecycle?: LifecycleId;
  actions?: NotificationAction[];
  timestamp: Date;
  expires_at?: Date;
  persistent?: boolean;
}

export interface NotificationAction {
  id: string;
  label: string;
  action: 'dismiss' | 'snooze' | 'execute' | 'navigate';
  data?: Record<string, any>;
}

// Theme and Styling
export interface MosaicTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    muted: string;
    border: string;
    error: string;
    warning: string;
    success: string;
    info: string;
  };
  typography: {
    font_family: string;
    font_sizes: Record<string, string>;
    font_weights: Record<string, number>;
  };
  spacing: Record<string, string>;
  border_radius: Record<string, string>;
  shadows: Record<string, string>;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    has_next: boolean;
    has_prev: boolean;
  };
}
