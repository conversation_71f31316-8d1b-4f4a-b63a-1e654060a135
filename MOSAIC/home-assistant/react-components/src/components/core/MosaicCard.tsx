/**
 * MOSAIC Dashboard Card Component
 * Core reusable card component for all MOSAIC dashboard widgets
 */

import React from 'react';
import { motion } from 'framer-motion';
import { MoreVertical, AlertTriangle, Loader2 } from 'lucide-react';
import { DashboardCardProps, LifecycleId, Priority } from '../../types';
import { cn } from '../../utils/cn';

// Lifecycle color mapping
const lifecycleColors: Record<LifecycleId, string> = {
  APEX_LC: 'border-blue-500 bg-blue-50 dark:bg-blue-950',
  PRISM_LC: 'border-purple-500 bg-purple-50 dark:bg-purple-950',
  AURORA_LC: 'border-green-500 bg-green-50 dark:bg-green-950',
  NEXUS_LC: 'border-orange-500 bg-orange-50 dark:bg-orange-950',
  FLUX_LC: 'border-cyan-500 bg-cyan-50 dark:bg-cyan-950',
  SPARK_LC: 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950',
  SHIELD_LC: 'border-red-500 bg-red-50 dark:bg-red-950',
  QUANTUM_LC: 'border-indigo-500 bg-indigo-50 dark:bg-indigo-950',
  ECHO_LC: 'border-pink-500 bg-pink-50 dark:bg-pink-950',
  PULSE_LC: 'border-emerald-500 bg-emerald-50 dark:bg-emerald-950',
  FLOW_LC: 'border-violet-500 bg-violet-50 dark:bg-violet-950',
};

// Priority styling
const priorityStyles: Record<Priority, string> = {
  low: 'border-l-gray-400',
  medium: 'border-l-yellow-400',
  high: 'border-l-orange-400',
  critical: 'border-l-red-500',
};

export const MosaicCard: React.FC<DashboardCardProps> = ({
  title,
  subtitle,
  icon,
  actions = [],
  loading = false,
  error,
  children,
  className,
  lifecycle,
  priority = 'medium',
  onError,
  onUpdate,
}) => {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [showActions, setShowActions] = React.useState(false);

  // Get lifecycle-specific styling
  const lifecycleStyle = lifecycle ? lifecycleColors[lifecycle] : '';
  const priorityStyle = priorityStyles[priority];

  // Handle error display
  React.useEffect(() => {
    if (error && onError) {
      onError(new Error(error));
    }
  }, [error, onError]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'relative rounded-lg border-2 border-l-4 shadow-sm transition-all duration-200',
        'hover:shadow-md hover:scale-[1.02]',
        'bg-white dark:bg-gray-900',
        'border-gray-200 dark:border-gray-700',
        lifecycleStyle,
        priorityStyle,
        className
      )}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 pb-2">
        <div className="flex items-center space-x-3">
          {icon && (
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                <span className="text-lg">{icon}</span>
              </div>
            </div>
          )}
          <div className="min-w-0 flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
              {title}
            </h3>
            {subtitle && (
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                {subtitle}
              </p>
            )}
          </div>
        </div>

        {/* Actions Menu */}
        <div className="relative">
          {(actions.length > 0 || loading) && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: showActions || loading ? 1 : 0 }}
              transition={{ duration: 0.2 }}
              className="flex items-center space-x-2"
            >
              {loading && (
                <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
              )}
              
              {actions.length > 0 && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <MoreVertical className="w-4 h-4 text-gray-400" />
                </button>
              )}
            </motion.div>
          )}

          {/* Actions Dropdown */}
          {isExpanded && actions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.15 }}
              className="absolute right-0 top-8 z-50 w-48 rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg"
            >
              <div className="py-1">
                {actions.map((action) => (
                  <button
                    key={action.id}
                    onClick={() => {
                      action.onClick();
                      setIsExpanded(false);
                    }}
                    disabled={action.disabled}
                    className={cn(
                      'w-full px-4 py-2 text-left text-sm transition-colors',
                      'hover:bg-gray-50 dark:hover:bg-gray-700',
                      'disabled:opacity-50 disabled:cursor-not-allowed',
                      action.variant === 'danger' && 'text-red-600 dark:text-red-400',
                      action.variant === 'primary' && 'text-blue-600 dark:text-blue-400'
                    )}
                  >
                    <div className="flex items-center space-x-2">
                      {action.icon && <span className="text-base">{action.icon}</span>}
                      <span>{action.label}</span>
                    </div>
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Lifecycle Badge */}
      {lifecycle && (
        <div className="absolute top-2 right-2">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
            {lifecycle.replace('_LC', '')}
          </span>
        </div>
      )}

      {/* Content */}
      <div className="px-4 pb-4">
        {error ? (
          <div className="flex items-center space-x-2 p-3 rounded-md bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800">
            <AlertTriangle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
          </div>
        ) : (
          <div className={cn(loading && 'opacity-50 pointer-events-none')}>
            {children}
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 rounded-lg flex items-center justify-center">
          <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span className="text-sm font-medium">Loading...</span>
          </div>
        </div>
      )}

      {/* Priority Indicator */}
      {priority === 'critical' && (
        <div className="absolute -top-1 -right-1">
          <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
        </div>
      )}
    </motion.div>
  );
};

// Quick Action Button Component
export interface QuickActionProps {
  icon: string;
  label: string;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'default' | 'primary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
}

export const QuickAction: React.FC<QuickActionProps> = ({
  icon,
  label,
  onClick,
  disabled = false,
  variant = 'default',
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base',
  };

  const variantClasses = {
    default: 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300',
    primary: 'bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300',
    danger: 'bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300',
  };

  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'rounded-full flex items-center justify-center transition-all duration-200',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        sizeClasses[size],
        variantClasses[variant]
      )}
      title={label}
    >
      <span className="text-lg">{icon}</span>
    </motion.button>
  );
};

// Status Indicator Component
export interface StatusIndicatorProps {
  status: 'online' | 'offline' | 'busy' | 'idle' | 'error';
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  size = 'md',
  showLabel = false,
}) => {
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    busy: 'bg-red-500',
    idle: 'bg-yellow-500',
    error: 'bg-red-600',
  };

  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  return (
    <div className="flex items-center space-x-2">
      <div
        className={cn(
          'rounded-full',
          statusColors[status],
          sizeClasses[size],
          (status === 'online' || status === 'busy') && 'animate-pulse'
        )}
      />
      {showLabel && (label || status) && (
        <span className="text-xs text-gray-600 dark:text-gray-400 capitalize">
          {label || status}
        </span>
      )}
    </div>
  );
};
