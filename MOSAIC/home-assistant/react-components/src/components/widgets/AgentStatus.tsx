/**
 * Agent Status Widget
 * Displays status and performance metrics for MOSAIC lifecycle agents
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>t, 
  Cpu, 
  MemoryStick, 
  Network, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Activity,
  Zap,
  TrendingUp
} from 'lucide-react';
import { MosaicCard, StatusIndicator } from '../core/MosaicCard';
import { AgentStatus as AgentStatusType, LifecycleId, MosaicComponentProps } from '../../types';
import { cn } from '../../utils/cn';

interface AgentStatusProps extends MosaicComponentProps {
  agents: AgentStatusType[];
  selectedAgent?: string;
  onAgentSelect?: (agentId: string) => void;
  onAgentAction?: (agentId: string, action: string) => void;
}

export const AgentStatus: React.FC<AgentStatusProps> = ({
  agents,
  selectedAgent,
  onAgentSelect,
  onAgentAction,
  className,
  lifecycle = 'PULSE_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid');
  const [filterStatus, setFilterStatus] = React.useState<'all' | 'active' | 'error'>('all');

  // Filter agents based on status
  const filteredAgents = agents.filter(agent => {
    if (filterStatus === 'all') return true;
    if (filterStatus === 'active') return agent.status === 'active' || agent.status === 'busy';
    if (filterStatus === 'error') return agent.status === 'error';
    return true;
  });

  // Get overall system health
  const systemHealth = {
    total: agents.length,
    active: agents.filter(a => a.status === 'active' || a.status === 'busy').length,
    error: agents.filter(a => a.status === 'error').length,
    avgHealth: agents.reduce((sum, a) => sum + a.health_score, 0) / agents.length || 0,
  };

  // Format uptime
  const formatUptime = (percentage: number) => {
    return `${percentage.toFixed(1)}%`;
  };

  // Format response time
  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  // Get status color
  const getStatusColor = (status: AgentStatusType['status']) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100 dark:bg-green-900';
      case 'busy': return 'text-blue-600 bg-blue-100 dark:bg-blue-900';
      case 'idle': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
      case 'error': return 'text-red-600 bg-red-100 dark:bg-red-900';
      case 'offline': return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
    }
  };

  // Get health score color
  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  // Card actions
  const cardActions = [
    {
      id: 'refresh',
      label: 'Refresh',
      icon: '🔄',
      onClick: () => onUpdate?.({}),
    },
    {
      id: 'view_logs',
      label: 'View Logs',
      icon: '📋',
      onClick: () => {
        // Open logs view
        console.log('Opening agent logs...');
      },
    },
    {
      id: 'restart_all',
      label: 'Restart All',
      icon: '⚡',
      onClick: () => {
        agents.forEach(agent => {
          if (agent.status === 'error') {
            onAgentAction?.(agent.agent_id, 'restart');
          }
        });
      },
      variant: 'danger' as const,
    },
  ];

  return (
    <MosaicCard
      title="Agent Status"
      subtitle={`${systemHealth.active}/${systemHealth.total} active`}
      icon="🤖"
      actions={cardActions}
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      onError={onError}
      onUpdate={onUpdate}
    >
      <div className="space-y-4">
        {/* System Overview */}
        <div className="grid grid-cols-4 gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {systemHealth.total}
            </div>
            <div className="text-xs text-gray-500">Total</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">
              {systemHealth.active}
            </div>
            <div className="text-xs text-gray-500">Active</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-red-600">
              {systemHealth.error}
            </div>
            <div className="text-xs text-gray-500">Errors</div>
          </div>
          <div className="text-center">
            <div className={cn(
              'text-lg font-bold',
              getHealthColor(systemHealth.avgHealth)
            )}>
              {systemHealth.avgHealth.toFixed(0)}%
            </div>
            <div className="text-xs text-gray-500">Health</div>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-1">
            {(['all', 'active', 'error'] as const).map((filter) => (
              <button
                key={filter}
                onClick={() => setFilterStatus(filter)}
                className={cn(
                  'px-3 py-1 text-xs font-medium rounded-md transition-colors',
                  filterStatus === filter
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700'
                )}
              >
                {filter.charAt(0).toUpperCase() + filter.slice(1)}
              </button>
            ))}
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                'p-1 rounded text-xs',
                viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700'
              )}
            >
              ⊞
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={cn(
                'p-1 rounded text-xs',
                viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700'
              )}
            >
              ☰
            </button>
          </div>
        </div>

        {/* Agents List/Grid */}
        <div className={cn(
          viewMode === 'grid' 
            ? 'grid grid-cols-1 gap-2' 
            : 'space-y-2'
        )}>
          {filteredAgents.map((agent) => (
            <motion.div
              key={agent.agent_id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                'p-3 border rounded-md cursor-pointer transition-all duration-200',
                'hover:shadow-sm hover:border-blue-300',
                selectedAgent === agent.agent_id
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
              )}
              onClick={() => onAgentSelect?.(agent.agent_id)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  <span className="font-medium text-sm text-gray-900 dark:text-white">
                    {agent.name}
                  </span>
                  <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                    {agent.lifecycle.replace('_LC', '')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <StatusIndicator status={agent.status} />
                  <span className={cn(
                    'text-xs font-medium',
                    getHealthColor(agent.health_score)
                  )}>
                    {agent.health_score}%
                  </span>
                </div>
              </div>

              {agent.current_task && (
                <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 truncate">
                  📋 {agent.current_task}
                </div>
              )}

              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    {agent.performance_metrics.tasks_completed}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    {formatResponseTime(agent.performance_metrics.average_response_time)}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="w-3 h-3 text-purple-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    {formatUptime(agent.performance_metrics.uptime_percentage)}
                  </span>
                </div>
              </div>

              {viewMode === 'list' && (
                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="flex items-center space-x-1">
                      <Cpu className="w-3 h-3 text-orange-500" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {agent.resource_usage.cpu_percent.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MemoryStick className="w-3 h-3 text-red-500" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {agent.resource_usage.memory_percent.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Network className="w-3 h-3 text-cyan-500" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {(agent.resource_usage.network_usage / 1024).toFixed(1)}KB/s
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Agent Actions */}
              {selectedAgent === agent.agent_id && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700"
                >
                  <div className="flex space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onAgentAction?.(agent.agent_id, 'restart');
                      }}
                      className="flex-1 px-2 py-1 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded text-xs font-medium transition-colors"
                    >
                      🔄 Restart
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onAgentAction?.(agent.agent_id, 'logs');
                      }}
                      className="flex-1 px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs font-medium transition-colors"
                    >
                      📋 Logs
                    </button>
                    {agent.status === 'error' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onAgentAction?.(agent.agent_id, 'debug');
                        }}
                        className="flex-1 px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 rounded text-xs font-medium transition-colors"
                      >
                        🐛 Debug
                      </button>
                    )}
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>

        {filteredAgents.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Bot className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No agents found</p>
            <p className="text-xs">Try adjusting the filter</p>
          </div>
        )}
      </div>
    </MosaicCard>
  );
};
