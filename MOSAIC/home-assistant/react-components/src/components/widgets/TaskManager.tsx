/**
 * Task Manager Integration Widget
 * MOSAIC task management with Home Assistant automation triggers
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckSquare, 
  Square, 
  Clock, 
  Calendar, 
  User, 
  Flag,
  Plus,
  Filter,
  MoreHorizontal,
  Play,
  Pause,
  AlertCircle
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  MosaicComponentProps,
  LifecycleId 
} from '../../types';
import { cn } from '../../utils/cn';

interface TaskManagerProps extends MosaicComponentProps {
  tasks: MosaicTask[];
  onTaskUpdate?: (taskId: string, updates: Partial<MosaicTask>) => void;
  onTaskCreate?: (task: Omit<MosaicTask, 'id'>) => void;
  onTaskDelete?: (taskId: string) => void;
  onAutomationTrigger?: (taskId: string, trigger: string) => void;
}

interface MosaicTask {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'completed' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'critical';
  lifecycle: LifecycleId;
  assignee?: string;
  due_date?: string;
  estimated_hours?: number;
  actual_hours?: number;
  tags: string[];
  automation_triggers?: string[];
  created_at: string;
  updated_at: string;
}

export const TaskManager: React.FC<TaskManagerProps> = ({
  tasks,
  onTaskUpdate,
  onTaskCreate,
  onTaskDelete,
  onAutomationTrigger,
  className,
  lifecycle = 'FLOW_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [filter, setFilter] = React.useState<'all' | 'todo' | 'in_progress' | 'completed'>('all');
  const [selectedTask, setSelectedTask] = React.useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = React.useState(false);

  // Mock tasks data
  const mockTasks: MosaicTask[] = tasks || [
    {
      id: '1',
      title: 'Implement AURORA-LC customer feedback system',
      description: 'Build automated feedback collection and analysis pipeline',
      status: 'in_progress',
      priority: 'high',
      lifecycle: 'AURORA_LC',
      assignee: 'Dan',
      due_date: '2024-01-15',
      estimated_hours: 8,
      actual_hours: 5.5,
      tags: ['development', 'automation', 'customer-success'],
      automation_triggers: ['deep_work_mode', 'focus_session'],
      created_at: '2024-01-10T09:00:00Z',
      updated_at: '2024-01-12T14:30:00Z',
    },
    {
      id: '2',
      title: 'Design PRISM-LC knowledge graph schema',
      description: 'Create semantic relationships for knowledge management',
      status: 'todo',
      priority: 'medium',
      lifecycle: 'PRISM_LC',
      assignee: 'Sarah',
      due_date: '2024-01-20',
      estimated_hours: 6,
      tags: ['design', 'knowledge-management', 'schema'],
      automation_triggers: ['morning_routine'],
      created_at: '2024-01-11T10:15:00Z',
      updated_at: '2024-01-11T10:15:00Z',
    },
    {
      id: '3',
      title: 'Optimize APEX-LC build pipeline',
      description: 'Reduce deployment time from 8 minutes to under 3 minutes',
      status: 'completed',
      priority: 'high',
      lifecycle: 'APEX_LC',
      assignee: 'Alex',
      due_date: '2024-01-12',
      estimated_hours: 4,
      actual_hours: 3.5,
      tags: ['optimization', 'devops', 'performance'],
      created_at: '2024-01-08T08:00:00Z',
      updated_at: '2024-01-12T16:45:00Z',
    },
    {
      id: '4',
      title: 'Setup SHIELD-LC security monitoring',
      description: 'Implement real-time threat detection and alerting',
      status: 'blocked',
      priority: 'critical',
      lifecycle: 'SHIELD_LC',
      assignee: 'Mike',
      due_date: '2024-01-18',
      estimated_hours: 12,
      actual_hours: 2,
      tags: ['security', 'monitoring', 'alerts'],
      automation_triggers: ['security_alert', 'threat_detected'],
      created_at: '2024-01-09T11:30:00Z',
      updated_at: '2024-01-13T09:20:00Z',
    },
  ];

  // Filter tasks
  const filteredTasks = mockTasks.filter(task => {
    if (filter === 'all') return true;
    return task.status === filter;
  });

  // Get status icon
  const getStatusIcon = (status: MosaicTask['status']) => {
    switch (status) {
      case 'completed': return <CheckSquare className="w-4 h-4 text-green-600" />;
      case 'in_progress': return <Play className="w-4 h-4 text-blue-600" />;
      case 'blocked': return <AlertCircle className="w-4 h-4 text-red-600" />;
      default: return <Square className="w-4 h-4 text-gray-400" />;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: MosaicTask['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
      default: return 'text-green-600 bg-green-100 dark:bg-green-900';
    }
  };

  // Get lifecycle color
  const getLifecycleColor = (lifecycle: LifecycleId) => {
    const colors = {
      APEX_LC: 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300',
      PRISM_LC: 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300',
      AURORA_LC: 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300',
      NEXUS_LC: 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300',
      FLUX_LC: 'bg-pink-100 text-pink-700 dark:bg-pink-900 dark:text-pink-300',
      SPARK_LC: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300',
      SHIELD_LC: 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300',
      QUANTUM_LC: 'bg-cyan-100 text-cyan-700 dark:bg-cyan-900 dark:text-cyan-300',
      ECHO_LC: 'bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300',
      PULSE_LC: 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300',
      FLOW_LC: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300',
    };
    return colors[lifecycle] || 'bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300';
  };

  // Handle task status change
  const handleStatusChange = (taskId: string, newStatus: MosaicTask['status']) => {
    onTaskUpdate?.(taskId, { status: newStatus, updated_at: new Date().toISOString() });
  };

  // Calculate task statistics
  const taskStats = {
    total: mockTasks.length,
    todo: mockTasks.filter(t => t.status === 'todo').length,
    in_progress: mockTasks.filter(t => t.status === 'in_progress').length,
    completed: mockTasks.filter(t => t.status === 'completed').length,
    blocked: mockTasks.filter(t => t.status === 'blocked').length,
  };

  return (
    <MosaicCard
      title="Task Manager"
      subtitle={`${taskStats.total} tasks • ${taskStats.in_progress} active`}
      icon="✅"
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      actions={[
        { 
          id: 'create_task', 
          label: 'New Task', 
          icon: '➕', 
          onClick: () => setShowCreateForm(true) 
        },
        { 
          id: 'automation_rules', 
          label: 'Automation', 
          icon: '🤖', 
          onClick: () => {} 
        },
      ]}
    >
      <div className="space-y-4">
        {/* Task Statistics */}
        <div className="grid grid-cols-4 gap-2">
          <div className="text-center p-2 bg-blue-50 dark:bg-blue-950 rounded">
            <div className="text-lg font-bold text-blue-600">{taskStats.todo}</div>
            <div className="text-xs text-blue-500">To Do</div>
          </div>
          <div className="text-center p-2 bg-yellow-50 dark:bg-yellow-950 rounded">
            <div className="text-lg font-bold text-yellow-600">{taskStats.in_progress}</div>
            <div className="text-xs text-yellow-500">In Progress</div>
          </div>
          <div className="text-center p-2 bg-green-50 dark:bg-green-950 rounded">
            <div className="text-lg font-bold text-green-600">{taskStats.completed}</div>
            <div className="text-xs text-green-500">Completed</div>
          </div>
          <div className="text-center p-2 bg-red-50 dark:bg-red-950 rounded">
            <div className="text-lg font-bold text-red-600">{taskStats.blocked}</div>
            <div className="text-xs text-red-500">Blocked</div>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {(['all', 'todo', 'in_progress', 'completed'] as const).map((filterOption) => (
            <button
              key={filterOption}
              onClick={() => setFilter(filterOption)}
              className={cn(
                'flex-1 py-1 px-2 text-xs font-medium rounded-md transition-colors capitalize',
                filter === filterOption
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              )}
            >
              {filterOption.replace('_', ' ')}
            </button>
          ))}
        </div>

        {/* Task List */}
        <div className="space-y-2 max-h-64 overflow-y-auto">
          <AnimatePresence>
            {filteredTasks.map((task) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className={cn(
                  'p-3 border rounded-lg transition-all duration-200 cursor-pointer',
                  selectedTask === task.id
                    ? 'border-blue-300 bg-blue-50 dark:bg-blue-950 dark:border-blue-700'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                )}
                onClick={() => setSelectedTask(selectedTask === task.id ? null : task.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        const newStatus = task.status === 'completed' ? 'todo' : 'completed';
                        handleStatusChange(task.id, newStatus);
                      }}
                      className="mt-0.5"
                    >
                      {getStatusIcon(task.status)}
                    </button>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className={cn(
                          'text-sm font-medium truncate',
                          task.status === 'completed' 
                            ? 'line-through text-gray-500' 
                            : 'text-gray-900 dark:text-white'
                        )}>
                          {task.title}
                        </h4>
                        <span className={cn(
                          'px-1.5 py-0.5 text-xs font-medium rounded-full',
                          getPriorityColor(task.priority)
                        )}>
                          {task.priority}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        <span className={cn(
                          'px-2 py-0.5 rounded-full text-xs font-medium',
                          getLifecycleColor(task.lifecycle)
                        )}>
                          {task.lifecycle.replace('_LC', '')}
                        </span>
                        
                        {task.assignee && (
                          <span className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{task.assignee}</span>
                          </span>
                        )}
                        
                        {task.due_date && (
                          <span className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{new Date(task.due_date).toLocaleDateString()}</span>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                    <MoreHorizontal className="w-4 h-4 text-gray-400" />
                  </button>
                </div>

                {/* Expanded Task Details */}
                <AnimatePresence>
                  {selectedTask === task.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
                    >
                      {task.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {task.description}
                        </p>
                      )}
                      
                      <div className="grid grid-cols-2 gap-3 text-xs">
                        <div>
                          <span className="font-medium text-gray-700 dark:text-gray-300">Estimated:</span>
                          <span className="ml-1 text-gray-500">{task.estimated_hours}h</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700 dark:text-gray-300">Actual:</span>
                          <span className="ml-1 text-gray-500">{task.actual_hours || 0}h</span>
                        </div>
                      </div>
                      
                      {task.tags.length > 0 && (
                        <div className="mt-2">
                          <div className="flex flex-wrap gap-1">
                            {task.tags.map((tag) => (
                              <span
                                key={tag}
                                className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded"
                              >
                                #{tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {task.automation_triggers && task.automation_triggers.length > 0 && (
                        <div className="mt-2">
                          <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Automation Triggers:
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {task.automation_triggers.map((trigger) => (
                              <button
                                key={trigger}
                                onClick={() => onAutomationTrigger?.(task.id, trigger)}
                                className="px-2 py-0.5 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 text-xs rounded transition-colors"
                              >
                                🤖 {trigger.replace('_', ' ')}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {filteredTasks.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Square className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No tasks found</p>
            <p className="text-xs">Try adjusting your filter or create a new task</p>
          </div>
        )}
      </div>
    </MosaicCard>
  );
};
