/**
 * Quick Actions Panel Widget
 * Rapid access to common automation and lifecycle operations
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Zap, 
  Play, 
  Pause, 
  Square, 
  Home, 
  Shield, 
  Coffee,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Lightbulb,
  Thermometer,
  Lock,
  Unlock,
  Wifi,
  WifiOff,
  Battery,
  Smartphone,
  Monitor,
  Settings
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  MosaicComponentProps,
  LifecycleId 
} from '../../types';
import { cn } from '../../utils/cn';

interface QuickActionsPanelProps extends MosaicComponentProps {
  actions: QuickAction[];
  onActionExecute?: (actionId: string, params?: any) => void;
  onActionToggle?: (actionId: string, enabled: boolean) => void;
  systemStatus?: SystemStatus;
}

interface QuickAction {
  id: string;
  label: string;
  description?: string;
  icon: string;
  category: 'environment' | 'security' | 'productivity' | 'lifecycle' | 'system';
  type: 'button' | 'toggle' | 'slider';
  enabled?: boolean;
  value?: number;
  lifecycle?: LifecycleId;
  shortcut?: string;
  automation_trigger?: string;
}

interface SystemStatus {
  deep_work_mode: boolean;
  security_armed: boolean;
  lights_on: boolean;
  audio_muted: boolean;
  climate_auto: boolean;
  wifi_connected: boolean;
  battery_level: number;
}

export const QuickActionsPanel: React.FC<QuickActionsPanelProps> = ({
  actions,
  onActionExecute,
  onActionToggle,
  systemStatus,
  className,
  lifecycle = 'NEXUS_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [selectedCategory, setSelectedCategory] = React.useState<string>('all');
  const [expandedAction, setExpandedAction] = React.useState<string | null>(null);

  // Mock system status
  const mockSystemStatus: SystemStatus = systemStatus || {
    deep_work_mode: false,
    security_armed: true,
    lights_on: true,
    audio_muted: false,
    climate_auto: true,
    wifi_connected: true,
    battery_level: 85,
  };

  // Mock quick actions
  const mockActions: QuickAction[] = actions || [
    // Environment Actions
    {
      id: 'deep_work_mode',
      label: 'Deep Work Mode',
      description: 'Optimize environment for focused work',
      icon: '🎯',
      category: 'environment',
      type: 'toggle',
      enabled: mockSystemStatus.deep_work_mode,
      lifecycle: 'FLOW_LC',
      shortcut: '⌘+D',
      automation_trigger: 'deep_work_session',
    },
    {
      id: 'meeting_mode',
      label: 'Meeting Mode',
      description: 'Perfect lighting and audio for video calls',
      icon: '📹',
      category: 'environment',
      type: 'button',
      lifecycle: 'NEXUS_LC',
      shortcut: '⌘+M',
    },
    {
      id: 'break_mode',
      label: 'Break Mode',
      description: 'Relaxing environment for breaks',
      icon: '☕',
      category: 'environment',
      type: 'button',
      lifecycle: 'FLOW_LC',
    },
    {
      id: 'lights_toggle',
      label: 'Lights',
      description: 'Toggle main lighting',
      icon: '💡',
      category: 'environment',
      type: 'toggle',
      enabled: mockSystemStatus.lights_on,
      shortcut: '⌘+L',
    },
    
    // Security Actions
    {
      id: 'security_arm',
      label: 'Security System',
      description: 'Arm/disarm security system',
      icon: '🛡️',
      category: 'security',
      type: 'toggle',
      enabled: mockSystemStatus.security_armed,
      lifecycle: 'SHIELD_LC',
    },
    {
      id: 'lock_doors',
      label: 'Lock All Doors',
      description: 'Secure all entry points',
      icon: '🔒',
      category: 'security',
      type: 'button',
      lifecycle: 'SHIELD_LC',
    },
    
    // Productivity Actions
    {
      id: 'start_pomodoro',
      label: 'Start Pomodoro',
      description: '25-minute focused work session',
      icon: '⏱️',
      category: 'productivity',
      type: 'button',
      lifecycle: 'FLOW_LC',
      shortcut: '⌘+P',
    },
    {
      id: 'quick_note',
      label: 'Quick Note',
      description: 'Capture thoughts instantly',
      icon: '📝',
      category: 'productivity',
      type: 'button',
      lifecycle: 'PRISM_LC',
      shortcut: '⌘+N',
    },
    
    // Lifecycle Actions
    {
      id: 'apex_deploy',
      label: 'Deploy APEX',
      description: 'Deploy latest APEX-LC changes',
      icon: '🚀',
      category: 'lifecycle',
      type: 'button',
      lifecycle: 'APEX_LC',
    },
    {
      id: 'prism_sync',
      label: 'Sync Knowledge',
      description: 'Update PRISM-LC knowledge base',
      icon: '🧠',
      category: 'lifecycle',
      type: 'button',
      lifecycle: 'PRISM_LC',
    },
    
    // System Actions
    {
      id: 'audio_toggle',
      label: 'Audio',
      description: 'Mute/unmute system audio',
      icon: '🔊',
      category: 'system',
      type: 'toggle',
      enabled: !mockSystemStatus.audio_muted,
      shortcut: '⌘+⇧+M',
    },
    {
      id: 'wifi_toggle',
      label: 'WiFi',
      description: 'WiFi connection status',
      icon: '📶',
      category: 'system',
      type: 'toggle',
      enabled: mockSystemStatus.wifi_connected,
    },
  ];

  // Filter actions by category
  const filteredActions = selectedCategory === 'all' 
    ? mockActions 
    : mockActions.filter(action => action.category === selectedCategory);

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'environment': return Home;
      case 'security': return Shield;
      case 'productivity': return Zap;
      case 'lifecycle': return Settings;
      case 'system': return Monitor;
      default: return Zap;
    }
  };

  // Get action icon component
  const getActionIcon = (iconStr: string) => {
    const iconMap: { [key: string]: any } = {
      '🎯': Zap,
      '📹': Monitor,
      '☕': Coffee,
      '💡': Lightbulb,
      '🛡️': Shield,
      '🔒': Lock,
      '⏱️': Play,
      '📝': Settings,
      '🚀': Play,
      '🧠': Settings,
      '🔊': Volume2,
      '📶': Wifi,
    };
    return iconMap[iconStr] || Zap;
  };

  // Handle action execution
  const handleActionExecute = (action: QuickAction) => {
    if (action.type === 'toggle') {
      onActionToggle?.(action.id, !action.enabled);
    } else {
      onActionExecute?.(action.id);
    }
  };

  // Categories for filtering
  const categories = [
    { id: 'all', label: 'All', icon: Zap },
    { id: 'environment', label: 'Environment', icon: Home },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'productivity', label: 'Productivity', icon: Zap },
    { id: 'lifecycle', label: 'Lifecycle', icon: Settings },
    { id: 'system', label: 'System', icon: Monitor },
  ];

  return (
    <MosaicCard
      title="Quick Actions"
      subtitle={`${filteredActions.length} actions available`}
      icon="⚡"
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      actions={[
        { 
          id: 'customize', 
          label: 'Customize', 
          icon: '⚙️', 
          onClick: () => {} 
        },
        { 
          id: 'shortcuts', 
          label: 'Shortcuts', 
          icon: '⌨️', 
          onClick: () => {} 
        },
      ]}
    >
      <div className="space-y-4">
        {/* System Status Overview */}
        <div className="grid grid-cols-3 gap-2">
          <div className={cn(
            'flex items-center justify-center space-x-1 p-2 rounded text-xs',
            mockSystemStatus.deep_work_mode 
              ? 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300'
              : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
          )}>
            <Zap className="w-3 h-3" />
            <span>Focus</span>
          </div>
          <div className={cn(
            'flex items-center justify-center space-x-1 p-2 rounded text-xs',
            mockSystemStatus.security_armed 
              ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
              : 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
          )}>
            <Shield className="w-3 h-3" />
            <span>{mockSystemStatus.security_armed ? 'Armed' : 'Safe'}</span>
          </div>
          <div className={cn(
            'flex items-center justify-center space-x-1 p-2 rounded text-xs',
            mockSystemStatus.battery_level > 20 
              ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
              : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
          )}>
            <Battery className="w-3 h-3" />
            <span>{mockSystemStatus.battery_level}%</span>
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex space-x-1 overflow-x-auto pb-1">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                'flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-medium whitespace-nowrap transition-colors',
                selectedCategory === category.id
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
              )}
            >
              <category.icon className="w-3 h-3" />
              <span>{category.label}</span>
            </button>
          ))}
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-2 gap-3">
          {filteredActions.map((action) => {
            const IconComponent = getActionIcon(action.icon);
            
            return (
              <motion.button
                key={action.id}
                onClick={() => handleActionExecute(action)}
                onDoubleClick={() => setExpandedAction(
                  expandedAction === action.id ? null : action.id
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  'p-3 rounded-lg border transition-all duration-200 text-left',
                  action.type === 'toggle' && action.enabled
                    ? 'border-blue-300 bg-blue-50 dark:bg-blue-950 dark:border-blue-700'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
                )}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className={cn(
                    'p-1.5 rounded',
                    action.type === 'toggle' && action.enabled
                      ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  )}>
                    <IconComponent className="w-4 h-4" />
                  </div>
                  
                  {action.shortcut && (
                    <span className="text-xs text-gray-400 font-mono">
                      {action.shortcut}
                    </span>
                  )}
                </div>
                
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    {action.label}
                  </div>
                  <div className="text-xs text-gray-500 line-clamp-2">
                    {action.description}
                  </div>
                </div>
                
                {action.lifecycle && (
                  <div className="mt-2">
                    <span className="inline-block px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                      {action.lifecycle.replace('_LC', '')}
                    </span>
                  </div>
                )}
              </motion.button>
            );
          })}
        </div>

        {filteredActions.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Zap className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No actions in this category</p>
            <p className="text-xs">Try selecting a different category</p>
          </div>
        )}

        {/* Expanded Action Details */}
        {expandedAction && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border"
          >
            {(() => {
              const action = mockActions.find(a => a.id === expandedAction);
              if (!action) return null;
              
              return (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {action.label}
                    </h4>
                    <button
                      onClick={() => setExpandedAction(null)}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      ×
                    </button>
                  </div>
                  
                  {action.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {action.description}
                    </p>
                  )}
                  
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Type:</span>
                      <span className="ml-1 text-gray-500 capitalize">{action.type}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Category:</span>
                      <span className="ml-1 text-gray-500 capitalize">{action.category}</span>
                    </div>
                  </div>
                  
                  {action.automation_trigger && (
                    <div className="text-xs">
                      <span className="font-medium text-gray-700 dark:text-gray-300">Automation:</span>
                      <span className="ml-1 text-gray-500">{action.automation_trigger}</span>
                    </div>
                  )}
                </div>
              );
            })()}
          </motion.div>
        )}
      </div>
    </MosaicCard>
  );
};
