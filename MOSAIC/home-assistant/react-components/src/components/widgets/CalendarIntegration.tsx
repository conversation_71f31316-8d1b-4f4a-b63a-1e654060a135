/**
 * Calendar Integration Widget
 * Smart scheduling with automated environment adjustments
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Video,
  Phone,
  Coffee,
  Lightbulb,
  Thermometer,
  ChevronLeft,
  ChevronRight,
  Plus,
  Settings
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  MosaicComponentProps,
  LifecycleId 
} from '../../types';
import { cn } from '../../utils/cn';

interface CalendarIntegrationProps extends MosaicComponentProps {
  events: CalendarEvent[];
  onEventUpdate?: (eventId: string, updates: Partial<CalendarEvent>) => void;
  onEnvironmentPrep?: (eventId: string, prepType: string) => void;
  onEventCreate?: (event: Omit<CalendarEvent, 'id'>) => void;
}

interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  attendees?: string[];
  event_type: 'meeting' | 'focus_time' | 'break' | 'personal' | 'travel';
  lifecycle?: LifecycleId;
  automation_rules?: {
    lighting?: { brightness: number; temperature: number };
    climate?: { temperature: number };
    audio?: { volume: number; source: string };
    notifications?: boolean;
  };
  meeting_link?: string;
  preparation_time?: number; // minutes before event
  status: 'confirmed' | 'tentative' | 'cancelled';
}

export const CalendarIntegration: React.FC<CalendarIntegrationProps> = ({
  events,
  onEventUpdate,
  onEnvironmentPrep,
  onEventCreate,
  className,
  lifecycle = 'FLOW_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const [viewMode, setViewMode] = React.useState<'day' | 'week' | 'agenda'>('day');
  const [selectedEvent, setSelectedEvent] = React.useState<string | null>(null);

  // Mock calendar events
  const mockEvents: CalendarEvent[] = events || [
    {
      id: '1',
      title: 'APEX-LC Sprint Planning',
      description: 'Plan next sprint for development lifecycle',
      start_time: '2024-01-15T09:00:00Z',
      end_time: '2024-01-15T10:30:00Z',
      location: 'Conference Room A',
      attendees: ['Dan', 'Sarah', 'Alex'],
      event_type: 'meeting',
      lifecycle: 'APEX_LC',
      automation_rules: {
        lighting: { brightness: 90, temperature: 5000 },
        climate: { temperature: 22 },
        audio: { volume: 40, source: 'none' },
        notifications: false,
      },
      meeting_link: 'https://meet.google.com/abc-defg-hij',
      preparation_time: 15,
      status: 'confirmed',
    },
    {
      id: '2',
      title: 'Deep Work: PRISM-LC Implementation',
      description: 'Focus session for knowledge management system',
      start_time: '2024-01-15T14:00:00Z',
      end_time: '2024-01-15T16:00:00Z',
      event_type: 'focus_time',
      lifecycle: 'PRISM_LC',
      automation_rules: {
        lighting: { brightness: 80, temperature: 4500 },
        climate: { temperature: 21 },
        audio: { volume: 25, source: 'ambient' },
        notifications: false,
      },
      preparation_time: 10,
      status: 'confirmed',
    },
    {
      id: '3',
      title: 'Coffee Break & Wellness Check',
      description: 'Mindful break with biometric review',
      start_time: '2024-01-15T16:15:00Z',
      end_time: '2024-01-15T16:30:00Z',
      event_type: 'break',
      lifecycle: 'FLOW_LC',
      automation_rules: {
        lighting: { brightness: 60, temperature: 3000 },
        climate: { temperature: 24 },
        audio: { volume: 30, source: 'relaxation' },
        notifications: true,
      },
      status: 'confirmed',
    },
    {
      id: '4',
      title: 'AURORA-LC Customer Review',
      description: 'Weekly customer success metrics review',
      start_time: '2024-01-16T11:00:00Z',
      end_time: '2024-01-16T12:00:00Z',
      attendees: ['Dan', 'Customer Success Team'],
      event_type: 'meeting',
      lifecycle: 'AURORA_LC',
      meeting_link: 'https://zoom.us/j/123456789',
      preparation_time: 20,
      status: 'tentative',
    },
  ];

  // Get events for selected date
  const getEventsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return mockEvents.filter(event => 
      event.start_time.startsWith(dateStr)
    ).sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime());
  };

  // Get event type icon and color
  const getEventTypeInfo = (type: CalendarEvent['event_type']) => {
    switch (type) {
      case 'meeting':
        return { icon: Users, color: 'text-blue-600 bg-blue-100 dark:bg-blue-900' };
      case 'focus_time':
        return { icon: Lightbulb, color: 'text-purple-600 bg-purple-100 dark:bg-purple-900' };
      case 'break':
        return { icon: Coffee, color: 'text-green-600 bg-green-100 dark:bg-green-900' };
      case 'personal':
        return { icon: Clock, color: 'text-gray-600 bg-gray-100 dark:bg-gray-900' };
      case 'travel':
        return { icon: MapPin, color: 'text-orange-600 bg-orange-100 dark:bg-orange-900' };
      default:
        return { icon: Calendar, color: 'text-gray-600 bg-gray-100 dark:bg-gray-900' };
    }
  };

  // Get lifecycle color
  const getLifecycleColor = (lifecycle?: LifecycleId) => {
    if (!lifecycle) return 'border-gray-200 dark:border-gray-700';
    
    const colors = {
      APEX_LC: 'border-l-blue-500',
      PRISM_LC: 'border-l-purple-500',
      AURORA_LC: 'border-l-green-500',
      NEXUS_LC: 'border-l-indigo-500',
      FLUX_LC: 'border-l-pink-500',
      SPARK_LC: 'border-l-yellow-500',
      SHIELD_LC: 'border-l-red-500',
      QUANTUM_LC: 'border-l-cyan-500',
      ECHO_LC: 'border-l-teal-500',
      PULSE_LC: 'border-l-orange-500',
      FLOW_LC: 'border-l-emerald-500',
    };
    return colors[lifecycle] || 'border-l-gray-500';
  };

  // Format time
  const formatTime = (dateStr: string) => {
    return new Date(dateStr).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  // Get current and upcoming events
  const todayEvents = getEventsForDate(new Date());
  const upcomingEvents = todayEvents.filter(event => 
    new Date(event.start_time) > new Date()
  ).slice(0, 3);

  // Get next event
  const nextEvent = upcomingEvents[0];

  return (
    <MosaicCard
      title="Calendar Integration"
      subtitle={`${todayEvents.length} events today • ${upcomingEvents.length} upcoming`}
      icon="📅"
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      actions={[
        { 
          id: 'create_event', 
          label: 'New Event', 
          icon: '➕', 
          onClick: () => onEventCreate?.({
            title: 'New Event',
            start_time: new Date().toISOString(),
            end_time: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
            event_type: 'meeting',
            status: 'tentative',
          })
        },
        { 
          id: 'automation_settings', 
          label: 'Automation', 
          icon: '⚙️', 
          onClick: () => {} 
        },
      ]}
    >
      <div className="space-y-4">
        {/* View Mode Selector */}
        <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {(['day', 'week', 'agenda'] as const).map((mode) => (
            <button
              key={mode}
              onClick={() => setViewMode(mode)}
              className={cn(
                'flex-1 py-1 px-2 text-xs font-medium rounded-md transition-colors capitalize',
                viewMode === mode
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              )}
            >
              {mode}
            </button>
          ))}
        </div>

        {/* Next Event Highlight */}
        {nextEvent && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 border border-blue-200 dark:border-blue-800 rounded-lg"
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                Next Event
              </span>
              <span className="text-xs text-blue-600 dark:text-blue-400">
                in {Math.ceil((new Date(nextEvent.start_time).getTime() - Date.now()) / (1000 * 60))} min
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {(() => {
                const { icon: Icon, color } = getEventTypeInfo(nextEvent.event_type);
                return (
                  <div className={cn('p-1 rounded', color)}>
                    <Icon className="w-3 h-3" />
                  </div>
                );
              })()}
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {nextEvent.title}
                </div>
                <div className="text-xs text-gray-500">
                  {formatTime(nextEvent.start_time)} - {formatTime(nextEvent.end_time)}
                </div>
              </div>
              {nextEvent.automation_rules && (
                <button
                  onClick={() => onEnvironmentPrep?.(nextEvent.id, 'prepare')}
                  className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                >
                  Prep Environment
                </button>
              )}
            </div>
          </motion.div>
        )}

        {/* Today's Events */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              Today's Schedule
            </h4>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setSelectedDate(new Date(selectedDate.getTime() - 24 * 60 * 60 * 1000))}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
              >
                <ChevronLeft className="w-4 h-4 text-gray-400" />
              </button>
              <span className="text-xs text-gray-500 px-2">
                {selectedDate.toLocaleDateString('en-US', { 
                  month: 'short', 
                  day: 'numeric' 
                })}
              </span>
              <button
                onClick={() => setSelectedDate(new Date(selectedDate.getTime() + 24 * 60 * 60 * 1000))}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
              >
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </button>
            </div>
          </div>

          <div className="space-y-2 max-h-64 overflow-y-auto">
            <AnimatePresence>
              {getEventsForDate(selectedDate).map((event) => {
                const { icon: Icon, color } = getEventTypeInfo(event.event_type);
                const isUpcoming = new Date(event.start_time) > new Date();
                const isActive = new Date(event.start_time) <= new Date() && new Date(event.end_time) > new Date();

                return (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className={cn(
                      'p-3 border-l-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-all duration-200',
                      getLifecycleColor(event.lifecycle),
                      selectedEvent === event.id && 'ring-2 ring-blue-300 dark:ring-blue-700',
                      isActive && 'bg-blue-50 dark:bg-blue-950',
                      event.status === 'cancelled' && 'opacity-50'
                    )}
                    onClick={() => setSelectedEvent(selectedEvent === event.id ? null : event.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className={cn('p-1 rounded mt-0.5', color)}>
                          <Icon className="w-3 h-3" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className={cn(
                              'text-sm font-medium truncate',
                              event.status === 'cancelled' 
                                ? 'line-through text-gray-500' 
                                : 'text-gray-900 dark:text-white'
                            )}>
                              {event.title}
                            </h4>
                            {isActive && (
                              <span className="px-1.5 py-0.5 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 text-xs font-medium rounded-full">
                                Live
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            <span className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{formatTime(event.start_time)} - {formatTime(event.end_time)}</span>
                            </span>
                            
                            {event.location && (
                              <span className="flex items-center space-x-1">
                                <MapPin className="w-3 h-3" />
                                <span className="truncate">{event.location}</span>
                              </span>
                            )}
                            
                            {event.attendees && event.attendees.length > 0 && (
                              <span className="flex items-center space-x-1">
                                <Users className="w-3 h-3" />
                                <span>{event.attendees.length}</span>
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        {event.meeting_link && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(event.meeting_link, '_blank');
                            }}
                            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                          >
                            <Video className="w-3 h-3 text-blue-500" />
                          </button>
                        )}
                        
                        {event.automation_rules && isUpcoming && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onEnvironmentPrep?.(event.id, 'prepare');
                            }}
                            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                          >
                            <Settings className="w-3 h-3 text-gray-400" />
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Expanded Event Details */}
                    <AnimatePresence>
                      {selectedEvent === event.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
                        >
                          {event.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                              {event.description}
                            </p>
                          )}
                          
                          {event.automation_rules && (
                            <div className="space-y-2">
                              <div className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                Environment Automation:
                              </div>
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                {event.automation_rules.lighting && (
                                  <div className="flex items-center space-x-1">
                                    <Lightbulb className="w-3 h-3 text-yellow-500" />
                                    <span>{event.automation_rules.lighting.brightness}% • {event.automation_rules.lighting.temperature}K</span>
                                  </div>
                                )}
                                {event.automation_rules.climate && (
                                  <div className="flex items-center space-x-1">
                                    <Thermometer className="w-3 h-3 text-blue-500" />
                                    <span>{event.automation_rules.climate.temperature}°C</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>

          {getEventsForDate(selectedDate).length === 0 && (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No events scheduled</p>
              <p className="text-xs">Your day is free for deep work</p>
            </div>
          )}
        </div>
      </div>
    </MosaicCard>
  );
};
