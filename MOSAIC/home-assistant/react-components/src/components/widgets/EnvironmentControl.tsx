/**
 * Environment Control Panel Widget
 * Smart workspace automation controls for lighting, climate, audio, and security
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Lightbulb, 
  Thermometer, 
  Volume2, 
  Shield, 
  Settings,
  Power,
  Sun,
  Moon,
  Wind,
  Lock,
  Unlock,
  VolumeX,
  Plus,
  Minus
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  MosaicComponentProps,
  EnvironmentState,
  EnvironmentPreset 
} from '../../types';
import { cn } from '../../utils/cn';

interface EnvironmentControlProps extends MosaicComponentProps {
  environmentState: EnvironmentState;
  presets: EnvironmentPreset[];
  onEnvironmentChange?: (changes: Partial<EnvironmentState>) => void;
  onPresetActivate?: (presetId: string) => void;
  onPresetCreate?: (preset: Omit<EnvironmentPreset, 'id'>) => void;
}

export const EnvironmentControl: React.FC<EnvironmentControlProps> = ({
  environmentState,
  presets,
  onEnvironmentChange,
  onPresetActivate,
  onPresetCreate,
  className,
  lifecycle = 'NEXUS_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [activeTab, setActiveTab] = React.useState<'lighting' | 'climate' | 'audio' | 'security'>('lighting');
  const [showPresets, setShowPresets] = React.useState(false);

  // Mock environment data
  const mockEnvironment: EnvironmentState = environmentState || {
    lighting: {
      main_brightness: 75,
      accent_brightness: 50,
      color_temperature: 4000,
      rgb_color: '#ffffff',
      adaptive_lighting: true,
      circadian_rhythm: true,
    },
    climate: {
      temperature: 22,
      target_temperature: 23,
      humidity: 45,
      air_quality: 'good',
      fan_speed: 'medium',
      hvac_mode: 'cool',
    },
    audio: {
      volume: 30,
      muted: false,
      current_source: 'spotify',
      ambient_sounds: true,
      noise_cancellation: false,
    },
    security: {
      armed: true,
      doors_locked: true,
      windows_secured: true,
      cameras_active: true,
      motion_detection: true,
    },
  };

  const mockPresets: EnvironmentPreset[] = presets || [
    {
      id: 'deep_work',
      name: 'Deep Work',
      icon: '🎯',
      description: 'Optimized for focused work sessions',
      settings: {
        lighting: { main_brightness: 80, color_temperature: 4500 },
        climate: { target_temperature: 21 },
        audio: { volume: 25, ambient_sounds: true },
        security: { armed: true },
      },
    },
    {
      id: 'meeting',
      name: 'Meeting Mode',
      icon: '📹',
      description: 'Perfect lighting and audio for video calls',
      settings: {
        lighting: { main_brightness: 90, color_temperature: 5000 },
        climate: { target_temperature: 22 },
        audio: { volume: 40, noise_cancellation: true },
        security: { armed: true },
      },
    },
    {
      id: 'relaxation',
      name: 'Relaxation',
      icon: '🧘',
      description: 'Calm environment for breaks and meditation',
      settings: {
        lighting: { main_brightness: 40, color_temperature: 2700 },
        climate: { target_temperature: 24 },
        audio: { volume: 20, ambient_sounds: true },
        security: { armed: false },
      },
    },
    {
      id: 'away',
      name: 'Away Mode',
      icon: '🚗',
      description: 'Energy saving and security when away',
      settings: {
        lighting: { main_brightness: 0 },
        climate: { target_temperature: 18 },
        audio: { volume: 0, muted: true },
        security: { armed: true },
      },
    },
  ];

  // Tab configuration
  const tabs = [
    { id: 'lighting', label: 'Lighting', icon: Lightbulb },
    { id: 'climate', label: 'Climate', icon: Thermometer },
    { id: 'audio', label: 'Audio', icon: Volume2 },
    { id: 'security', label: 'Security', icon: Shield },
  ] as const;

  // Handle value changes
  const handleChange = (category: keyof EnvironmentState, key: string, value: any) => {
    const changes = {
      [category]: {
        ...mockEnvironment[category],
        [key]: value,
      },
    };
    onEnvironmentChange?.(changes);
  };

  // Render lighting controls
  const renderLightingControls = () => (
    <div className="space-y-4">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Main Brightness</span>
          <span className="text-sm text-gray-500">{mockEnvironment.lighting.main_brightness}%</span>
        </div>
        <input
          type="range"
          min="0"
          max="100"
          value={mockEnvironment.lighting.main_brightness}
          onChange={(e) => handleChange('lighting', 'main_brightness', parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
        />
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Color Temperature</span>
          <span className="text-sm text-gray-500">{mockEnvironment.lighting.color_temperature}K</span>
        </div>
        <input
          type="range"
          min="2000"
          max="6500"
          value={mockEnvironment.lighting.color_temperature}
          onChange={(e) => handleChange('lighting', 'color_temperature', parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
        />
        <div className="flex justify-between text-xs text-gray-500">
          <span>Warm</span>
          <span>Cool</span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={() => handleChange('lighting', 'adaptive_lighting', !mockEnvironment.lighting.adaptive_lighting)}
          className={cn(
            'flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors',
            mockEnvironment.lighting.adaptive_lighting
              ? 'bg-blue-100 border-blue-300 text-blue-700 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300'
              : 'bg-gray-100 border-gray-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300'
          )}
        >
          <Sun className="w-4 h-4" />
          <span className="text-sm">Adaptive</span>
        </button>
        <button
          onClick={() => handleChange('lighting', 'circadian_rhythm', !mockEnvironment.lighting.circadian_rhythm)}
          className={cn(
            'flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors',
            mockEnvironment.lighting.circadian_rhythm
              ? 'bg-purple-100 border-purple-300 text-purple-700 dark:bg-purple-900 dark:border-purple-700 dark:text-purple-300'
              : 'bg-gray-100 border-gray-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300'
          )}
        >
          <Moon className="w-4 h-4" />
          <span className="text-sm">Circadian</span>
        </button>
      </div>
    </div>
  );

  // Render climate controls
  const renderClimateControls = () => (
    <div className="space-y-4">
      <div className="text-center">
        <div className="text-3xl font-bold text-blue-600 mb-2">
          {mockEnvironment.climate.temperature}°C
        </div>
        <div className="text-sm text-gray-500">Current Temperature</div>
      </div>

      <div className="flex items-center justify-center space-x-4">
        <button
          onClick={() => handleChange('climate', 'target_temperature', mockEnvironment.climate.target_temperature - 1)}
          className="p-2 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 rounded-lg transition-colors"
        >
          <Minus className="w-4 h-4" />
        </button>
        <div className="text-center">
          <div className="text-xl font-bold">{mockEnvironment.climate.target_temperature}°C</div>
          <div className="text-xs text-gray-500">Target</div>
        </div>
        <button
          onClick={() => handleChange('climate', 'target_temperature', mockEnvironment.climate.target_temperature + 1)}
          className="p-2 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
        </button>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-lg font-bold text-gray-900 dark:text-white">{mockEnvironment.climate.humidity}%</div>
          <div className="text-xs text-gray-500">Humidity</div>
        </div>
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-lg font-bold text-green-600 capitalize">{mockEnvironment.climate.air_quality}</div>
          <div className="text-xs text-gray-500">Air Quality</div>
        </div>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Fan Speed</label>
        <select
          value={mockEnvironment.climate.fan_speed}
          onChange={(e) => handleChange('climate', 'fan_speed', e.target.value)}
          className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        >
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
          <option value="auto">Auto</option>
        </select>
      </div>
    </div>
  );

  // Render audio controls
  const renderAudioControls = () => (
    <div className="space-y-4">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Volume</span>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleChange('audio', 'muted', !mockEnvironment.audio.muted)}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              {mockEnvironment.audio.muted ? (
                <VolumeX className="w-4 h-4 text-red-500" />
              ) : (
                <Volume2 className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              )}
            </button>
            <span className="text-sm text-gray-500">{mockEnvironment.audio.volume}%</span>
          </div>
        </div>
        <input
          type="range"
          min="0"
          max="100"
          value={mockEnvironment.audio.volume}
          onChange={(e) => handleChange('audio', 'volume', parseInt(e.target.value))}
          disabled={mockEnvironment.audio.muted}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 disabled:opacity-50"
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Audio Source</label>
        <select
          value={mockEnvironment.audio.current_source}
          onChange={(e) => handleChange('audio', 'current_source', e.target.value)}
          className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        >
          <option value="spotify">Spotify</option>
          <option value="apple_music">Apple Music</option>
          <option value="youtube">YouTube</option>
          <option value="ambient">Ambient Sounds</option>
          <option value="none">None</option>
        </select>
      </div>

      <div className="grid grid-cols-1 gap-3">
        <button
          onClick={() => handleChange('audio', 'ambient_sounds', !mockEnvironment.audio.ambient_sounds)}
          className={cn(
            'flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors',
            mockEnvironment.audio.ambient_sounds
              ? 'bg-green-100 border-green-300 text-green-700 dark:bg-green-900 dark:border-green-700 dark:text-green-300'
              : 'bg-gray-100 border-gray-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300'
          )}
        >
          <Wind className="w-4 h-4" />
          <span className="text-sm">Ambient Sounds</span>
        </button>
        <button
          onClick={() => handleChange('audio', 'noise_cancellation', !mockEnvironment.audio.noise_cancellation)}
          className={cn(
            'flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors',
            mockEnvironment.audio.noise_cancellation
              ? 'bg-blue-100 border-blue-300 text-blue-700 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300'
              : 'bg-gray-100 border-gray-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300'
          )}
        >
          <VolumeX className="w-4 h-4" />
          <span className="text-sm">Noise Cancellation</span>
        </button>
      </div>
    </div>
  );

  // Render security controls
  const renderSecurityControls = () => (
    <div className="space-y-4">
      <div className="text-center">
        <div className={cn(
          'inline-flex items-center space-x-2 px-4 py-2 rounded-lg font-medium',
          mockEnvironment.security.armed
            ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
            : 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
        )}>
          <Shield className="w-4 h-4" />
          <span>{mockEnvironment.security.armed ? 'Armed' : 'Disarmed'}</span>
        </div>
      </div>

      <button
        onClick={() => handleChange('security', 'armed', !mockEnvironment.security.armed)}
        className={cn(
          'w-full py-3 px-4 rounded-lg font-medium transition-colors',
          mockEnvironment.security.armed
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-green-600 hover:bg-green-700 text-white'
        )}
      >
        {mockEnvironment.security.armed ? 'Disarm System' : 'Arm System'}
      </button>

      <div className="grid grid-cols-2 gap-3">
        <div className={cn(
          'flex items-center justify-center space-x-2 p-3 rounded-lg border',
          mockEnvironment.security.doors_locked
            ? 'bg-green-100 border-green-300 text-green-700 dark:bg-green-900 dark:border-green-700 dark:text-green-300'
            : 'bg-red-100 border-red-300 text-red-700 dark:bg-red-900 dark:border-red-700 dark:text-red-300'
        )}>
          {mockEnvironment.security.doors_locked ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
          <span className="text-sm">Doors</span>
        </div>
        <div className={cn(
          'flex items-center justify-center space-x-2 p-3 rounded-lg border',
          mockEnvironment.security.cameras_active
            ? 'bg-blue-100 border-blue-300 text-blue-700 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300'
            : 'bg-gray-100 border-gray-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300'
        )}>
          <span className="text-lg">📹</span>
          <span className="text-sm">Cameras</span>
        </div>
      </div>
    </div>
  );

  return (
    <MosaicCard
      title="Environment Control"
      subtitle="Smart Workspace Automation"
      icon="🏠"
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      actions={[
        { 
          id: 'presets', 
          label: 'Presets', 
          icon: '⚙️', 
          onClick: () => setShowPresets(!showPresets) 
        },
        { 
          id: 'settings', 
          label: 'Settings', 
          icon: '🔧', 
          onClick: () => {} 
        },
      ]}
    >
      <div className="space-y-4">
        {/* Environment Presets */}
        {showPresets && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="grid grid-cols-2 gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            {mockPresets.map((preset) => (
              <button
                key={preset.id}
                onClick={() => onPresetActivate?.(preset.id)}
                className="flex flex-col items-center space-y-1 p-2 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-all duration-200 hover:scale-105"
              >
                <span className="text-lg">{preset.icon}</span>
                <span className="text-xs font-medium text-center">{preset.name}</span>
              </button>
            ))}
          </motion.div>
        )}

        {/* Tab Navigation */}
        <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={cn(
                'flex-1 flex items-center justify-center space-x-1 py-2 px-3 rounded-md transition-colors',
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              )}
            >
              <tab.icon className="w-4 h-4" />
              <span className="text-xs font-medium hidden sm:inline">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.2 }}
        >
          {activeTab === 'lighting' && renderLightingControls()}
          {activeTab === 'climate' && renderClimateControls()}
          {activeTab === 'audio' && renderAudioControls()}
          {activeTab === 'security' && renderSecurityControls()}
        </motion.div>
      </div>
    </MosaicCard>
  );
};
