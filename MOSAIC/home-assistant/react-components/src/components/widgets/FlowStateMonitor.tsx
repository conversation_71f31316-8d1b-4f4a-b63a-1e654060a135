/**
 * Flow State Monitor Widget
 * Tracks and displays current flow state, focus metrics, and productivity data
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Target, 
  Clock, 
  TrendingUp, 
  Zap, 
  Eye,
  Heart,
  Activity,
  Thermometer,
  Wind
} from 'lucide-react';
import { MosaicCard, StatusIndicator } from '../core/MosaicCard';
import { FlowStateData, FlowStateConfig, MosaicComponentProps } from '../../types';
import { cn } from '../../utils/cn';

interface FlowStateMonitorProps extends MosaicComponentProps {
  data: FlowStateData;
  config: FlowStateConfig;
  onConfigChange?: (config: Partial<FlowStateConfig>) => void;
  onStateChange?: (state: FlowStateData['current_state']) => void;
}

export const FlowStateMonitor: React.FC<FlowStateMonitorProps> = ({
  data,
  config,
  onConfigChange,
  onStateChange,
  className,
  lifecycle = 'FLOW_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [isConfigOpen, setIsConfigOpen] = React.useState(false);

  // Flow state color mapping
  const stateColors = {
    deep_work: 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300',
    collaboration: 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300',
    break: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300',
    transition: 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300',
    offline: 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300',
  };

  // Calculate focus score color
  const getFocusScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  // Format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  // Card actions
  const cardActions = [
    {
      id: 'configure',
      label: 'Configure',
      icon: '⚙️',
      onClick: () => setIsConfigOpen(true),
    },
    {
      id: 'reset_session',
      label: 'Reset Session',
      icon: '🔄',
      onClick: () => {
        // Reset session logic
        onUpdate?.({
          ...data,
          session_duration: 0,
          interruption_count: 0,
          productivity_metrics: {
            tasks_completed: 0,
            code_commits: 0,
            meetings_attended: 0,
            documents_created: 0,
          },
        });
      },
    },
    {
      id: 'export_data',
      label: 'Export Data',
      icon: '📊',
      onClick: () => {
        // Export data logic
        const dataStr = JSON.stringify(data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `flow-state-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
      },
    },
  ];

  return (
    <MosaicCard
      title="Flow State Monitor"
      subtitle={`Session: ${formatDuration(data.session_duration)}`}
      icon="🧠"
      actions={cardActions}
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      onError={onError}
      onUpdate={onUpdate}
    >
      <div className="space-y-4">
        {/* Current State */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Brain className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Current State
            </span>
          </div>
          <div className={cn(
            'px-3 py-1 rounded-full text-sm font-medium capitalize',
            stateColors[data.current_state]
          )}>
            {data.current_state.replace('_', ' ')}
          </div>
        </div>

        {/* Focus Score */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Target className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Focus Score
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${data.focus_score}%` }}
                transition={{ duration: 0.5 }}
                className={cn(
                  'h-full rounded-full',
                  data.focus_score >= 80 ? 'bg-green-500' :
                  data.focus_score >= 60 ? 'bg-yellow-500' :
                  data.focus_score >= 40 ? 'bg-orange-500' : 'bg-red-500'
                )}
              />
            </div>
            <span className={cn(
              'text-sm font-bold',
              getFocusScoreColor(data.focus_score)
            )}>
              {data.focus_score}%
            </span>
          </div>
        </div>

        {/* Interruptions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Zap className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Interruptions
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className={cn(
              'text-sm font-bold',
              data.interruption_count === 0 ? 'text-green-600' :
              data.interruption_count <= 3 ? 'text-yellow-600' : 'text-red-600'
            )}>
              {data.interruption_count}
            </span>
            <span className="text-xs text-gray-500">today</span>
          </div>
        </div>

        {/* Productivity Metrics */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
            <TrendingUp className="w-4 h-4 mr-2" />
            Productivity Today
          </h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {data.productivity_metrics.tasks_completed}
              </div>
              <div className="text-xs text-gray-500">Tasks</div>
            </div>
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {data.productivity_metrics.code_commits}
              </div>
              <div className="text-xs text-gray-500">Commits</div>
            </div>
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {data.productivity_metrics.meetings_attended}
              </div>
              <div className="text-xs text-gray-500">Meetings</div>
            </div>
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
              <div className="text-lg font-bold text-gray-900 dark:text-white">
                {data.productivity_metrics.documents_created}
              </div>
              <div className="text-xs text-gray-500">Docs</div>
            </div>
          </div>
        </div>

        {/* Environmental Factors */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
            <Eye className="w-4 h-4 mr-2" />
            Environment
          </h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Activity className="w-4 h-4 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">Noise</span>
              </div>
              <span className="font-medium">{data.environmental_factors.noise_level}dB</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Thermometer className="w-4 h-4 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">Temperature</span>
              </div>
              <span className="font-medium">{data.environmental_factors.temperature}°C</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Wind className="w-4 h-4 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">Air Quality</span>
              </div>
              <span className="font-medium">{data.environmental_factors.air_quality}%</span>
            </div>
          </div>
        </div>

        {/* Biometric Data (if available) */}
        {data.biometric_data && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
              <Heart className="w-4 h-4 mr-2" />
              Biometrics
            </h4>
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                <div className="text-sm font-bold text-red-600">
                  {data.biometric_data.heart_rate}
                </div>
                <div className="text-xs text-gray-500">BPM</div>
              </div>
              <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                <div className="text-sm font-bold text-orange-600">
                  {data.biometric_data.stress_level}%
                </div>
                <div className="text-xs text-gray-500">Stress</div>
              </div>
              <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                <div className="text-sm font-bold text-blue-600">
                  {data.biometric_data.activity_level}%
                </div>
                <div className="text-xs text-gray-500">Activity</div>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex justify-between items-center">
            <button
              onClick={() => onStateChange?.('deep_work')}
              className="flex-1 mr-2 px-3 py-2 bg-green-100 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800 text-green-700 dark:text-green-300 rounded-md text-sm font-medium transition-colors"
            >
              🎯 Deep Work
            </button>
            <button
              onClick={() => onStateChange?.('break')}
              className="flex-1 ml-2 px-3 py-2 bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-900 dark:hover:bg-yellow-800 text-yellow-700 dark:text-yellow-300 rounded-md text-sm font-medium transition-colors"
            >
              ☕ Break
            </button>
          </div>
        </div>
      </div>
    </MosaicCard>
  );
};
