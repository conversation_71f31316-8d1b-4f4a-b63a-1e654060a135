/**
 * Notification Center Widget
 * Centralized notification management for MOSAIC system
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  BellOff, 
  AlertCircle, 
  CheckCircle, 
  Info, 
  AlertTriangle,
  X,
  Eye,
  EyeOff,
  Filter,
  Clock,
  User,
  Settings,
  Trash2,
  MoreHorizontal
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  MosaicComponentProps,
  LifecycleId 
} from '../../types';
import { cn } from '../../utils/cn';

interface NotificationCenterProps extends MosaicComponentProps {
  notifications: MosaicNotification[];
  onNotificationAction?: (notificationId: string, action: 'read' | 'dismiss' | 'snooze' | 'delete') => void;
  onNotificationClick?: (notification: MosaicNotification) => void;
  onSettingsOpen?: () => void;
}

interface MosaicNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'system';
  priority: 'low' | 'medium' | 'high' | 'critical';
  lifecycle?: LifecycleId;
  source: string;
  timestamp: string;
  read: boolean;
  dismissed: boolean;
  actions?: NotificationAction[];
  metadata?: {
    agent_id?: string;
    task_id?: string;
    event_id?: string;
    automation_rule?: string;
  };
}

interface NotificationAction {
  id: string;
  label: string;
  type: 'primary' | 'secondary' | 'danger';
  action: string;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  notifications,
  onNotificationAction,
  onNotificationClick,
  onSettingsOpen,
  className,
  lifecycle = 'NEXUS_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [filter, setFilter] = React.useState<'all' | 'unread' | 'high' | 'system'>('all');
  const [selectedNotification, setSelectedNotification] = React.useState<string | null>(null);
  const [showDismissed, setShowDismissed] = React.useState(false);

  // Mock notifications data
  const mockNotifications: MosaicNotification[] = notifications || [
    {
      id: '1',
      title: 'APEX-LC Deployment Complete',
      message: 'Successfully deployed version 2.1.0 to production environment',
      type: 'success',
      priority: 'medium',
      lifecycle: 'APEX_LC',
      source: 'Deployment Pipeline',
      timestamp: '2024-01-15T14:30:00Z',
      read: false,
      dismissed: false,
      actions: [
        { id: 'view_logs', label: 'View Logs', type: 'secondary', action: 'view_deployment_logs' },
        { id: 'rollback', label: 'Rollback', type: 'danger', action: 'rollback_deployment' },
      ],
      metadata: { automation_rule: 'auto_deploy_on_merge' },
    },
    {
      id: '2',
      title: 'AURORA-LC Customer Alert',
      message: 'Customer satisfaction score dropped below threshold (7.2/10)',
      type: 'warning',
      priority: 'high',
      lifecycle: 'AURORA_LC',
      source: 'Customer Success Monitor',
      timestamp: '2024-01-15T13:45:00Z',
      read: false,
      dismissed: false,
      actions: [
        { id: 'review_feedback', label: 'Review Feedback', type: 'primary', action: 'open_customer_feedback' },
        { id: 'schedule_call', label: 'Schedule Call', type: 'secondary', action: 'schedule_customer_call' },
      ],
      metadata: { task_id: 'customer_health_check' },
    },
    {
      id: '3',
      title: 'Deep Work Session Starting',
      message: 'Your 2-hour focus session begins in 5 minutes. Environment is being optimized.',
      type: 'info',
      priority: 'medium',
      lifecycle: 'FLOW_LC',
      source: 'Calendar Integration',
      timestamp: '2024-01-15T13:55:00Z',
      read: true,
      dismissed: false,
      actions: [
        { id: 'start_now', label: 'Start Now', type: 'primary', action: 'start_focus_session' },
        { id: 'postpone', label: 'Postpone 15min', type: 'secondary', action: 'postpone_session' },
      ],
      metadata: { event_id: 'focus_session_001' },
    },
    {
      id: '4',
      title: 'SHIELD-LC Security Alert',
      message: 'Unusual network activity detected from unknown device',
      type: 'error',
      priority: 'critical',
      lifecycle: 'SHIELD_LC',
      source: 'Security Monitor',
      timestamp: '2024-01-15T12:20:00Z',
      read: false,
      dismissed: false,
      actions: [
        { id: 'investigate', label: 'Investigate', type: 'primary', action: 'open_security_dashboard' },
        { id: 'block_device', label: 'Block Device', type: 'danger', action: 'block_suspicious_device' },
      ],
      metadata: { automation_rule: 'threat_detection' },
    },
    {
      id: '5',
      title: 'System Update Available',
      message: 'MOSAIC Core v3.2.1 is available with performance improvements',
      type: 'system',
      priority: 'low',
      source: 'System Updater',
      timestamp: '2024-01-15T10:00:00Z',
      read: true,
      dismissed: false,
      actions: [
        { id: 'update_now', label: 'Update Now', type: 'primary', action: 'start_system_update' },
        { id: 'schedule_update', label: 'Schedule', type: 'secondary', action: 'schedule_system_update' },
      ],
    },
    {
      id: '6',
      title: 'Biometric Alert',
      message: 'Stress level elevated (8.2/10). Consider taking a break.',
      type: 'warning',
      priority: 'medium',
      lifecycle: 'FLOW_LC',
      source: 'Health Monitor',
      timestamp: '2024-01-15T11:30:00Z',
      read: true,
      dismissed: true,
      actions: [
        { id: 'start_break', label: 'Take Break', type: 'primary', action: 'start_wellness_break' },
        { id: 'breathing_exercise', label: 'Breathing Exercise', type: 'secondary', action: 'start_breathing_exercise' },
      ],
    },
  ];

  // Filter notifications
  const filteredNotifications = mockNotifications.filter(notification => {
    if (!showDismissed && notification.dismissed) return false;
    
    switch (filter) {
      case 'unread': return !notification.read;
      case 'high': return notification.priority === 'high' || notification.priority === 'critical';
      case 'system': return notification.type === 'system';
      default: return true;
    }
  }).sort((a, b) => {
    // Sort by priority first, then by timestamp
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const aPriority = priorityOrder[a.priority];
    const bPriority = priorityOrder[b.priority];
    
    if (aPriority !== bPriority) return bPriority - aPriority;
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
  });

  // Get notification type icon and color
  const getNotificationTypeInfo = (type: MosaicNotification['type']) => {
    switch (type) {
      case 'success':
        return { icon: CheckCircle, color: 'text-green-600 bg-green-100 dark:bg-green-900' };
      case 'warning':
        return { icon: AlertTriangle, color: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900' };
      case 'error':
        return { icon: AlertCircle, color: 'text-red-600 bg-red-100 dark:bg-red-900' };
      case 'system':
        return { icon: Settings, color: 'text-blue-600 bg-blue-100 dark:bg-blue-900' };
      default:
        return { icon: Info, color: 'text-blue-600 bg-blue-100 dark:bg-blue-900' };
    }
  };

  // Get priority color
  const getPriorityColor = (priority: MosaicNotification['priority']) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500';
      case 'high': return 'border-l-orange-500';
      case 'medium': return 'border-l-yellow-500';
      default: return 'border-l-green-500';
    }
  };

  // Get lifecycle color
  const getLifecycleColor = (lifecycle?: LifecycleId) => {
    if (!lifecycle) return 'text-gray-500';
    
    const colors = {
      APEX_LC: 'text-blue-600',
      PRISM_LC: 'text-purple-600',
      AURORA_LC: 'text-green-600',
      NEXUS_LC: 'text-indigo-600',
      FLUX_LC: 'text-pink-600',
      SPARK_LC: 'text-yellow-600',
      SHIELD_LC: 'text-red-600',
      QUANTUM_LC: 'text-cyan-600',
      ECHO_LC: 'text-teal-600',
      PULSE_LC: 'text-orange-600',
      FLOW_LC: 'text-emerald-600',
    };
    return colors[lifecycle] || 'text-gray-600';
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  // Handle notification action
  const handleNotificationAction = (notificationId: string, action: string) => {
    onNotificationAction?.(notificationId, action as any);
  };

  // Calculate notification stats
  const notificationStats = {
    total: mockNotifications.filter(n => !n.dismissed).length,
    unread: mockNotifications.filter(n => !n.read && !n.dismissed).length,
    high: mockNotifications.filter(n => (n.priority === 'high' || n.priority === 'critical') && !n.dismissed).length,
    critical: mockNotifications.filter(n => n.priority === 'critical' && !n.dismissed).length,
  };

  return (
    <MosaicCard
      title="Notification Center"
      subtitle={`${notificationStats.unread} unread • ${notificationStats.critical} critical`}
      icon="🔔"
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      actions={[
        { 
          id: 'mark_all_read', 
          label: 'Mark All Read', 
          icon: '✓', 
          onClick: () => mockNotifications.forEach(n => handleNotificationAction(n.id, 'read'))
        },
        { 
          id: 'settings', 
          label: 'Settings', 
          icon: '⚙️', 
          onClick: onSettingsOpen 
        },
      ]}
    >
      <div className="space-y-4">
        {/* Notification Stats */}
        <div className="grid grid-cols-4 gap-2">
          <div className="text-center p-2 bg-blue-50 dark:bg-blue-950 rounded">
            <div className="text-lg font-bold text-blue-600">{notificationStats.total}</div>
            <div className="text-xs text-blue-500">Total</div>
          </div>
          <div className="text-center p-2 bg-yellow-50 dark:bg-yellow-950 rounded">
            <div className="text-lg font-bold text-yellow-600">{notificationStats.unread}</div>
            <div className="text-xs text-yellow-500">Unread</div>
          </div>
          <div className="text-center p-2 bg-orange-50 dark:bg-orange-950 rounded">
            <div className="text-lg font-bold text-orange-600">{notificationStats.high}</div>
            <div className="text-xs text-orange-500">High</div>
          </div>
          <div className="text-center p-2 bg-red-50 dark:bg-red-950 rounded">
            <div className="text-lg font-bold text-red-600">{notificationStats.critical}</div>
            <div className="text-xs text-red-500">Critical</div>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex items-center justify-between">
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {(['all', 'unread', 'high', 'system'] as const).map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={cn(
                  'px-3 py-1 text-xs font-medium rounded-md transition-colors capitalize',
                  filter === filterOption
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                )}
              >
                {filterOption}
              </button>
            ))}
          </div>
          
          <button
            onClick={() => setShowDismissed(!showDismissed)}
            className={cn(
              'flex items-center space-x-1 px-2 py-1 text-xs rounded transition-colors',
              showDismissed
                ? 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
            )}
          >
            {showDismissed ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
            <span>Dismissed</span>
          </button>
        </div>

        {/* Notifications List */}
        <div className="space-y-2 max-h-80 overflow-y-auto">
          <AnimatePresence>
            {filteredNotifications.map((notification) => {
              const { icon: Icon, color } = getNotificationTypeInfo(notification.type);
              
              return (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  className={cn(
                    'p-3 border-l-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-all duration-200',
                    getPriorityColor(notification.priority),
                    selectedNotification === notification.id && 'ring-2 ring-blue-300 dark:ring-blue-700',
                    !notification.read && 'bg-blue-50 dark:bg-blue-950',
                    notification.dismissed && 'opacity-60'
                  )}
                  onClick={() => {
                    setSelectedNotification(selectedNotification === notification.id ? null : notification.id);
                    if (!notification.read) {
                      handleNotificationAction(notification.id, 'read');
                    }
                    onNotificationClick?.(notification);
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className={cn('p-1 rounded mt-0.5', color)}>
                        <Icon className="w-3 h-3" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {notification.title}
                          </h4>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <span className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{formatTimestamp(notification.timestamp)}</span>
                          </span>
                          
                          <span className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{notification.source}</span>
                          </span>
                          
                          {notification.lifecycle && (
                            <span className={cn('font-medium', getLifecycleColor(notification.lifecycle))}>
                              {notification.lifecycle.replace('_LC', '')}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleNotificationAction(notification.id, 'dismiss');
                        }}
                        className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                      >
                        <X className="w-3 h-3 text-gray-400" />
                      </button>
                      
                      <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                        <MoreHorizontal className="w-3 h-3 text-gray-400" />
                      </button>
                    </div>
                  </div>

                  {/* Expanded Notification Actions */}
                  <AnimatePresence>
                    {selectedNotification === notification.id && notification.actions && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
                      >
                        <div className="flex flex-wrap gap-2">
                          {notification.actions.map((action) => (
                            <button
                              key={action.id}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleNotificationAction(notification.id, action.action);
                              }}
                              className={cn(
                                'px-3 py-1 text-xs font-medium rounded transition-colors',
                                action.type === 'primary' && 'bg-blue-600 hover:bg-blue-700 text-white',
                                action.type === 'secondary' && 'bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300',
                                action.type === 'danger' && 'bg-red-600 hover:bg-red-700 text-white'
                              )}
                            >
                              {action.label}
                            </button>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>

        {filteredNotifications.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No notifications</p>
            <p className="text-xs">You're all caught up!</p>
          </div>
        )}
      </div>
    </MosaicCard>
  );
};
