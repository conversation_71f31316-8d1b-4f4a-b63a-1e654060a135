/**
 * Biometric Tracker Widget
 * Integration with Apple Watch and other devices for health monitoring
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Heart, 
  Activity, 
  Zap, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  Watch,
  Smartphone,
  Battery,
  Wifi,
  WifiOff
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  MosaicComponentProps,
  BiometricData,
  BiometricThresholds 
} from '../../types';
import { cn } from '../../utils/cn';

interface BiometricTrackerProps extends MosaicComponentProps {
  biometricData: BiometricData;
  thresholds: BiometricThresholds;
  onThresholdAlert?: (metric: string, value: number, threshold: number) => void;
  onDeviceAction?: (deviceId: string, action: string) => void;
}

interface BiometricDevice {
  id: string;
  name: string;
  type: 'apple_watch' | 'fitness_tracker' | 'smart_scale' | 'blood_pressure';
  connected: boolean;
  battery_level?: number;
  last_sync: string;
  metrics: string[];
}

export const BiometricTracker: React.FC<BiometricTrackerProps> = ({
  biometricData,
  thresholds,
  onThresholdAlert,
  onDeviceAction,
  className,
  lifecycle = 'FLOW_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [selectedMetric, setSelectedMetric] = React.useState<string>('heart_rate');
  const [timeRange, setTimeRange] = React.useState<'1h' | '24h' | '7d'>('24h');

  // Mock biometric data
  const mockBiometricData: BiometricData = biometricData || {
    heart_rate: 72,
    heart_rate_variability: 45,
    stress_level: 35,
    activity_level: 65,
    sleep_quality: 85,
    blood_oxygen: 98,
    body_temperature: 36.8,
    steps: 8420,
    calories_burned: 2150,
    timestamp: new Date().toISOString(),
  };

  const mockThresholds: BiometricThresholds = thresholds || {
    heart_rate: { min: 60, max: 100, critical_max: 150 },
    stress_level: { max: 70, critical_max: 85 },
    blood_oxygen: { min: 95, critical_min: 90 },
    body_temperature: { min: 36.1, max: 37.2, critical_max: 38.0 },
  };

  const connectedDevices: BiometricDevice[] = [
    {
      id: 'apple_watch_series_9',
      name: 'Apple Watch Series 9',
      type: 'apple_watch',
      connected: true,
      battery_level: 78,
      last_sync: '2 minutes ago',
      metrics: ['heart_rate', 'heart_rate_variability', 'activity_level', 'blood_oxygen'],
    },
    {
      id: 'iphone_health',
      name: 'iPhone Health',
      type: 'fitness_tracker',
      connected: true,
      battery_level: 85,
      last_sync: '1 minute ago',
      metrics: ['steps', 'calories_burned', 'sleep_quality'],
    },
    {
      id: 'withings_scale',
      name: 'Withings Body+',
      type: 'smart_scale',
      connected: false,
      last_sync: '2 hours ago',
      metrics: ['body_temperature', 'weight'],
    },
  ];

  // Get metric status based on thresholds
  const getMetricStatus = (metric: string, value: number) => {
    const threshold = mockThresholds[metric as keyof BiometricThresholds];
    if (!threshold) return 'normal';

    if (threshold.critical_min && value < threshold.critical_min) return 'critical';
    if (threshold.critical_max && value > threshold.critical_max) return 'critical';
    if (threshold.min && value < threshold.min) return 'warning';
    if (threshold.max && value > threshold.max) return 'warning';
    return 'normal';
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900';
      case 'warning': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
      case 'good': return 'text-green-600 bg-green-100 dark:bg-green-900';
      default: return 'text-blue-600 bg-blue-100 dark:bg-blue-900';
    }
  };

  // Get device icon
  const getDeviceIcon = (type: BiometricDevice['type']) => {
    switch (type) {
      case 'apple_watch': return <Watch className="w-4 h-4" />;
      case 'fitness_tracker': return <Smartphone className="w-4 h-4" />;
      case 'smart_scale': return <Activity className="w-4 h-4" />;
      case 'blood_pressure': return <Heart className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  // Biometric metrics configuration
  const metrics = [
    {
      id: 'heart_rate',
      label: 'Heart Rate',
      value: mockBiometricData.heart_rate,
      unit: 'BPM',
      icon: Heart,
      color: 'text-red-500',
      status: getMetricStatus('heart_rate', mockBiometricData.heart_rate),
    },
    {
      id: 'stress_level',
      label: 'Stress Level',
      value: mockBiometricData.stress_level,
      unit: '%',
      icon: Zap,
      color: 'text-orange-500',
      status: getMetricStatus('stress_level', mockBiometricData.stress_level),
    },
    {
      id: 'blood_oxygen',
      label: 'Blood Oxygen',
      value: mockBiometricData.blood_oxygen,
      unit: '%',
      icon: Activity,
      color: 'text-blue-500',
      status: getMetricStatus('blood_oxygen', mockBiometricData.blood_oxygen),
    },
    {
      id: 'body_temperature',
      label: 'Temperature',
      value: mockBiometricData.body_temperature,
      unit: '°C',
      icon: TrendingUp,
      color: 'text-purple-500',
      status: getMetricStatus('body_temperature', mockBiometricData.body_temperature),
    },
  ];

  return (
    <MosaicCard
      title="Biometric Tracker"
      subtitle="Health Monitoring"
      icon="❤️"
      lifecycle={lifecycle}
      priority={priority}
      className={className}
      actions={[
        { 
          id: 'sync_devices', 
          label: 'Sync All', 
          icon: '🔄', 
          onClick: () => {} 
        },
        { 
          id: 'health_trends', 
          label: 'Trends', 
          icon: '📊', 
          onClick: () => {} 
        },
      ]}
    >
      <div className="space-y-4">
        {/* Time Range Selector */}
        <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {(['1h', '24h', '7d'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={cn(
                'flex-1 py-1 px-2 text-xs font-medium rounded-md transition-colors',
                timeRange === range
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              )}
            >
              {range}
            </button>
          ))}
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 gap-3">
          {metrics.map((metric) => (
            <motion.button
              key={metric.id}
              onClick={() => setSelectedMetric(metric.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={cn(
                'p-3 rounded-lg border transition-all duration-200',
                selectedMetric === metric.id
                  ? 'border-blue-300 bg-blue-50 dark:bg-blue-950 dark:border-blue-700'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              )}
            >
              <div className="flex items-center justify-between mb-2">
                <metric.icon className={cn('w-4 h-4', metric.color)} />
                {metric.status !== 'normal' && (
                  <div className={cn(
                    'w-2 h-2 rounded-full',
                    metric.status === 'critical' ? 'bg-red-500' : 'bg-yellow-500'
                  )} />
                )}
              </div>
              <div className="text-left">
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  {metric.value}
                  <span className="text-sm font-normal text-gray-500 ml-1">
                    {metric.unit}
                  </span>
                </div>
                <div className="text-xs text-gray-500">{metric.label}</div>
              </div>
            </motion.button>
          ))}
        </div>

        {/* Selected Metric Details */}
        <motion.div
          key={selectedMetric}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          {(() => {
            const metric = metrics.find(m => m.id === selectedMetric);
            if (!metric) return null;

            return (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {metric.label} Details
                  </h4>
                  <span className={cn(
                    'px-2 py-1 text-xs font-medium rounded-full',
                    getStatusColor(metric.status)
                  )}>
                    {metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
                  </span>
                </div>
                
                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center">
                    <div className="font-medium text-gray-900 dark:text-white">Current</div>
                    <div className="text-gray-500">{metric.value} {metric.unit}</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-gray-900 dark:text-white">Avg 24h</div>
                    <div className="text-gray-500">{(metric.value * 0.95).toFixed(1)} {metric.unit}</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-gray-900 dark:text-white">Trend</div>
                    <div className="flex items-center justify-center">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                    </div>
                  </div>
                </div>

                {/* Threshold Information */}
                {mockThresholds[selectedMetric as keyof BiometricThresholds] && (
                  <div className="text-xs text-gray-500">
                    Normal range: {mockThresholds[selectedMetric as keyof BiometricThresholds]?.min || 'N/A'} - {mockThresholds[selectedMetric as keyof BiometricThresholds]?.max || 'N/A'} {metric.unit}
                  </div>
                )}
              </div>
            );
          })()}
        </motion.div>

        {/* Connected Devices */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            Connected Devices
          </h4>
          <div className="space-y-2">
            {connectedDevices.map((device) => (
              <div
                key={device.id}
                className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    'p-1 rounded',
                    device.connected 
                      ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  )}>
                    {getDeviceIcon(device.type)}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {device.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {device.connected ? (
                        <span className="flex items-center space-x-1">
                          <Wifi className="w-3 h-3" />
                          <span>Synced {device.last_sync}</span>
                        </span>
                      ) : (
                        <span className="flex items-center space-x-1">
                          <WifiOff className="w-3 h-3" />
                          <span>Last sync {device.last_sync}</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {device.battery_level && (
                    <div className="flex items-center space-x-1">
                      <Battery className={cn(
                        'w-3 h-3',
                        device.battery_level > 20 ? 'text-green-500' : 'text-red-500'
                      )} />
                      <span className="text-xs text-gray-500">
                        {device.battery_level}%
                      </span>
                    </div>
                  )}
                  
                  {!device.connected && (
                    <button
                      onClick={() => onDeviceAction?.(device.id, 'reconnect')}
                      className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Reconnect
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Health Alerts */}
        {metrics.some(m => m.status !== 'normal') && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="p-3 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg"
          >
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              <span className="text-sm font-medium text-yellow-700 dark:text-yellow-300">
                Health Alert
              </span>
            </div>
            <div className="text-xs text-yellow-600 dark:text-yellow-400">
              {metrics.filter(m => m.status !== 'normal').map(m => m.label).join(', ')} outside normal range.
              Consider taking a break or consulting a healthcare professional.
            </div>
          </motion.div>
        )}
      </div>
    </MosaicCard>
  );
};
