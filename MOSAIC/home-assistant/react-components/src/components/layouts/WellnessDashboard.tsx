/**
 * Wellness Dashboard Layout
 * Optimized for health monitoring and wellness tracking
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Heart, 
  Activity, 
  Moon, 
  Droplets, 
  Target, 
  TrendingUp,
  Brain,
  Zap,
  Clock,
  Award
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  DashboardConfig, 
  MosaicComponentProps,
  BiometricData 
} from '../../types';
import { cn } from '../../utils/cn';

interface WellnessDashboardProps extends MosaicComponentProps {
  config: DashboardConfig;
  biometricData: BiometricData;
  wellnessGoals: WellnessGoal[];
  onConfigChange?: (config: DashboardConfig) => void;
  onWidgetAction?: (widgetId: string, action: string, data?: any) => void;
}

interface WellnessGoal {
  id: string;
  type: 'steps' | 'water' | 'sleep' | 'meditation' | 'exercise';
  target: number;
  current: number;
  unit: string;
  streak: number;
}

export const WellnessDashboard: React.FC<WellnessDashboardProps> = ({
  config,
  biometricData,
  wellnessGoals,
  onConfigChange,
  onWidgetAction,
  className,
  lifecycle = 'FLOW_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [selectedMetric, setSelectedMetric] = React.useState<string>('overview');
  const [timeRange, setTimeRange] = React.useState<'today' | 'week' | 'month'>('today');

  // Mock wellness data
  const healthMetrics = {
    heart_rate: {
      current: 72,
      resting: 65,
      max_today: 145,
      zone: 'normal' as const,
      trend: 'stable' as const,
    },
    sleep: {
      last_night: 7.5,
      deep_sleep: 1.8,
      rem_sleep: 1.2,
      sleep_score: 85,
      bedtime: '23:30',
      wake_time: '07:00',
    },
    activity: {
      steps: 8420,
      calories: 2150,
      active_minutes: 45,
      floors: 12,
      distance: 6.2,
    },
    stress: {
      current_level: 35,
      avg_today: 42,
      peak_today: 78,
      recovery_time: 15,
    },
    hydration: {
      glasses_today: 6,
      target: 8,
      last_drink: '30 minutes ago',
    },
  };

  const mindfulnessData = {
    meditation_streak: 12,
    total_sessions: 156,
    avg_session_length: 15,
    mindful_minutes_today: 20,
    stress_reduction: 25,
  };

  const achievements = [
    { id: 1, title: '10K Steps', icon: '🚶', earned: true, date: 'Today' },
    { id: 2, title: 'Sleep Champion', icon: '😴', earned: true, date: 'Yesterday' },
    { id: 3, title: 'Hydration Hero', icon: '💧', earned: false, progress: 75 },
    { id: 4, title: 'Meditation Master', icon: '🧘', earned: true, date: '2 days ago' },
  ];

  // Get health zone color
  const getZoneColor = (zone: string) => {
    switch (zone) {
      case 'excellent': return 'text-green-600 bg-green-100 dark:bg-green-900';
      case 'good': return 'text-blue-600 bg-blue-100 dark:bg-blue-900';
      case 'normal': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
      case 'poor': return 'text-red-600 bg-red-100 dark:bg-red-900';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
    }
  };

  // Calculate progress percentage
  const getProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  return (
    <div className={cn('p-6 space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
            <Heart className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Wellness Dashboard
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Health & Mindfulness Tracking
            </p>
          </div>
        </div>

        {/* Time Range Selector */}
        <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {(['today', 'week', 'month'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={cn(
                'px-3 py-1 text-xs font-medium rounded-md transition-colors',
                timeRange === range
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              )}
            >
              {range.charAt(0).toUpperCase() + range.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Quick Health Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950 dark:to-pink-950 p-4 rounded-lg border border-red-200 dark:border-red-800">
          <div className="flex items-center space-x-2 mb-2">
            <Heart className="w-5 h-5 text-red-500" />
            <span className="text-sm font-medium text-red-700 dark:text-red-300">Heart Rate</span>
          </div>
          <div className="text-2xl font-bold text-red-600">{healthMetrics.heart_rate.current}</div>
          <div className="text-xs text-red-500">BPM • {healthMetrics.heart_rate.zone}</div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center space-x-2 mb-2">
            <Moon className="w-5 h-5 text-blue-500" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Sleep</span>
          </div>
          <div className="text-2xl font-bold text-blue-600">{healthMetrics.sleep.last_night}h</div>
          <div className="text-xs text-blue-500">Score: {healthMetrics.sleep.sleep_score}</div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-center space-x-2 mb-2">
            <Activity className="w-5 h-5 text-green-500" />
            <span className="text-sm font-medium text-green-700 dark:text-green-300">Steps</span>
          </div>
          <div className="text-2xl font-bold text-green-600">{healthMetrics.activity.steps.toLocaleString()}</div>
          <div className="text-xs text-green-500">{healthMetrics.activity.distance}km</div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950 dark:to-violet-950 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
          <div className="flex items-center space-x-2 mb-2">
            <Brain className="w-5 h-5 text-purple-500" />
            <span className="text-sm font-medium text-purple-700 dark:text-purple-300">Stress</span>
          </div>
          <div className="text-2xl font-bold text-purple-600">{healthMetrics.stress.current_level}</div>
          <div className="text-xs text-purple-500">Low stress level</div>
        </div>
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Daily Goals */}
        <div className="col-span-1">
          <MosaicCard
            title="Daily Goals"
            subtitle="Today's Progress"
            icon="🎯"
            lifecycle="FLOW_LC"
            priority="high"
            actions={[
              { id: 'edit_goals', label: 'Edit Goals', icon: '⚙️', onClick: () => {} },
            ]}
          >
            <div className="space-y-4">
              {wellnessGoals.map((goal) => {
                const progress = getProgress(goal.current, goal.target);
                const goalIcons = {
                  steps: '🚶',
                  water: '💧',
                  sleep: '😴',
                  meditation: '🧘',
                  exercise: '💪',
                };
                
                return (
                  <div key={goal.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{goalIcons[goal.type]}</span>
                        <span className="text-sm font-medium capitalize">{goal.type}</span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {goal.current}/{goal.target} {goal.unit}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${progress}%` }}
                        transition={{ duration: 0.5, ease: 'easeOut' }}
                        className={cn(
                          'h-2 rounded-full transition-colors',
                          progress >= 100 ? 'bg-green-500' : 
                          progress >= 75 ? 'bg-blue-500' :
                          progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                        )}
                      />
                    </div>
                    {goal.streak > 0 && (
                      <div className="text-xs text-gray-500">
                        🔥 {goal.streak} day streak
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </MosaicCard>
        </div>

        {/* Sleep Analysis */}
        <div className="col-span-1">
          <MosaicCard
            title="Sleep Analysis"
            subtitle={`${healthMetrics.sleep.last_night}h last night`}
            icon="🌙"
            lifecycle="FLOW_LC"
            priority="medium"
            actions={[
              { id: 'sleep_trends', label: 'View Trends', icon: '📊', onClick: () => {} },
            ]}
          >
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {healthMetrics.sleep.sleep_score}
                </div>
                <div className="text-sm text-gray-500">Sleep Score</div>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Deep Sleep</span>
                  <span className="text-sm font-medium">{healthMetrics.sleep.deep_sleep}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">REM Sleep</span>
                  <span className="text-sm font-medium">{healthMetrics.sleep.rem_sleep}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Bedtime</span>
                  <span className="text-sm font-medium">{healthMetrics.sleep.bedtime}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Wake Time</span>
                  <span className="text-sm font-medium">{healthMetrics.sleep.wake_time}</span>
                </div>
              </div>
            </div>
          </MosaicCard>
        </div>

        {/* Mindfulness */}
        <div className="col-span-1">
          <MosaicCard
            title="Mindfulness"
            subtitle={`${mindfulnessData.meditation_streak} day streak`}
            icon="🧘"
            lifecycle="FLOW_LC"
            priority="medium"
            actions={[
              { id: 'start_meditation', label: 'Meditate', icon: '🧘', onClick: () => {} },
              { id: 'breathing_exercise', label: 'Breathe', icon: '🫁', onClick: () => {} },
            ]}
          >
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-2 bg-purple-50 dark:bg-purple-950 rounded">
                  <div className="text-lg font-bold text-purple-600">{mindfulnessData.mindful_minutes_today}</div>
                  <div className="text-xs text-purple-500">Minutes Today</div>
                </div>
                <div className="text-center p-2 bg-green-50 dark:bg-green-950 rounded">
                  <div className="text-lg font-bold text-green-600">{mindfulnessData.stress_reduction}%</div>
                  <div className="text-xs text-green-500">Stress Reduction</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Total Sessions</span>
                  <span className="font-medium">{mindfulnessData.total_sessions}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Avg Session</span>
                  <span className="font-medium">{mindfulnessData.avg_session_length}min</span>
                </div>
              </div>
            </div>
          </MosaicCard>
        </div>

        {/* Activity Summary */}
        <div className="col-span-1 md:col-span-2">
          <MosaicCard
            title="Activity Summary"
            subtitle="Today's Movement"
            icon="🏃"
            lifecycle="FLOW_LC"
            priority="medium"
            actions={[
              { id: 'start_workout', label: 'Start Workout', icon: '💪', onClick: () => {} },
              { id: 'log_activity', label: 'Log Activity', icon: '📝', onClick: () => {} },
            ]}
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {healthMetrics.activity.steps.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">Steps</div>
                <div className="text-xs text-green-500">Goal: 10,000</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {healthMetrics.activity.calories}
                </div>
                <div className="text-sm text-gray-500">Calories</div>
                <div className="text-xs text-orange-500">Goal: 2,200</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {healthMetrics.activity.active_minutes}
                </div>
                <div className="text-sm text-gray-500">Active Min</div>
                <div className="text-xs text-blue-500">Goal: 60</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {healthMetrics.activity.floors}
                </div>
                <div className="text-sm text-gray-500">Floors</div>
                <div className="text-xs text-purple-500">Goal: 15</div>
              </div>
            </div>
          </MosaicCard>
        </div>

        {/* Achievements */}
        <div className="col-span-1">
          <MosaicCard
            title="Achievements"
            subtitle="Recent Milestones"
            icon="🏆"
            lifecycle="FLOW_LC"
            priority="low"
          >
            <div className="space-y-3">
              {achievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className={cn(
                    'flex items-center justify-between p-2 rounded',
                    achievement.earned 
                      ? 'bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800'
                      : 'bg-gray-50 dark:bg-gray-800'
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{achievement.icon}</span>
                    <div>
                      <div className={cn(
                        'text-sm font-medium',
                        achievement.earned ? 'text-yellow-700 dark:text-yellow-300' : 'text-gray-600 dark:text-gray-400'
                      )}>
                        {achievement.title}
                      </div>
                      {achievement.earned ? (
                        <div className="text-xs text-yellow-600 dark:text-yellow-400">
                          {achievement.date}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500">
                          {achievement.progress}% complete
                        </div>
                      )}
                    </div>
                  </div>
                  {achievement.earned && (
                    <Award className="w-4 h-4 text-yellow-500" />
                  )}
                </div>
              ))}
            </div>
          </MosaicCard>
        </div>
      </div>
    </div>
  );
};
