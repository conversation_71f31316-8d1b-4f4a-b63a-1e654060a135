/**
 * Developer Workspace Dashboard Layout
 * Optimized for software development workflow with APEX-LC integration
 */

import React from 'react';
import { motion } from 'framer-motion';
import { Grid, Monitor, Code, GitBranch, Bug, Zap } from 'lucide-react';
import { FlowStateMonitor } from '../widgets/FlowStateMonitor';
import { AgentStatus } from '../widgets/AgentStatus';
import { MosaicCard } from '../core/MosaicCard';
import { 
  DashboardLayout, 
  DashboardConfig, 
  FlowStateData, 
  AgentStatus as AgentStatusType,
  MosaicComponentProps 
} from '../../types';
import { cn } from '../../utils/cn';

interface DeveloperWorkspaceProps extends MosaicComponentProps {
  config: DashboardConfig;
  flowStateData: FlowStateData;
  agents: AgentStatusType[];
  onConfigChange?: (config: DashboardConfig) => void;
  onWidgetAction?: (widgetId: string, action: string, data?: any) => void;
}

export const DeveloperWorkspace: React.FC<DeveloperWorkspaceProps> = ({
  config,
  flowStateData,
  agents,
  onConfigChange,
  onWidgetAction,
  className,
  lifecycle = 'APEX_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [layoutMode, setLayoutMode] = React.useState<'focus' | 'overview' | 'debug'>('overview');
  const [selectedAgent, setSelectedAgent] = React.useState<string>();

  // Filter agents for development-related lifecycles
  const devAgents = agents.filter(agent => 
    ['APEX_LC', 'PRISM_LC', 'QUANTUM_LC'].includes(agent.lifecycle)
  );

  // Mock data for development widgets
  const codeMetrics = {
    commits_today: 8,
    lines_added: 342,
    lines_removed: 156,
    tests_passing: 94,
    test_coverage: 87.5,
    build_status: 'success' as const,
    deployment_status: 'deployed' as const,
    active_branches: 3,
    open_prs: 2,
    code_reviews_pending: 1,
  };

  const environmentStatus = {
    development: { status: 'healthy', uptime: '99.9%', last_deploy: '2 hours ago' },
    staging: { status: 'healthy', uptime: '99.8%', last_deploy: '1 day ago' },
    production: { status: 'healthy', uptime: '99.99%', last_deploy: '3 days ago' },
  };

  const issueTracker = {
    open_bugs: 5,
    critical_issues: 1,
    feature_requests: 12,
    in_progress: 8,
    ready_for_review: 3,
    blocked: 2,
  };

  // Layout configurations for different modes
  const getLayoutConfig = () => {
    switch (layoutMode) {
      case 'focus':
        return 'grid-cols-1 lg:grid-cols-2 gap-4';
      case 'debug':
        return 'grid-cols-1 lg:grid-cols-3 gap-3';
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4';
    }
  };

  // Widget visibility based on layout mode
  const isWidgetVisible = (widgetType: string) => {
    if (layoutMode === 'focus') {
      return ['flow_state', 'code_metrics'].includes(widgetType);
    }
    if (layoutMode === 'debug') {
      return ['agent_status', 'environment_status', 'issue_tracker'].includes(widgetType);
    }
    return true; // Show all in overview mode
  };

  return (
    <div className={cn('p-6 space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Monitor className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Developer Workspace
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              APEX-LC Development Environment
            </p>
          </div>
        </div>

        {/* Layout Mode Selector */}
        <div className="flex items-center space-x-2">
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {(['overview', 'focus', 'debug'] as const).map((mode) => (
              <button
                key={mode}
                onClick={() => setLayoutMode(mode)}
                className={cn(
                  'px-3 py-1 text-xs font-medium rounded-md transition-colors',
                  layoutMode === mode
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                )}
              >
                {mode.charAt(0).toUpperCase() + mode.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex items-center space-x-3 overflow-x-auto pb-2">
        <button className="flex items-center space-x-2 px-4 py-2 bg-green-100 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800 text-green-700 dark:text-green-300 rounded-lg transition-colors whitespace-nowrap">
          <GitBranch className="w-4 h-4" />
          <span className="text-sm font-medium">New Branch</span>
        </button>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-lg transition-colors whitespace-nowrap">
          <Code className="w-4 h-4" />
          <span className="text-sm font-medium">Code Review</span>
        </button>
        <button className="flex items-center space-x-2 px-4 py-2 bg-purple-100 hover:bg-purple-200 dark:bg-purple-900 dark:hover:bg-purple-800 text-purple-700 dark:text-purple-300 rounded-lg transition-colors whitespace-nowrap">
          <Zap className="w-4 h-4" />
          <span className="text-sm font-medium">Deploy</span>
        </button>
        <button className="flex items-center space-x-2 px-4 py-2 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 rounded-lg transition-colors whitespace-nowrap">
          <Bug className="w-4 h-4" />
          <span className="text-sm font-medium">Debug</span>
        </button>
      </div>

      {/* Main Dashboard Grid */}
      <motion.div
        layout
        className={cn('grid', getLayoutConfig())}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        {/* Flow State Monitor */}
        {isWidgetVisible('flow_state') && (
          <motion.div
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={layoutMode === 'focus' ? 'col-span-1' : 'col-span-1'}
          >
            <FlowStateMonitor
              data={flowStateData}
              config={{
                deep_work_threshold: 85,
                break_reminder_interval: 90,
                environment_optimization: true,
                biometric_integration: true,
                notification_settings: {
                  interruption_blocking: true,
                  focus_mode_alerts: true,
                  break_reminders: true,
                },
              }}
              onStateChange={(state) => onWidgetAction?.('flow_state', 'state_change', state)}
              lifecycle="FLOW_LC"
              priority="critical"
            />
          </motion.div>
        )}

        {/* Code Metrics */}
        {isWidgetVisible('code_metrics') && (
          <motion.div
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={layoutMode === 'focus' ? 'col-span-1' : 'col-span-1'}
          >
            <MosaicCard
              title="Code Metrics"
              subtitle="Today's Development Activity"
              icon="📊"
              lifecycle="APEX_LC"
              priority="high"
              actions={[
                { id: 'view_details', label: 'View Details', icon: '📈', onClick: () => {} },
                { id: 'export_data', label: 'Export', icon: '📤', onClick: () => {} },
              ]}
            >
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{codeMetrics.commits_today}</div>
                    <div className="text-xs text-gray-500">Commits</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">+{codeMetrics.lines_added}</div>
                    <div className="text-xs text-gray-500">Lines Added</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Test Coverage</span>
                    <span className="text-sm font-medium">{codeMetrics.test_coverage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${codeMetrics.test_coverage}%` }}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center">
                    <div className="font-medium text-gray-900 dark:text-white">{codeMetrics.active_branches}</div>
                    <div className="text-gray-500">Branches</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-gray-900 dark:text-white">{codeMetrics.open_prs}</div>
                    <div className="text-gray-500">Open PRs</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-gray-900 dark:text-white">{codeMetrics.code_reviews_pending}</div>
                    <div className="text-gray-500">Reviews</div>
                  </div>
                </div>
              </div>
            </MosaicCard>
          </motion.div>
        )}

        {/* Agent Status */}
        {isWidgetVisible('agent_status') && (
          <motion.div
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={layoutMode === 'debug' ? 'col-span-1' : 'col-span-1 md:col-span-2'}
          >
            <AgentStatus
              agents={devAgents}
              selectedAgent={selectedAgent}
              onAgentSelect={setSelectedAgent}
              onAgentAction={(agentId, action) => onWidgetAction?.('agent_status', action, { agentId })}
              lifecycle="PULSE_LC"
              priority="high"
            />
          </motion.div>
        )}

        {/* Environment Status */}
        {isWidgetVisible('environment_status') && (
          <motion.div
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <MosaicCard
              title="Environment Status"
              subtitle="Deployment Pipeline"
              icon="🚀"
              lifecycle="NEXUS_LC"
              priority="medium"
              actions={[
                { id: 'deploy', label: 'Deploy', icon: '🚀', onClick: () => {} },
                { id: 'rollback', label: 'Rollback', icon: '⏪', onClick: () => {}, variant: 'danger' },
              ]}
            >
              <div className="space-y-3">
                {Object.entries(environmentStatus).map(([env, status]) => (
                  <div key={env} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="flex items-center space-x-2">
                      <div className={cn(
                        'w-2 h-2 rounded-full',
                        status.status === 'healthy' ? 'bg-green-500' : 'bg-red-500'
                      )} />
                      <span className="text-sm font-medium capitalize">{env}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-600 dark:text-gray-400">{status.uptime}</div>
                      <div className="text-xs text-gray-500">{status.last_deploy}</div>
                    </div>
                  </div>
                ))}
              </div>
            </MosaicCard>
          </motion.div>
        )}

        {/* Issue Tracker */}
        {isWidgetVisible('issue_tracker') && (
          <motion.div
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <MosaicCard
              title="Issue Tracker"
              subtitle="Development Tasks"
              icon="🐛"
              lifecycle="PRISM_LC"
              priority="medium"
              actions={[
                { id: 'create_issue', label: 'New Issue', icon: '➕', onClick: () => {} },
                { id: 'view_board', label: 'View Board', icon: '📋', onClick: () => {} },
              ]}
            >
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-center p-2 bg-red-50 dark:bg-red-950 rounded">
                    <div className="text-lg font-bold text-red-600">{issueTracker.critical_issues}</div>
                    <div className="text-xs text-red-500">Critical</div>
                  </div>
                  <div className="text-center p-2 bg-orange-50 dark:bg-orange-950 rounded">
                    <div className="text-lg font-bold text-orange-600">{issueTracker.open_bugs}</div>
                    <div className="text-xs text-orange-500">Bugs</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">In Progress</span>
                    <span className="font-medium">{issueTracker.in_progress}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Ready for Review</span>
                    <span className="font-medium">{issueTracker.ready_for_review}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Feature Requests</span>
                    <span className="font-medium">{issueTracker.feature_requests}</span>
                  </div>
                </div>
              </div>
            </MosaicCard>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};
