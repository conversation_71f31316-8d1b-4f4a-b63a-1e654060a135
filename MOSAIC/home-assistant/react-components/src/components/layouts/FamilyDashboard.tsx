/**
 * Family Dashboard Layout
 * Optimized for family life management and home automation
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  Users, 
  Calendar, 
  Shield, 
  Thermometer, 
  Car,
  Heart,
  Clock,
  MapPin,
  Battery
} from 'lucide-react';
import { MosaicCard } from '../core/MosaicCard';
import { 
  DashboardConfig, 
  MosaicComponentProps,
  EnvironmentState,
  BiometricData 
} from '../../types';
import { cn } from '../../utils/cn';

interface FamilyDashboardProps extends MosaicComponentProps {
  config: DashboardConfig;
  environmentState: EnvironmentState;
  familyMembers: FamilyMember[];
  onConfigChange?: (config: DashboardConfig) => void;
  onWidgetAction?: (widgetId: string, action: string, data?: any) => void;
}

interface FamilyMember {
  id: string;
  name: string;
  avatar?: string;
  location: 'home' | 'work' | 'school' | 'away';
  device_battery?: number;
  last_seen: string;
  biometric_data?: BiometricData;
  calendar_next?: {
    title: string;
    time: string;
    location?: string;
  };
}

export const FamilyDashboard: React.FC<FamilyDashboardProps> = ({
  config,
  environmentState,
  familyMembers,
  onConfigChange,
  onWidgetAction,
  className,
  lifecycle = 'AURORA_LC',
  priority = 'high',
  onError,
  onUpdate,
}) => {
  const [selectedRoom, setSelectedRoom] = React.useState<string>('overview');
  const [timeOfDay, setTimeOfDay] = React.useState<'morning' | 'afternoon' | 'evening' | 'night'>('morning');

  // Mock data for family dashboard
  const homeStatus = {
    security: {
      armed: true,
      doors_locked: 4,
      windows_closed: 12,
      cameras_active: 6,
      last_activity: '2 minutes ago',
    },
    climate: {
      temperature: 22,
      humidity: 45,
      air_quality: 'good',
      energy_usage: 'normal',
    },
    devices: {
      lights_on: 8,
      smart_devices: 24,
      offline_devices: 1,
      automation_active: 15,
    },
  };

  const familySchedule = [
    { time: '08:00', event: 'Kids School Pickup', person: 'Sarah', location: 'Elementary School' },
    { time: '10:30', event: 'Grocery Shopping', person: 'Mom', location: 'Whole Foods' },
    { time: '15:00', event: 'Soccer Practice', person: 'Alex', location: 'Community Center' },
    { time: '18:00', event: 'Family Dinner', person: 'Everyone', location: 'Home' },
    { time: '20:00', event: 'Movie Night', person: 'Family', location: 'Living Room' },
  ];

  const quickActions = [
    { id: 'good_morning', label: 'Good Morning', icon: '🌅', action: 'morning_routine' },
    { id: 'leaving_home', label: 'Leaving Home', icon: '🚗', action: 'away_mode' },
    { id: 'coming_home', label: 'Coming Home', icon: '🏠', action: 'welcome_home' },
    { id: 'bedtime', label: 'Bedtime', icon: '🌙', action: 'night_routine' },
    { id: 'movie_time', label: 'Movie Time', icon: '🎬', action: 'entertainment_mode' },
    { id: 'dinner_time', label: 'Dinner Time', icon: '🍽️', action: 'dinner_mode' },
  ];

  // Get location icon
  const getLocationIcon = (location: FamilyMember['location']) => {
    switch (location) {
      case 'home': return '🏠';
      case 'work': return '💼';
      case 'school': return '🎓';
      default: return '📍';
    }
  };

  // Get location color
  const getLocationColor = (location: FamilyMember['location']) => {
    switch (location) {
      case 'home': return 'text-green-600 bg-green-100 dark:bg-green-900';
      case 'work': return 'text-blue-600 bg-blue-100 dark:bg-blue-900';
      case 'school': return 'text-purple-600 bg-purple-100 dark:bg-purple-900';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
    }
  };

  return (
    <div className={cn('p-6 space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <Home className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Family Dashboard
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Home & Family Management
            </p>
          </div>
        </div>

        {/* Time of Day Indicator */}
        <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <Clock className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </span>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
        {quickActions.map((action) => (
          <button
            key={action.id}
            onClick={() => onWidgetAction?.('quick_actions', action.action)}
            className="flex flex-col items-center space-y-2 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-all duration-200 hover:scale-105"
          >
            <span className="text-2xl">{action.icon}</span>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 text-center">
              {action.label}
            </span>
          </button>
        ))}
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {/* Family Members */}
        <div className="col-span-1 md:col-span-2">
          <MosaicCard
            title="Family Members"
            subtitle={`${familyMembers.filter(m => m.location === 'home').length} at home`}
            icon="👨‍👩‍👧‍👦"
            lifecycle="AURORA_LC"
            priority="high"
            actions={[
              { id: 'locate_all', label: 'Locate All', icon: '📍', onClick: () => {} },
              { id: 'send_message', label: 'Send Message', icon: '💬', onClick: () => {} },
            ]}
          >
            <div className="space-y-3">
              {familyMembers.map((member) => (
                <motion.div
                  key={member.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                      {member.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {member.name}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={cn(
                          'text-xs px-2 py-1 rounded-full',
                          getLocationColor(member.location)
                        )}>
                          {getLocationIcon(member.location)} {member.location}
                        </span>
                        {member.device_battery && (
                          <div className="flex items-center space-x-1">
                            <Battery className="w-3 h-3 text-gray-500" />
                            <span className="text-xs text-gray-500">
                              {member.device_battery}%
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-xs text-gray-500">{member.last_seen}</div>
                    {member.calendar_next && (
                      <div className="text-xs text-blue-600 dark:text-blue-400">
                        {member.calendar_next.time} - {member.calendar_next.title}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </MosaicCard>
        </div>

        {/* Home Security */}
        <div className="col-span-1">
          <MosaicCard
            title="Home Security"
            subtitle={homeStatus.security.armed ? 'Armed' : 'Disarmed'}
            icon="🛡️"
            lifecycle="SHIELD_LC"
            priority="critical"
            actions={[
              { 
                id: 'toggle_security', 
                label: homeStatus.security.armed ? 'Disarm' : 'Arm',
                icon: homeStatus.security.armed ? '🔓' : '🔒',
                onClick: () => {},
                variant: homeStatus.security.armed ? 'danger' : 'primary'
              },
            ]}
          >
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="text-center p-2 bg-green-50 dark:bg-green-950 rounded">
                  <div className="font-bold text-green-600">{homeStatus.security.doors_locked}</div>
                  <div className="text-xs text-green-500">Doors Locked</div>
                </div>
                <div className="text-center p-2 bg-blue-50 dark:bg-blue-950 rounded">
                  <div className="font-bold text-blue-600">{homeStatus.security.cameras_active}</div>
                  <div className="text-xs text-blue-500">Cameras Active</div>
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-xs text-gray-500">Last Activity</div>
                <div className="text-sm font-medium">{homeStatus.security.last_activity}</div>
              </div>
            </div>
          </MosaicCard>
        </div>

        {/* Climate Control */}
        <div className="col-span-1">
          <MosaicCard
            title="Climate"
            subtitle={`${homeStatus.climate.temperature}°C`}
            icon="🌡️"
            lifecycle="NEXUS_LC"
            priority="medium"
            actions={[
              { id: 'adjust_temp', label: 'Adjust', icon: '⚙️', onClick: () => {} },
            ]}
          >
            <div className="space-y-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {homeStatus.climate.temperature}°C
                </div>
                <div className="text-sm text-gray-500">
                  {homeStatus.climate.humidity}% humidity
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Air Quality</span>
                  <span className="font-medium capitalize text-green-600">
                    {homeStatus.climate.air_quality}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Energy Usage</span>
                  <span className="font-medium capitalize">
                    {homeStatus.climate.energy_usage}
                  </span>
                </div>
              </div>
            </div>
          </MosaicCard>
        </div>

        {/* Today's Schedule */}
        <div className="col-span-1 md:col-span-2">
          <MosaicCard
            title="Today's Schedule"
            subtitle={`${familySchedule.length} events`}
            icon="📅"
            lifecycle="FLOW_LC"
            priority="medium"
            actions={[
              { id: 'add_event', label: 'Add Event', icon: '➕', onClick: () => {} },
              { id: 'view_calendar', label: 'View Calendar', icon: '📅', onClick: () => {} },
            ]}
          >
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {familySchedule.map((event, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-sm font-medium text-blue-600 dark:text-blue-400 min-w-[3rem]">
                      {event.time}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {event.event}
                      </div>
                      <div className="text-xs text-gray-500">
                        {event.person} • {event.location}
                      </div>
                    </div>
                  </div>
                  <MapPin className="w-4 h-4 text-gray-400" />
                </div>
              ))}
            </div>
          </MosaicCard>
        </div>

        {/* Smart Devices */}
        <div className="col-span-1">
          <MosaicCard
            title="Smart Devices"
            subtitle={`${homeStatus.devices.smart_devices} devices`}
            icon="💡"
            lifecycle="NEXUS_LC"
            priority="low"
            actions={[
              { id: 'device_settings', label: 'Settings', icon: '⚙️', onClick: () => {} },
            ]}
          >
            <div className="space-y-3">
              <div className="grid grid-cols-1 gap-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Lights On</span>
                  <span className="font-medium">{homeStatus.devices.lights_on}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Automations</span>
                  <span className="font-medium">{homeStatus.devices.automation_active}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Offline</span>
                  <span className={cn(
                    'font-medium',
                    homeStatus.devices.offline_devices > 0 ? 'text-red-600' : 'text-green-600'
                  )}>
                    {homeStatus.devices.offline_devices}
                  </span>
                </div>
              </div>
              
              <button className="w-full py-2 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded text-sm font-medium transition-colors">
                Manage Devices
              </button>
            </div>
          </MosaicCard>
        </div>

        {/* Weather & Environment */}
        <div className="col-span-1">
          <MosaicCard
            title="Weather"
            subtitle="Today's Forecast"
            icon="🌤️"
            lifecycle="NEXUS_LC"
            priority="low"
          >
            <div className="space-y-3">
              <div className="text-center">
                <div className="text-3xl">☀️</div>
                <div className="text-lg font-bold">24°C</div>
                <div className="text-sm text-gray-500">Sunny</div>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="text-center">
                  <div className="font-medium">High</div>
                  <div className="text-gray-500">26°C</div>
                </div>
                <div className="text-center">
                  <div className="font-medium">Low</div>
                  <div className="text-gray-500">18°C</div>
                </div>
              </div>
            </div>
          </MosaicCard>
        </div>
      </div>
    </div>
  );
};
