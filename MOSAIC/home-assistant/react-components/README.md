# MOSAIC Home Assistant React Components

A comprehensive React component library for integrating MOSAIC lifecycle management with Home Assistant OS, enabling seamless work-life synthesis automation and smart workspace management.

## Features

- 🏠 **Home Assistant Integration**: Built on @hakit/core and @hakit/components
- 🧠 **Flow State Monitoring**: Real-time tracking of focus, productivity, and environmental factors
- 🤖 **Agent Status Management**: Monitor and control MOSAIC lifecycle agents
- 📊 **Biometric Integration**: Apple Watch and other health device data
- 🎨 **Lifecycle-Aware Styling**: Visual indicators for each MOSAIC lifecycle
- 🌙 **Dark Mode Support**: Automatic theme switching
- ⚡ **Performance Optimized**: Framer Motion animations and efficient rendering
- 📱 **Responsive Design**: Works across desktop, tablet, and mobile

## Installation

```bash
npm install @mosaic/home-assistant-react
```

## Quick Start

```tsx
import React from 'react';
import { FlowStateMonitor, AgentStatus } from '@mosaic/home-assistant-react';
import '@mosaic/home-assistant-react/styles';

function MosaicDashboard() {
  const flowData = {
    current_state: 'deep_work',
    focus_score: 85,
    interruption_count: 2,
    session_duration: 120,
    productivity_metrics: {
      tasks_completed: 5,
      code_commits: 3,
      meetings_attended: 1,
      documents_created: 2,
    },
    environmental_factors: {
      noise_level: 35,
      lighting_level: 75,
      temperature: 22,
      air_quality: 90,
    },
  };

  const agents = [
    {
      agent_id: 'apex-001',
      name: 'APEX Development Agent',
      lifecycle: 'APEX_LC',
      status: 'active',
      health_score: 95,
      // ... more agent data
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
      <FlowStateMonitor
        data={flowData}
        config={{
          deep_work_threshold: 80,
          break_reminder_interval: 90,
          environment_optimization: true,
          biometric_integration: true,
          notification_settings: {
            interruption_blocking: true,
            focus_mode_alerts: true,
            break_reminders: true,
          },
        }}
        onStateChange={(state) => console.log('Flow state changed:', state)}
      />
      
      <AgentStatus
        agents={agents}
        onAgentSelect={(agentId) => console.log('Selected agent:', agentId)}
        onAgentAction={(agentId, action) => console.log('Agent action:', agentId, action)}
      />
    </div>
  );
}
```

## Components

### Core Components

#### MosaicCard
Base card component for all MOSAIC widgets with lifecycle-aware styling.

```tsx
<MosaicCard
  title="Custom Widget"
  subtitle="Lifecycle integration"
  icon="🎯"
  lifecycle="APEX_LC"
  priority="high"
  actions={[
    { id: 'refresh', label: 'Refresh', icon: '🔄', onClick: () => {} }
  ]}
>
  <div>Your content here</div>
</MosaicCard>
```

#### StatusIndicator
Visual status indicator with animation support.

```tsx
<StatusIndicator 
  status="online" 
  label="Agent Active" 
  size="md" 
  showLabel={true} 
/>
```

### Widget Components

#### FlowStateMonitor
Comprehensive flow state tracking with environmental and biometric data.

**Props:**
- `data: FlowStateData` - Current flow state metrics
- `config: FlowStateConfig` - Configuration settings
- `onStateChange?: (state) => void` - State change callback
- `onConfigChange?: (config) => void` - Configuration change callback

#### AgentStatus
Monitor and manage MOSAIC lifecycle agents.

**Props:**
- `agents: AgentStatusType[]` - Array of agent status data
- `selectedAgent?: string` - Currently selected agent ID
- `onAgentSelect?: (agentId) => void` - Agent selection callback
- `onAgentAction?: (agentId, action) => void` - Agent action callback

## Lifecycle Integration

Each component supports lifecycle-aware styling and behavior:

```tsx
// Lifecycle colors and styling
const lifecycles = [
  'APEX_LC',    // Development - Blue
  'PRISM_LC',   // Knowledge - Purple  
  'AURORA_LC',  // Customer Success - Green
  'NEXUS_LC',   // Integration - Orange
  'FLUX_LC',    // Data Flow - Cyan
  'SPARK_LC',   // Innovation - Yellow
  'SHIELD_LC',  // Security - Red
  'QUANTUM_LC', // AI/ML - Indigo
  'ECHO_LC',    // Communication - Pink
  'PULSE_LC',   // Monitoring - Emerald
  'FLOW_LC',    // Orchestration - Violet
];
```

## Home Assistant Integration

### Setup

1. Install the MOSAIC Home Assistant add-on
2. Configure the React component integration
3. Set up automation rules

```yaml
# configuration.yaml
mosaic:
  react_components:
    enabled: true
    port: 3000
    ssl: false
    cors_allowed_origins:
      - "http://localhost:3000"
      - "https://your-domain.com"
```

### Event Handling

```tsx
import { useHass } from '@hakit/core';

function MyComponent() {
  const { callService, useEntity } = useHass();
  
  const handleFlowStateChange = (state) => {
    callService({
      domain: 'mosaic',
      service: 'set_flow_state',
      serviceData: { state },
    });
  };
  
  const lightEntity = useEntity('light.office_main');
  
  return (
    <FlowStateMonitor
      data={flowData}
      onStateChange={handleFlowStateChange}
    />
  );
}
```

## Theming

### Custom Theme

```tsx
import { MosaicTheme } from '@mosaic/home-assistant-react';

const customTheme: MosaicTheme = {
  name: 'custom',
  colors: {
    primary: '#3b82f6',
    secondary: '#6366f1',
    // ... more colors
  },
  // ... more theme properties
};
```

### CSS Variables

```css
:root {
  --mosaic-primary: #3b82f6;
  --mosaic-secondary: #6366f1;
  --mosaic-accent: #f59e0b;
  --mosaic-background: #ffffff;
  --mosaic-surface: #f9fafb;
  --mosaic-text: #111827;
  --mosaic-border: #e5e7eb;
}

[data-theme="dark"] {
  --mosaic-background: #111827;
  --mosaic-surface: #1f2937;
  --mosaic-text: #f9fafb;
  --mosaic-border: #374151;
}
```

## Development

### Setup

```bash
git clone <repository>
cd mosaic/home-assistant/react-components
npm install
npm run dev
```

### Building

```bash
npm run build
npm run type-check
npm run lint
```

### Testing

```bash
npm run test
npm run test:coverage
npm run test:ui
```

### Storybook

```bash
npm run storybook
npm run build-storybook
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

- 📖 [Documentation](https://docs.alias.org/mosaic/home-assistant)
- 💬 [Discord Community](https://discord.gg/alias)
- 🐛 [Issue Tracker](https://github.com/alias-org/mosaic/issues)
- 📧 [Email Support](mailto:<EMAIL>)
