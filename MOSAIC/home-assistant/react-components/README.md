# MOSAIC Home Assistant React Components

A comprehensive React component library for integrating MOSAIC lifecycle management with Home Assistant OS, enabling seamless work-life synthesis automation and smart workspace management.

## Features

- 🏠 **Home Assistant Integration**: Built on @hakit/core and @hakit/components
- 🧠 **Flow State Monitoring**: Real-time tracking of focus, productivity, and environmental factors
- 🤖 **Agent Status Management**: Monitor and control MOSAIC lifecycle agents
- 📊 **Biometric Integration**: Apple Watch and other health device data
- 🎨 **Lifecycle-Aware Styling**: Visual indicators for each MOSAIC lifecycle
- 🌙 **Dark Mode Support**: Automatic theme switching
- ⚡ **Performance Optimized**: Framer Motion animations and efficient rendering
- 📱 **Responsive Design**: Works across desktop, tablet, and mobile

## Installation

```bash
npm install @mosaic/home-assistant-react
```

## Quick Start

```tsx
import React from 'react';
import {
  FlowStateMonitor,
  AgentStatus,
  TaskManager,
  CalendarIntegration,
  QuickActionsPanel,
  NotificationCenter,
  DeveloperWorkspace
} from '@mosaic/home-assistant-react';
import '@mosaic/home-assistant-react/styles';

function MosaicDashboard() {
  const flowData = {
    current_state: 'deep_work',
    focus_score: 85,
    interruption_count: 2,
    session_duration: 120,
    productivity_metrics: {
      tasks_completed: 5,
      code_commits: 3,
      meetings_attended: 1,
      documents_created: 2,
    },
    environmental_factors: {
      noise_level: 35,
      lighting_level: 75,
      temperature: 22,
      air_quality: 90,
    },
  };

  const agents = [
    {
      agent_id: 'apex-001',
      name: 'APEX Development Agent',
      lifecycle: 'APEX_LC',
      status: 'active',
      health_score: 95,
      // ... more agent data
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
      <FlowStateMonitor
        data={flowData}
        config={{
          deep_work_threshold: 80,
          break_reminder_interval: 90,
          environment_optimization: true,
          biometric_integration: true,
          notification_settings: {
            interruption_blocking: true,
            focus_mode_alerts: true,
            break_reminders: true,
          },
        }}
        onStateChange={(state) => console.log('Flow state changed:', state)}
      />
      
      <AgentStatus
        agents={agents}
        onAgentSelect={(agentId) => console.log('Selected agent:', agentId)}
        onAgentAction={(agentId, action) => console.log('Agent action:', agentId, action)}
      />

      <TaskManager
        tasks={[
          {
            id: '1',
            title: 'Implement APEX-LC Feature',
            status: 'in_progress',
            priority: 'high',
            lifecycle: 'APEX_LC',
            due_date: '2024-01-20',
            automation_triggers: ['deploy_on_complete'],
          }
        ]}
        onTaskUpdate={(taskId, updates) => console.log('Task updated:', taskId, updates)}
      />

      <CalendarIntegration
        events={[
          {
            id: '1',
            title: 'Deep Work Session',
            start_time: '2024-01-15T14:00:00Z',
            end_time: '2024-01-15T16:00:00Z',
            event_type: 'focus_time',
            lifecycle: 'FLOW_LC',
            automation_rules: {
              lighting: { brightness: 80, temperature: 4500 },
              climate: { temperature: 21 },
              notifications: false,
            },
          }
        ]}
        onEnvironmentPrep={(eventId, prepType) => console.log('Prep environment:', eventId, prepType)}
      />

      <QuickActionsPanel
        actions={[
          {
            id: 'deep_work_mode',
            label: 'Deep Work Mode',
            icon: '🎯',
            category: 'environment',
            type: 'toggle',
            lifecycle: 'FLOW_LC',
            shortcut: '⌘+D',
          }
        ]}
        onActionExecute={(actionId) => console.log('Action executed:', actionId)}
      />

      <NotificationCenter
        notifications={[
          {
            id: '1',
            title: 'APEX-LC Deployment Complete',
            message: 'Successfully deployed version 2.1.0',
            type: 'success',
            priority: 'medium',
            lifecycle: 'APEX_LC',
            source: 'Deployment Pipeline',
            timestamp: '2024-01-15T14:30:00Z',
            read: false,
            dismissed: false,
          }
        ]}
        onNotificationAction={(notificationId, action) => console.log('Notification action:', notificationId, action)}
      />
    </div>
  );
}

// Or use a pre-built dashboard layout
function DeveloperDashboard() {
  return (
    <DeveloperWorkspace
      layoutMode="overview"
      onLayoutModeChange={(mode) => console.log('Layout mode changed:', mode)}
      onQuickAction={(action) => console.log('Quick action:', action)}
    />
  );
}
```

## Components

### Core Components

#### MosaicCard
Base card component for all MOSAIC widgets with lifecycle-aware styling.

```tsx
<MosaicCard
  title="Custom Widget"
  subtitle="Lifecycle integration"
  icon="🎯"
  lifecycle="APEX_LC"
  priority="high"
  actions={[
    { id: 'refresh', label: 'Refresh', icon: '🔄', onClick: () => {} }
  ]}
>
  <div>Your content here</div>
</MosaicCard>
```

#### StatusIndicator
Visual status indicator with animation support.

```tsx
<StatusIndicator 
  status="online" 
  label="Agent Active" 
  size="md" 
  showLabel={true} 
/>
```

### Widget Components

#### FlowStateMonitor
Comprehensive flow state tracking with environmental and biometric data integration.

**Features:**
- Real-time flow state display with color-coded states (deep_work, collaboration, break, transition, offline)
- Animated focus score visualization with progress bars and thresholds
- Interruption tracking and productivity metrics (tasks, commits, meetings, documents)
- Environmental factors monitoring (noise, temperature, air quality)
- Optional biometric data integration (heart rate, stress level, activity)
- Quick action buttons for state changes and session management

**Props:**
- `data: FlowStateData` - Current flow state metrics
- `config: FlowStateConfig` - Configuration settings
- `onStateChange?: (state) => void` - State change callback
- `onConfigChange?: (config) => void` - Configuration change callback

#### AgentStatus
Monitor and manage MOSAIC lifecycle agents with detailed performance metrics.

**Features:**
- System overview with total, active, and error agent counts plus average health score
- Filterable agent list with grid/list view modes and status-based filtering
- Detailed agent information with performance metrics, resource usage, and current tasks
- Interactive agent selection with expandable action controls
- Health score visualization with color-coded indicators and status badges
- Agent action support (restart, logs viewing, debugging operations)

**Props:**
- `agents: AgentStatusType[]` - Array of agent status data
- `selectedAgent?: string` - Currently selected agent ID
- `onAgentSelect?: (agentId) => void` - Agent selection callback
- `onAgentAction?: (agentId, action) => void` - Agent action callback

#### EnvironmentControl
Smart workspace automation control with comprehensive environment management.

**Features:**
- Tabbed interface for lighting, climate, audio, and security control
- Environment preset system with configurable presets for different work modes
- Lighting controls (brightness, color temperature, adaptive lighting, circadian rhythm)
- Climate control (temperature, humidity, air quality, fan speed)
- Audio management (volume, source selection, ambient sounds, noise cancellation)
- Security system integration (armed/disarmed status, door/window monitoring, camera control)

#### BiometricTracker
Advanced biometric monitoring with multi-device integration and health alerting.

**Features:**
- Real-time biometric data display (heart rate, stress level, blood oxygen, body temperature)
- Device connectivity management with battery status and sync tracking
- Threshold-based health alerting with configurable normal ranges
- Multi-device support (Apple Watch, iPhone Health, smart scales, blood pressure monitors)
- Health status visualization with color-coded indicators and trend analysis
- Time range selection for historical data viewing (1h, 24h, 7d)

#### TaskManager
MOSAIC task management with Home Assistant automation triggers and lifecycle integration.

**Features:**
- Task overview with status-based filtering (all, todo, in_progress, completed)
- Task statistics dashboard with visual progress indicators
- Detailed task information (priority, lifecycle, assignee, due dates, time tracking)
- Automation trigger integration for smart task management
- Expandable task details with tags, descriptions, and time estimates
- Interactive task status changes and completion tracking

#### CalendarIntegration
Smart scheduling with automated environment adjustments and meeting preparation.

**Features:**
- Multiple view modes (day, week, agenda) with date navigation
- Next event highlighting with countdown and preparation options
- Event type categorization (meeting, focus_time, break, personal, travel)
- Automated environment preparation based on event types
- Meeting link integration with one-click join
- Lifecycle-aware event organization and color coding
- Environment automation rules for optimal meeting/work conditions

#### QuickActionsPanel
Rapid access to common automation and lifecycle operations with system status monitoring.

**Features:**
- Categorized action organization (environment, security, productivity, lifecycle, system)
- System status overview with real-time indicators (focus mode, security, battery)
- Toggle and button action types with keyboard shortcuts
- Action execution with immediate feedback and status updates
- Expandable action details with automation trigger information
- Customizable action grid with category filtering

#### NotificationCenter
Centralized notification management for the entire MOSAIC system with intelligent filtering.

**Features:**
- Priority-based notification sorting with visual indicators
- Multiple filter options (all, unread, high priority, system notifications)
- Notification type categorization (info, success, warning, error, system)
- Interactive notification actions with contextual buttons
- Lifecycle-aware notification organization and color coding
- Notification statistics dashboard with unread and critical counts
- Expandable notification details with metadata and automation context

### Dashboard Layouts

#### DeveloperWorkspace
Specialized layout for software development workflow with APEX-LC integration.

**Features:**
- Multiple layout modes (overview, focus, debug) with dynamic widget visibility
- Quick action buttons for common development tasks (new branch, code review, deploy, debug)
- Integrated widgets: FlowStateMonitor, Code Metrics, AgentStatus, Environment Status, Issue Tracker
- Responsive grid system with Framer Motion animations
- APEX-LC lifecycle integration for development workflow management

#### FamilyDashboard
Family-oriented dashboard optimized for household management and home automation.

**Features:**
- Family member tracking with location status and device battery monitoring
- Home security monitoring with armed/disarmed status and door/window tracking
- Climate control with temperature, humidity, and energy usage monitoring
- Daily schedule management with family events and person assignments
- Smart device control with lights, automations, and offline device monitoring
- Weather integration with current conditions and forecast display
- Quick action buttons for family routines (good morning, leaving home, bedtime)

#### WellnessDashboard
Health and wellness focused dashboard layout with comprehensive biometric tracking.

**Features:**
- Quick health overview with heart rate, sleep, steps, and stress level monitoring
- Daily wellness goals with progress tracking and streak counting
- Sleep analysis with sleep score, deep sleep, REM sleep, and bedtime tracking
- Mindfulness integration with meditation streak and stress reduction monitoring
- Activity summary with steps, calories, active minutes, and floors climbed
- Achievement system with wellness milestones and progress indicators
- Biometric data integration with Apple Watch and other health devices

## Lifecycle Integration

Each component supports lifecycle-aware styling and behavior:

```tsx
// Lifecycle colors and styling
const lifecycles = [
  'APEX_LC',    // Development - Blue
  'PRISM_LC',   // Knowledge - Purple  
  'AURORA_LC',  // Customer Success - Green
  'NEXUS_LC',   // Integration - Orange
  'FLUX_LC',    // Data Flow - Cyan
  'SPARK_LC',   // Innovation - Yellow
  'SHIELD_LC',  // Security - Red
  'QUANTUM_LC', // AI/ML - Indigo
  'ECHO_LC',    // Communication - Pink
  'PULSE_LC',   // Monitoring - Emerald
  'FLOW_LC',    // Orchestration - Violet
];
```

## Home Assistant Integration

### Setup

1. Install the MOSAIC Home Assistant add-on
2. Configure the React component integration
3. Set up automation rules

```yaml
# configuration.yaml
mosaic:
  react_components:
    enabled: true
    port: 3000
    ssl: false
    cors_allowed_origins:
      - "http://localhost:3000"
      - "https://your-domain.com"
```

### Event Handling

```tsx
import { useHass } from '@hakit/core';

function MyComponent() {
  const { callService, useEntity } = useHass();
  
  const handleFlowStateChange = (state) => {
    callService({
      domain: 'mosaic',
      service: 'set_flow_state',
      serviceData: { state },
    });
  };
  
  const lightEntity = useEntity('light.office_main');
  
  return (
    <FlowStateMonitor
      data={flowData}
      onStateChange={handleFlowStateChange}
    />
  );
}
```

## Theming

### Custom Theme

```tsx
import { MosaicTheme } from '@mosaic/home-assistant-react';

const customTheme: MosaicTheme = {
  name: 'custom',
  colors: {
    primary: '#3b82f6',
    secondary: '#6366f1',
    // ... more colors
  },
  // ... more theme properties
};
```

### CSS Variables

```css
:root {
  --mosaic-primary: #3b82f6;
  --mosaic-secondary: #6366f1;
  --mosaic-accent: #f59e0b;
  --mosaic-background: #ffffff;
  --mosaic-surface: #f9fafb;
  --mosaic-text: #111827;
  --mosaic-border: #e5e7eb;
}

[data-theme="dark"] {
  --mosaic-background: #111827;
  --mosaic-surface: #1f2937;
  --mosaic-text: #f9fafb;
  --mosaic-border: #374151;
}
```

## Development

### Setup

```bash
git clone <repository>
cd mosaic/home-assistant/react-components
npm install
npm run dev
```

### Building

```bash
npm run build
npm run type-check
npm run lint
```

### Testing

```bash
npm run test
npm run test:coverage
npm run test:ui
```

### Storybook

```bash
npm run storybook
npm run build-storybook
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

- 📖 [Documentation](https://docs.alias.org/mosaic/home-assistant)
- 💬 [Discord Community](https://discord.gg/alias)
- 🐛 [Issue Tracker](https://github.com/alias-org/mosaic/issues)
- 📧 [Email Support](mailto:<EMAIL>)
