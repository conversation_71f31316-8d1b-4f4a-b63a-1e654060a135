{"name": "@mosaic/home-assistant-react", "version": "1.0.0", "description": "MOSAIC Home Assistant React Component Kit for work-life synthesis automation", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && vite build", "dev": "vite", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "type-check": "tsc --noEmit"}, "dependencies": {"@hakit/core": "^3.0.0", "@hakit/components": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "zustand": "^4.4.7", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-hook-form": "^7.48.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^4.4.0", "vitest": "^0.34.0", "@vitest/ui": "^0.34.0", "@vitest/coverage-v8": "^0.34.0", "@storybook/react": "^7.5.0", "@storybook/react-vite": "^7.5.0", "@storybook/addon-essentials": "^7.5.0", "@storybook/addon-interactions": "^7.5.0", "@storybook/addon-links": "^7.5.0", "@storybook/blocks": "^7.5.0", "@storybook/testing-library": "^0.2.0", "storybook": "^7.5.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "keywords": ["mosaic", "home-assistant", "react", "components", "smart-home", "automation", "work-life-synthesis"], "author": "ALIAS Organization", "license": "MIT", "files": ["dist", "README.md"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./styles": "./dist/styles.css"}}