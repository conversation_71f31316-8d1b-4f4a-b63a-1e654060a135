# MOSAIC Infrastructure Configuration
# Defines all infrastructure components and their settings

kafka:
  brokers:
    - "localhost:9092"
  client_id: "mosaic-client"
  connection_timeout: 3000
  request_timeout: 30000
  retry:
    initial_retry_time: 100
    retries: 8
    max_retry_time: 30000
  
  # Topic Configuration
  topics:
    # Lifecycle Communication Topics
    apex_events: "mosaic.apex.events"
    aurora_events: "mosaic.aurora.events"
    prism_events: "mosaic.prism.events"
    pulse_events: "mosaic.pulse.events"
    nexus_events: "mosaic.nexus.events"
    flux_events: "mosaic.flux.events"
    spark_events: "mosaic.spark.events"
    shield_events: "mosaic.shield.events"
    quantum_events: "mosaic.quantum.events"
    echo_events: "mosaic.echo.events"
    flow_events: "mosaic.flow.events"
    
    # System Topics
    system_events: "mosaic.system.events"
    health_checks: "mosaic.system.health"
    metrics: "mosaic.system.metrics"
    
    # Feature Management Topics (Bucket.co integration)
    feature_flags: "mosaic.features.flags"
    feature_feedback: "mosaic.features.feedback"
    feature_analytics: "mosaic.features.analytics"
    
    # Dead Letter Queues
    dlq_apex: "mosaic.dlq.apex"
    dlq_aurora: "mosaic.dlq.aurora"
    dlq_prism: "mosaic.dlq.prism"
    dlq_pulse: "mosaic.dlq.pulse"
    dlq_system: "mosaic.dlq.system"
    dlq_features: "mosaic.dlq.features"

  # Topic Settings
  topic_config:
    num_partitions: 3
    replication_factor: 1
    cleanup_policy: "delete"
    retention_ms: 604800000  # 7 days
    segment_ms: 86400000     # 1 day
    max_message_bytes: 1048576  # 1MB

  # Consumer Groups
  consumer_groups:
    apex_processors: "mosaic-apex-processors"
    aurora_processors: "mosaic-aurora-processors"
    prism_processors: "mosaic-prism-processors"
    pulse_processors: "mosaic-pulse-processors"
    system_monitors: "mosaic-system-monitors"
    feature_processors: "mosaic-feature-processors"

schema_registry:
  url: "http://localhost:8081"
  timeout: 5000
  compatibility: "BACKWARD"
  
  # Schema Subjects
  schemas:
    mosaic_event: "mosaic-event-value"
    apex_event: "apex-event-value"
    aurora_event: "aurora-event-value"
    prism_event: "prism-event-value"
    pulse_event: "pulse-event-value"
    system_event: "system-event-value"
    feature_event: "feature-event-value"

redis:
  host: "localhost"
  port: 6379
  db: 0
  password: null
  connection_timeout: 5000
  command_timeout: 5000
  retry_delay_on_failure: 100
  max_retry_delay: 2000
  
  # Key Prefixes
  prefixes:
    context: "mosaic:context:"
    cache: "mosaic:cache:"
    sessions: "mosaic:sessions:"
    locks: "mosaic:locks:"
    metrics: "mosaic:metrics:"

postgresql:
  host: "localhost"
  port: 5432
  database: "mosaic"
  username: "mosaic"
  password: "mosaic_dev_password"
  pool_size: 10
  connection_timeout: 5000
  idle_timeout: 30000
  
  # Schema Configuration
  schemas:
    events: "events"
    contexts: "contexts"
    metrics: "metrics"
    features: "features"

# Event Bus Configuration
event_bus:
  # Processing Configuration
  batch_size: 100
  batch_timeout: 1000
  max_concurrent_consumers: 5
  
  # Error Handling
  max_retries: 3
  retry_backoff: "exponential"
  dlq_enabled: true
  
  # Monitoring
  metrics_enabled: true
  health_check_interval: 30000
  
  # Event Routing
  routing:
    default_partition_strategy: "round_robin"
    lifecycle_partition_strategy: "by_source"
    system_partition_strategy: "by_priority"

# Monitoring and Observability
monitoring:
  prometheus:
    enabled: true
    port: 9090
    metrics_path: "/metrics"
  
  health_checks:
    enabled: true
    port: 8090
    path: "/health"
    interval: 30000
  
  logging:
    level: "info"
    format: "json"
    output: "stdout"

# Development Settings
development:
  auto_create_topics: true
  reset_consumer_groups: false
  log_events: true
  mock_external_services: false

# Production Settings
production:
  auto_create_topics: false
  ssl_enabled: true
  sasl_enabled: true
  monitoring_enabled: true
  backup_enabled: true
