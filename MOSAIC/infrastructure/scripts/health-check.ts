#!/usr/bin/env ts-node

/**
 * Health Check Script for MOSAIC Infrastructure
 * Verifies all infrastructure components are running correctly
 */

import { getKafkaConfig } from '../event-bus/kafka-config';

interface HealthCheckResult {
  component: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  message: string;
  timestamp: Date;
  details?: Record<string, any>;
}

class HealthChecker {
  private results: HealthCheckResult[] = [];

  async runAllChecks(): Promise<void> {
    console.log('🏥 Running MOSAIC Infrastructure Health Checks...\n');

    await this.checkKafka();
    await this.checkSchemaRegistry();
    await this.checkRedis();
    await this.checkPostgreSQL();
    await this.checkDocker();

    this.printResults();
  }

  private async checkKafka(): Promise<void> {
    console.log('🔍 Checking Kafka...');
    
    try {
      const config = getKafkaConfig();
      const isHealthy = await config.healthCheck();
      
      this.results.push({
        component: 'Kafka',
        status: isHealthy ? 'healthy' : 'unhealthy',
        message: isHealthy ? '<PERSON><PERSON><PERSON> is responding' : 'Ka<PERSON>ka is not responding',
        timestamp: new Date(),
        details: {
          brokers: config.getConfig().kafka.brokers,
          client_id: config.getConfig().kafka.client_id,
        },
      });

      console.log(`  ${isHealthy ? '✅' : '❌'} Kafka: ${isHealthy ? 'Healthy' : 'Unhealthy'}`);
    } catch (error) {
      this.results.push({
        component: 'Kafka',
        status: 'unhealthy',
        message: `Kafka check failed: ${error}`,
        timestamp: new Date(),
      });
      console.log(`  ❌ Kafka: Error - ${error}`);
    }
  }

  private async checkSchemaRegistry(): Promise<void> {
    console.log('🔍 Checking Schema Registry...');
    
    try {
      const config = getKafkaConfig();
      const schemaRegistryUrl = config.getConfig().schema_registry.url;
      
      // Simple HTTP check to schema registry
      const response = await fetch(`${schemaRegistryUrl}/subjects`, {
        method: 'GET',
        headers: { 'Accept': 'application/json' },
      });

      const isHealthy = response.ok;
      
      this.results.push({
        component: 'Schema Registry',
        status: isHealthy ? 'healthy' : 'unhealthy',
        message: isHealthy ? 'Schema Registry is responding' : `Schema Registry returned ${response.status}`,
        timestamp: new Date(),
        details: {
          url: schemaRegistryUrl,
          status_code: response.status,
        },
      });

      console.log(`  ${isHealthy ? '✅' : '❌'} Schema Registry: ${isHealthy ? 'Healthy' : 'Unhealthy'}`);
    } catch (error) {
      this.results.push({
        component: 'Schema Registry',
        status: 'unhealthy',
        message: `Schema Registry check failed: ${error}`,
        timestamp: new Date(),
      });
      console.log(`  ❌ Schema Registry: Error - ${error}`);
    }
  }

  private async checkRedis(): Promise<void> {
    console.log('🔍 Checking Redis...');
    
    try {
      // Try to import and connect to Redis
      const { createClient } = await import('redis');
      const client = createClient({
        url: 'redis://localhost:6379',
        socket: {
          connectTimeout: 5000,
        },
      });

      await client.connect();
      await client.ping();
      await client.disconnect();

      this.results.push({
        component: 'Redis',
        status: 'healthy',
        message: 'Redis is responding to ping',
        timestamp: new Date(),
        details: {
          url: 'redis://localhost:6379',
        },
      });

      console.log('  ✅ Redis: Healthy');
    } catch (error) {
      this.results.push({
        component: 'Redis',
        status: 'unhealthy',
        message: `Redis check failed: ${error}`,
        timestamp: new Date(),
      });
      console.log(`  ❌ Redis: Error - ${error}`);
    }
  }

  private async checkPostgreSQL(): Promise<void> {
    console.log('🔍 Checking PostgreSQL...');
    
    try {
      // Try to import and connect to PostgreSQL
      const { Client } = await import('pg');
      const client = new Client({
        host: 'localhost',
        port: 5432,
        database: 'mosaic',
        user: 'mosaic',
        password: 'mosaic_password',
        connectionTimeoutMillis: 5000,
      });

      await client.connect();
      const result = await client.query('SELECT NOW()');
      await client.end();

      this.results.push({
        component: 'PostgreSQL',
        status: 'healthy',
        message: 'PostgreSQL is responding to queries',
        timestamp: new Date(),
        details: {
          host: 'localhost:5432',
          database: 'mosaic',
          server_time: result.rows[0].now,
        },
      });

      console.log('  ✅ PostgreSQL: Healthy');
    } catch (error) {
      this.results.push({
        component: 'PostgreSQL',
        status: 'unhealthy',
        message: `PostgreSQL check failed: ${error}`,
        timestamp: new Date(),
      });
      console.log(`  ❌ PostgreSQL: Error - ${error}`);
    }
  }

  private async checkDocker(): Promise<void> {
    console.log('🔍 Checking Docker containers...');
    
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // Check if Docker is running
      await execAsync('docker version');

      // Check MOSAIC containers
      const { stdout } = await execAsync('docker ps --filter "name=mosaic" --format "table {{.Names}}\t{{.Status}}"');
      
      const containers = stdout.trim().split('\n').slice(1); // Remove header
      const runningContainers = containers.filter(line => line.includes('Up'));

      this.results.push({
        component: 'Docker',
        status: runningContainers.length > 0 ? 'healthy' : 'unhealthy',
        message: `${runningContainers.length} MOSAIC containers running`,
        timestamp: new Date(),
        details: {
          total_containers: containers.length,
          running_containers: runningContainers.length,
          containers: containers,
        },
      });

      console.log(`  ${runningContainers.length > 0 ? '✅' : '❌'} Docker: ${runningContainers.length} containers running`);
    } catch (error) {
      this.results.push({
        component: 'Docker',
        status: 'unhealthy',
        message: `Docker check failed: ${error}`,
        timestamp: new Date(),
      });
      console.log(`  ❌ Docker: Error - ${error}`);
    }
  }

  private printResults(): void {
    console.log('\n📋 Health Check Summary:');
    console.log('=' .repeat(60));

    const healthyCount = this.results.filter(r => r.status === 'healthy').length;
    const unhealthyCount = this.results.filter(r => r.status === 'unhealthy').length;
    const unknownCount = this.results.filter(r => r.status === 'unknown').length;

    console.log(`✅ Healthy: ${healthyCount}`);
    console.log(`❌ Unhealthy: ${unhealthyCount}`);
    console.log(`❓ Unknown: ${unknownCount}`);
    console.log('');

    for (const result of this.results) {
      const icon = result.status === 'healthy' ? '✅' : result.status === 'unhealthy' ? '❌' : '❓';
      console.log(`${icon} ${result.component}: ${result.message}`);
      
      if (result.details) {
        for (const [key, value] of Object.entries(result.details)) {
          console.log(`   ${key}: ${JSON.stringify(value)}`);
        }
      }
      console.log('');
    }

    // Overall status
    const overallStatus = unhealthyCount === 0 ? 'HEALTHY' : 'UNHEALTHY';
    const statusIcon = overallStatus === 'HEALTHY' ? '🟢' : '🔴';
    
    console.log('=' .repeat(60));
    console.log(`${statusIcon} Overall Status: ${overallStatus}`);
    
    if (overallStatus === 'UNHEALTHY') {
      console.log('\n🚨 Action Required:');
      console.log('Some components are unhealthy. Please check the following:');
      console.log('1. Ensure Docker containers are running: docker-compose up -d');
      console.log('2. Check network connectivity');
      console.log('3. Verify configuration files');
      console.log('4. Check logs for detailed error information');
    }
  }

  getResults(): HealthCheckResult[] {
    return this.results;
  }

  isOverallHealthy(): boolean {
    return this.results.every(result => result.status === 'healthy');
  }
}

// CLI execution
async function main() {
  const checker = new HealthChecker();
  await checker.runAllChecks();
  
  // Exit with appropriate code
  const isHealthy = checker.isOverallHealthy();
  process.exit(isHealthy ? 0 : 1);
}

if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Health check failed:', error);
    process.exit(1);
  });
}

export { HealthChecker, HealthCheckResult };
