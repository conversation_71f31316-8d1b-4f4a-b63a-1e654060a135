#!/usr/bin/env ts-node

/**
 * Test script for MOSAIC Infrastructure
 * Tests event bus, schema registry, and DLQ functionality
 */

import { getMosaicInfrastructure } from '../index';
import { EventBuilder } from '../../shared/events';
import { MosaicEvent, MosaicEventType } from '../../shared/types';

async function testInfrastructure() {
  console.log('🧪 Testing MOSAIC Infrastructure...\n');

  try {
    // Initialize infrastructure
    console.log('1. Initializing infrastructure...');
    const infrastructure = getMosaicInfrastructure();
    await infrastructure.initialize();
    console.log('✅ Infrastructure initialized\n');

    // Get components
    const eventBus = infrastructure.getEventBus();
    const schemaRegistry = infrastructure.getSchemaRegistry();
    const eventRouter = infrastructure.getEventRouter();

    // Test 1: Health Check
    console.log('2. Testing health check...');
    const healthStatus = await infrastructure.healthCheck();
    console.log(`Health Status:`, healthStatus);
    console.log('✅ Health check completed\n');

    // Test 2: Event Publishing
    console.log('3. Testing event publishing...');
    
    const testEvents: MosaicEvent[] = [
      EventBuilder.create('apex.feature.created')
        .source('apex-lc')
        .payload({ 
          feature_id: 'test-feature-1',
          name: 'Test Feature',
          description: 'A test feature for infrastructure validation'
        })
        .priority('medium')
        .build(),

      EventBuilder.create('aurora.customer.health_updated')
        .source('aurora-lc')
        .payload({
          customer_id: 'cust-123',
          health_score: 0.85,
          previous_score: 0.75,
          factors: ['engagement_up', 'support_tickets_down']
        })
        .priority('high')
        .build(),

      EventBuilder.create('prism.insight.generated')
        .source('prism-lc')
        .payload({
          insight: 'Customer satisfaction correlates with feature adoption rate',
          confidence: 0.92,
          sources: ['customer_feedback', 'usage_analytics'],
          tags: ['customer_success', 'product_insights']
        })
        .priority('medium')
        .build(),
    ];

    for (const event of testEvents) {
      await eventBus.publish(event);
      console.log(`  📤 Published: ${event.type}`);
    }
    console.log('✅ Event publishing completed\n');

    // Test 3: Event Subscription
    console.log('4. Testing event subscription...');
    
    let receivedEvents = 0;
    const eventHandler = async (event: MosaicEvent) => {
      receivedEvents++;
      console.log(`  📥 Received: ${event.type} from ${event.source}`);
    };

    const eventTypes: MosaicEventType[] = [
      'apex.feature.created',
      'aurora.customer.health_updated',
      'prism.insight.generated'
    ];

    await eventBus.subscribe(eventTypes, eventHandler, 'test-consumer-group');
    console.log(`  📋 Subscribed to: ${eventTypes.join(', ')}`);

    // Wait a bit for events to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log(`✅ Event subscription test completed (received ${receivedEvents} events)\n`);

    // Test 4: Event Routing
    console.log('5. Testing event routing...');
    
    const routingTestEvent = EventBuilder.create('aurora.customer.health_updated')
      .source('aurora-lc')
      .payload({
        customer_id: 'cust-456',
        health_score: 0.25, // Critical health score
        previous_score: 0.65,
        factors: ['support_tickets_up', 'engagement_down', 'churn_risk']
      })
      .priority('critical')
      .build();

    const routedTopics = await eventRouter.routeEvent(routingTestEvent);
    console.log(`  🔀 Event routed to topics: ${routedTopics.join(', ')}`);
    console.log('✅ Event routing completed\n');

    // Test 5: Schema Validation
    console.log('6. Testing schema validation...');
    
    const validEvent = EventBuilder.create('feature.flag.toggled')
      .source('system')
      .payload({
        feature_key: 'new_dashboard',
        user_id: 'user-789',
        company_id: 'comp-123',
        enabled: true,
        timestamp: new Date()
      })
      .priority('low')
      .build();

    const isValid = await schemaRegistry.validateEvent(validEvent);
    console.log(`  ✅ Schema validation result: ${isValid ? 'VALID' : 'INVALID'}`);
    console.log('✅ Schema validation completed\n');

    // Test 6: Metrics Collection
    console.log('7. Collecting metrics...');
    
    const metrics = infrastructure.getMetrics();
    console.log('  📊 Event Bus Metrics:', metrics.eventBus);
    console.log('  📊 Routing Metrics:', metrics.routing);
    
    const dlqStats = await metrics.dlq;
    console.log('  📊 DLQ Stats:', dlqStats);
    console.log('✅ Metrics collection completed\n');

    // Test 7: Error Handling
    console.log('8. Testing error handling...');
    
    try {
      // Create an invalid event to test error handling
      const invalidEvent = {
        id: 'invalid-event',
        type: 'invalid.event.type' as MosaicEventType,
        source: 'test' as any,
        timestamp: new Date(),
        payload: { invalid: true },
      } as MosaicEvent;

      await eventBus.publish(invalidEvent);
      console.log('  ⚠️  Invalid event was published (this might be expected)');
    } catch (error) {
      console.log(`  ✅ Error handling working: ${error}`);
    }
    console.log('✅ Error handling test completed\n');

    console.log('🎉 All infrastructure tests completed successfully!');
    
    // Final metrics
    console.log('\n📈 Final Metrics:');
    const finalMetrics = infrastructure.getMetrics();
    console.log('  Events Published:', finalMetrics.eventBus.events_published);
    console.log('  Events Consumed:', finalMetrics.eventBus.events_consumed);
    console.log('  Events Failed:', finalMetrics.eventBus.events_failed);
    console.log('  Rules Evaluated:', finalMetrics.routing.rules_evaluated);
    console.log('  Rules Matched:', finalMetrics.routing.rules_matched);

  } catch (error) {
    console.error('❌ Infrastructure test failed:', error);
    process.exit(1);
  }
}

async function cleanup() {
  console.log('\n🧹 Cleaning up...');
  try {
    const infrastructure = getMosaicInfrastructure();
    if (infrastructure.isReady()) {
      await infrastructure.shutdown();
      console.log('✅ Infrastructure shutdown completed');
    }
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  await cleanup();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  await cleanup();
  process.exit(0);
});

// Run the test
if (require.main === module) {
  testInfrastructure()
    .then(() => {
      console.log('\n✨ Test completed successfully');
      return cleanup();
    })
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      cleanup().finally(() => process.exit(1));
    });
}
