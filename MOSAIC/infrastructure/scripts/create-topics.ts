#!/usr/bin/env ts-node

/**
 * Create Kafka Topics Script for MOSAIC Infrastructure
 * Creates all required topics for MOSAIC lifecycles
 */

import { getKafkaConfig } from '../event-bus/kafka-config';

async function createTopics() {
  console.log('📝 Creating Kafka topics for MOSAIC...\n');

  try {
    const config = getKafkaConfig();
    
    console.log('🔗 Connecting to Kafka...');
    const kafka = config.getKafkaInstance();
    const admin = kafka.admin(config.getAdminConfig());
    
    await admin.connect();
    console.log('✅ Connected to Kafka\n');

    // Get existing topics
    console.log('🔍 Checking existing topics...');
    const existingTopics = await admin.listTopics();
    console.log(`Found ${existingTopics.length} existing topics`);

    // Define all MOSAIC topics
    const mosaicTopics = [
      // Lifecycle Event Topics
      { key: 'apex_events', name: config.getTopicName('apex_events') },
      { key: 'aurora_events', name: config.getTopicName('aurora_events') },
      { key: 'prism_events', name: config.getTopicName('prism_events') },
      { key: 'pulse_events', name: config.getTopicName('pulse_events') },
      { key: 'nexus_events', name: config.getTopicName('nexus_events') },
      { key: 'flux_events', name: config.getTopicName('flux_events') },
      { key: 'spark_events', name: config.getTopicName('spark_events') },
      { key: 'shield_events', name: config.getTopicName('shield_events') },
      { key: 'quantum_events', name: config.getTopicName('quantum_events') },
      { key: 'echo_events', name: config.getTopicName('echo_events') },
      { key: 'flow_events', name: config.getTopicName('flow_events') },
      
      // System Topics
      { key: 'system_events', name: config.getTopicName('system_events') },
      
      // Feature Management Topics
      { key: 'feature_flags', name: config.getTopicName('feature_flags') },
      { key: 'feature_feedback', name: config.getTopicName('feature_feedback') },
      { key: 'feature_analytics', name: config.getTopicName('feature_analytics') },
      
      // Dead Letter Queue Topics
      { key: 'dlq_apex', name: config.getTopicName('dlq_apex') },
      { key: 'dlq_aurora', name: config.getTopicName('dlq_aurora') },
      { key: 'dlq_prism', name: config.getTopicName('dlq_prism') },
      { key: 'dlq_pulse', name: config.getTopicName('dlq_pulse') },
      { key: 'dlq_nexus', name: config.getTopicName('dlq_nexus') },
      { key: 'dlq_flux', name: config.getTopicName('dlq_flux') },
      { key: 'dlq_spark', name: config.getTopicName('dlq_spark') },
      { key: 'dlq_shield', name: config.getTopicName('dlq_shield') },
      { key: 'dlq_quantum', name: config.getTopicName('dlq_quantum') },
      { key: 'dlq_echo', name: config.getTopicName('dlq_echo') },
      { key: 'dlq_flow', name: config.getTopicName('dlq_flow') },
      { key: 'dlq_system', name: config.getTopicName('dlq_system') },
      { key: 'dlq_features', name: config.getTopicName('dlq_features') },
    ];

    // Filter out topics that already exist
    const topicsToCreate = mosaicTopics.filter(topic => !existingTopics.includes(topic.name));
    
    if (topicsToCreate.length === 0) {
      console.log('✅ All MOSAIC topics already exist\n');
    } else {
      console.log(`📝 Creating ${topicsToCreate.length} new topics...\n`);

      // Get topic configuration
      const topicConfig = config.getConfig().kafka.topic_config;

      // Create topics
      const topicsToCreateConfig = topicsToCreate.map(topic => ({
        topic: topic.name,
        numPartitions: topicConfig.num_partitions,
        replicationFactor: topicConfig.replication_factor,
        configEntries: [
          { name: 'cleanup.policy', value: topicConfig.cleanup_policy },
          { name: 'retention.ms', value: topicConfig.retention_ms.toString() },
          { name: 'segment.ms', value: topicConfig.segment_ms.toString() },
          { name: 'max.message.bytes', value: topicConfig.max_message_bytes.toString() },
        ],
      }));

      await admin.createTopics({
        topics: topicsToCreateConfig,
        waitForLeaders: true,
        timeout: 30000,
      });

      console.log('✅ Topics created successfully!\n');

      // List created topics
      for (const topic of topicsToCreate) {
        console.log(`  📝 ${topic.key}: ${topic.name}`);
      }
    }

    // Verify all topics exist
    console.log('\n🔍 Verifying topic creation...');
    const updatedTopics = await admin.listTopics();
    const missingTopics = mosaicTopics.filter(topic => !updatedTopics.includes(topic.name));

    if (missingTopics.length === 0) {
      console.log('✅ All MOSAIC topics verified');
    } else {
      console.log('❌ Some topics are missing:');
      for (const topic of missingTopics) {
        console.log(`  ❌ ${topic.key}: ${topic.name}`);
      }
      throw new Error(`${missingTopics.length} topics are missing`);
    }

    // Get topic metadata
    console.log('\n📊 Topic Summary:');
    const metadata = await admin.fetchTopicMetadata({
      topics: mosaicTopics.map(t => t.name),
    });

    for (const [topicName, topicMetadata] of Object.entries(metadata.topics)) {
      const partitionCount = topicMetadata.partitions.length;
      const replicationFactor = topicMetadata.partitions[0]?.replicas.length || 0;
      console.log(`  📝 ${topicName}: ${partitionCount} partitions, RF=${replicationFactor}`);
    }

    await admin.disconnect();
    console.log('\n🎉 Topic creation completed successfully!');

  } catch (error) {
    console.error('❌ Failed to create topics:', error);
    process.exit(1);
  }
}

async function listTopics() {
  console.log('📋 Listing all Kafka topics...\n');

  try {
    const config = getKafkaConfig();
    const kafka = config.getKafkaInstance();
    const admin = kafka.admin(config.getAdminConfig());
    
    await admin.connect();
    
    const topics = await admin.listTopics();
    
    console.log(`Found ${topics.length} topics:`);
    for (const topic of topics.sort()) {
      console.log(`  📝 ${topic}`);
    }

    await admin.disconnect();
    console.log('\n✅ Topic listing completed');

  } catch (error) {
    console.error('❌ Failed to list topics:', error);
    process.exit(1);
  }
}

async function deleteTopics(topicNames: string[]) {
  console.log(`🗑️  Deleting ${topicNames.length} topics...\n`);

  try {
    const config = getKafkaConfig();
    const kafka = config.getKafkaInstance();
    const admin = kafka.admin(config.getAdminConfig());
    
    await admin.connect();
    
    await admin.deleteTopics({
      topics: topicNames,
      timeout: 30000,
    });

    console.log('✅ Topics deleted successfully:');
    for (const topic of topicNames) {
      console.log(`  🗑️  ${topic}`);
    }

    await admin.disconnect();
    console.log('\n✅ Topic deletion completed');

  } catch (error) {
    console.error('❌ Failed to delete topics:', error);
    process.exit(1);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'create':
      await createTopics();
      break;
    
    case 'list':
      await listTopics();
      break;
    
    case 'delete':
      const topicsToDelete = args.slice(1);
      if (topicsToDelete.length === 0) {
        console.error('❌ Please specify topics to delete');
        process.exit(1);
      }
      await deleteTopics(topicsToDelete);
      break;
    
    default:
      console.log('📝 MOSAIC Kafka Topic Management');
      console.log('');
      console.log('Usage:');
      console.log('  ts-node create-topics.ts create    # Create all MOSAIC topics');
      console.log('  ts-node create-topics.ts list      # List all topics');
      console.log('  ts-node create-topics.ts delete <topic1> <topic2>  # Delete specific topics');
      console.log('');
      console.log('Examples:');
      console.log('  ts-node create-topics.ts create');
      console.log('  ts-node create-topics.ts list');
      console.log('  ts-node create-topics.ts delete mosaic.apex.events mosaic.aurora.events');
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}
