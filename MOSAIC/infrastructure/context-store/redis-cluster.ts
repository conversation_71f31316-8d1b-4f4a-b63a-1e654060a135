/**
 * Redis Cluster Implementation for MOSAIC Context Store
 * Provides distributed caching and real-time data storage
 */

import { createClient, RedisClientType, RedisClusterType, createCluster } from 'redis';
import { ContextEntry, LifecycleId, MosaicEvent } from '../../shared/types';

export interface RedisConfig {
  cluster: {
    enabled: boolean;
    nodes: Array<{ host: string; port: number }>;
  };
  single: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
  options: {
    retry_attempts: number;
    retry_delay: number;
    connection_timeout: number;
    command_timeout: number;
  };
  keyPrefixes: {
    context: string;
    sessions: string;
    cache: string;
    events: string;
    metrics: string;
  };
}

export class MosaicRedisCluster {
  private client: RedisClientType | RedisClusterType;
  private config: RedisConfig;
  private isConnected: boolean = false;
  private metrics = {
    operations: 0,
    hits: 0,
    misses: 0,
    errors: 0,
    connections: 0,
  };

  constructor(config: RedisConfig) {
    this.config = config;
    this.client = this.createClient();
  }

  private createClient(): RedisClientType | RedisClusterType {
    if (this.config.cluster.enabled) {
      return createCluster({
        rootNodes: this.config.cluster.nodes.map(node => ({
          url: `redis://${node.host}:${node.port}`,
        })),
        defaults: {
          socket: {
            connectTimeout: this.config.options.connection_timeout,
            commandTimeout: this.config.options.command_timeout,
          },
        },
      });
    } else {
      return createClient({
        url: `redis://${this.config.single.host}:${this.config.single.port}`,
        password: this.config.single.password,
        database: this.config.single.db,
        socket: {
          connectTimeout: this.config.options.connection_timeout,
          commandTimeout: this.config.options.command_timeout,
        },
      });
    }
  }

  async connect(): Promise<void> {
    try {
      await this.client.connect();
      this.isConnected = true;
      this.metrics.connections++;
      console.log('✅ Redis cluster connected successfully');
    } catch (error) {
      this.metrics.errors++;
      console.error('❌ Failed to connect to Redis cluster:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.client.disconnect();
      this.isConnected = false;
      console.log('✅ Redis cluster disconnected');
    }
  }

  // Context Store Operations
  async setContext(lifecycleId: LifecycleId, key: string, value: any, ttl?: number): Promise<void> {
    const contextKey = `${this.config.keyPrefixes.context}:${lifecycleId}:${key}`;
    
    try {
      const serializedValue = JSON.stringify(value);
      
      if (ttl) {
        await this.client.setEx(contextKey, ttl, serializedValue);
      } else {
        await this.client.set(contextKey, serializedValue);
      }
      
      this.metrics.operations++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to set context: ${error}`);
    }
  }

  async getContext<T>(lifecycleId: LifecycleId, key: string): Promise<T | null> {
    const contextKey = `${this.config.keyPrefixes.context}:${lifecycleId}:${key}`;
    
    try {
      const value = await this.client.get(contextKey);
      this.metrics.operations++;
      
      if (value) {
        this.metrics.hits++;
        return JSON.parse(value) as T;
      } else {
        this.metrics.misses++;
        return null;
      }
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to get context: ${error}`);
    }
  }

  async deleteContext(lifecycleId: LifecycleId, key: string): Promise<boolean> {
    const contextKey = `${this.config.keyPrefixes.context}:${lifecycleId}:${key}`;
    
    try {
      const result = await this.client.del(contextKey);
      this.metrics.operations++;
      return result > 0;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to delete context: ${error}`);
    }
  }

  async listContextKeys(lifecycleId: LifecycleId, pattern?: string): Promise<string[]> {
    const searchPattern = pattern 
      ? `${this.config.keyPrefixes.context}:${lifecycleId}:${pattern}`
      : `${this.config.keyPrefixes.context}:${lifecycleId}:*`;
    
    try {
      const keys = await this.client.keys(searchPattern);
      this.metrics.operations++;
      
      // Remove prefix to return clean key names
      const prefix = `${this.config.keyPrefixes.context}:${lifecycleId}:`;
      return keys.map(key => key.replace(prefix, ''));
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to list context keys: ${error}`);
    }
  }

  // Session Management
  async createSession(sessionId: string, data: any, ttl: number = 3600): Promise<void> {
    const sessionKey = `${this.config.keyPrefixes.sessions}:${sessionId}`;
    
    try {
      await this.client.setEx(sessionKey, ttl, JSON.stringify(data));
      this.metrics.operations++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to create session: ${error}`);
    }
  }

  async getSession<T>(sessionId: string): Promise<T | null> {
    const sessionKey = `${this.config.keyPrefixes.sessions}:${sessionId}`;
    
    try {
      const value = await this.client.get(sessionKey);
      this.metrics.operations++;
      
      if (value) {
        this.metrics.hits++;
        return JSON.parse(value) as T;
      } else {
        this.metrics.misses++;
        return null;
      }
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to get session: ${error}`);
    }
  }

  async extendSession(sessionId: string, ttl: number): Promise<boolean> {
    const sessionKey = `${this.config.keyPrefixes.sessions}:${sessionId}`;
    
    try {
      const result = await this.client.expire(sessionKey, ttl);
      this.metrics.operations++;
      return result;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to extend session: ${error}`);
    }
  }

  // Cache Operations
  async cache(key: string, value: any, ttl: number = 300): Promise<void> {
    const cacheKey = `${this.config.keyPrefixes.cache}:${key}`;
    
    try {
      await this.client.setEx(cacheKey, ttl, JSON.stringify(value));
      this.metrics.operations++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to cache value: ${error}`);
    }
  }

  async getCached<T>(key: string): Promise<T | null> {
    const cacheKey = `${this.config.keyPrefixes.cache}:${key}`;
    
    try {
      const value = await this.client.get(cacheKey);
      this.metrics.operations++;
      
      if (value) {
        this.metrics.hits++;
        return JSON.parse(value) as T;
      } else {
        this.metrics.misses++;
        return null;
      }
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to get cached value: ${error}`);
    }
  }

  // Event Stream Operations
  async publishEvent(event: MosaicEvent): Promise<void> {
    const streamKey = `${this.config.keyPrefixes.events}:${event.source}`;
    
    try {
      await this.client.xAdd(streamKey, '*', {
        id: event.id,
        type: event.type,
        payload: JSON.stringify(event.payload),
        timestamp: event.timestamp.toISOString(),
      });
      this.metrics.operations++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to publish event: ${error}`);
    }
  }

  async subscribeToEvents(
    lifecycleId: LifecycleId,
    handler: (event: MosaicEvent) => void,
    fromId: string = '$'
  ): Promise<void> {
    const streamKey = `${this.config.keyPrefixes.events}:${lifecycleId}`;
    
    try {
      // Use Redis Streams for event subscription
      const stream = this.client.xRead(
        { key: streamKey, id: fromId },
        { BLOCK: 1000, COUNT: 10 }
      );
      
      // Process events (simplified for this implementation)
      // In production, this would be a continuous loop
      this.metrics.operations++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to subscribe to events: ${error}`);
    }
  }

  // Health Check
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      return false;
    }
  }

  // Metrics
  getMetrics() {
    return {
      ...this.metrics,
      hit_rate: this.metrics.hits / (this.metrics.hits + this.metrics.misses) || 0,
      error_rate: this.metrics.errors / this.metrics.operations || 0,
      is_connected: this.isConnected,
    };
  }

  // Utility Methods
  async flushAll(): Promise<void> {
    if (process.env.NODE_ENV !== 'production') {
      await this.client.flushAll();
      console.log('⚠️  Redis cluster flushed (development only)');
    }
  }

  async getInfo(): Promise<string> {
    try {
      return await this.client.info();
    } catch (error) {
      throw new Error(`Failed to get Redis info: ${error}`);
    }
  }
}
