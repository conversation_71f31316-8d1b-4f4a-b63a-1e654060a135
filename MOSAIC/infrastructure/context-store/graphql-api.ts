/**
 * GraphQL API for MOSAIC Context Store
 * Provides unified interface for context queries and mutations
 */

import { ApolloServer } from '@apollo/server';
import { startStandaloneServer } from '@apollo/server/standalone';
import { gql } from 'graphql-tag';
import { MosaicRedisCluster } from './redis-cluster';
import { MosaicPostgreSQLStore } from './postgresql-store';
import { LifecycleId, MosaicEvent } from '../../shared/types';

// GraphQL Schema Definition
const typeDefs = gql`
  scalar JSON
  scalar DateTime

  enum LifecycleId {
    APEX_LC
    PRISM_LC
    AURORA_LC
    NEXUS_LC
    FLUX_LC
    SPARK_LC
    SHIELD_LC
    QUANTUM_LC
    ECHO_LC
    PULSE_LC
    FLOW_LC
  }

  type ContextEntry {
    id: ID!
    lifecycleId: LifecycleId!
    key: String!
    value: JSON!
    metadata: JSON
    createdAt: DateTime!
    updatedAt: DateTime!
    expiresAt: DateTime
  }

  type EventRecord {
    id: ID!
    eventId: String!
    type: String!
    source: LifecycleId!
    target: LifecycleId
    payload: JSON!
    metadata: JSON
    createdAt: DateTime!
  }

  type SessionData {
    sessionId: String!
    data: JSON!
    createdAt: DateTime!
    expiresAt: DateTime!
  }

  type ContextMetrics {
    operations: Int!
    hits: Int!
    misses: Int!
    errors: Int!
    hitRate: Float!
    errorRate: Float!
    isConnected: Boolean!
  }

  type Query {
    # Context Queries
    getContext(lifecycleId: LifecycleId!, key: String!): JSON
    listContextKeys(lifecycleId: LifecycleId!, pattern: String): [String!]!
    getAllContext(lifecycleId: LifecycleId!): [ContextEntry!]!
    
    # Event Queries
    getEvents(
      lifecycleId: LifecycleId
      eventType: String
      limit: Int = 100
      offset: Int = 0
    ): [EventRecord!]!
    
    # Session Queries
    getSession(sessionId: String!): JSON
    
    # Metrics
    getMetrics: ContextMetrics!
    
    # Health Check
    healthCheck: Boolean!
  }

  type Mutation {
    # Context Mutations
    setContext(
      lifecycleId: LifecycleId!
      key: String!
      value: JSON!
      metadata: JSON
      ttl: Int
      expiresAt: DateTime
    ): Boolean!
    
    deleteContext(lifecycleId: LifecycleId!, key: String!): Boolean!
    
    # Event Mutations
    publishEvent(
      eventId: String!
      type: String!
      source: LifecycleId!
      target: LifecycleId
      payload: JSON!
      metadata: JSON
    ): Boolean!
    
    # Session Mutations
    createSession(
      sessionId: String!
      data: JSON!
      ttl: Int = 3600
    ): Boolean!
    
    deleteSession(sessionId: String!): Boolean!
    
    # Cache Mutations
    cache(key: String!, value: JSON!, ttl: Int = 300): Boolean!
    
    # Utility Mutations
    cleanup: Boolean!
  }

  type Subscription {
    # Real-time event subscriptions
    eventStream(lifecycleId: LifecycleId): EventRecord!
    
    # Context change subscriptions
    contextChanges(lifecycleId: LifecycleId!, key: String): ContextEntry!
  }
`;

// GraphQL Resolvers
export class ContextStoreResolvers {
  constructor(
    private redisCluster: MosaicRedisCluster,
    private postgresStore: MosaicPostgreSQLStore
  ) {}

  getResolvers() {
    return {
      Query: {
        // Context Queries
        getContext: async (_: any, { lifecycleId, key }: { lifecycleId: LifecycleId; key: string }) => {
          // Try Redis first (cache), then PostgreSQL (persistent)
          let value = await this.redisCluster.getContext(lifecycleId, key);
          if (!value) {
            value = await this.postgresStore.getContext(lifecycleId, key);
            // Cache in Redis if found in PostgreSQL
            if (value) {
              await this.redisCluster.setContext(lifecycleId, key, value, 300); // 5 min cache
            }
          }
          return value;
        },

        listContextKeys: async (_: any, { lifecycleId, pattern }: { lifecycleId: LifecycleId; pattern?: string }) => {
          return await this.postgresStore.listContextKeys(lifecycleId, pattern);
        },

        getAllContext: async (_: any, { lifecycleId }: { lifecycleId: LifecycleId }) => {
          // This would need to be implemented in PostgreSQL store
          return [];
        },

        // Event Queries
        getEvents: async (_: any, args: any) => {
          return await this.postgresStore.getEvents(
            args.lifecycleId,
            args.eventType,
            args.limit,
            args.offset
          );
        },

        // Session Queries
        getSession: async (_: any, { sessionId }: { sessionId: string }) => {
          // Try Redis first, then PostgreSQL
          let session = await this.redisCluster.getSession(sessionId);
          if (!session) {
            session = await this.postgresStore.getSession(sessionId);
          }
          return session;
        },

        // Metrics
        getMetrics: async () => {
          const redisMetrics = this.redisCluster.getMetrics();
          const pgMetrics = this.postgresStore.getMetrics();
          
          return {
            operations: redisMetrics.operations + pgMetrics.queries,
            hits: redisMetrics.hits,
            misses: redisMetrics.misses,
            errors: redisMetrics.errors + pgMetrics.errors,
            hitRate: redisMetrics.hit_rate,
            errorRate: redisMetrics.error_rate,
            isConnected: redisMetrics.is_connected && pgMetrics.is_connected,
          };
        },

        // Health Check
        healthCheck: async () => {
          const redisHealth = await this.redisCluster.healthCheck();
          const pgHealth = await this.postgresStore.healthCheck();
          return redisHealth && pgHealth;
        },
      },

      Mutation: {
        // Context Mutations
        setContext: async (_: any, args: any) => {
          const { lifecycleId, key, value, metadata = {}, ttl, expiresAt } = args;
          
          // Set in both Redis and PostgreSQL
          if (ttl) {
            await this.redisCluster.setContext(lifecycleId, key, value, ttl);
          }
          
          const expiry = expiresAt ? new Date(expiresAt) : (ttl ? new Date(Date.now() + ttl * 1000) : undefined);
          await this.postgresStore.setContext(lifecycleId, key, value, metadata, expiry);
          
          return true;
        },

        deleteContext: async (_: any, { lifecycleId, key }: { lifecycleId: LifecycleId; key: string }) => {
          // Delete from both stores
          await this.redisCluster.deleteContext(lifecycleId, key);
          return await this.postgresStore.deleteContext(lifecycleId, key);
        },

        // Event Mutations
        publishEvent: async (_: any, args: any) => {
          const event: MosaicEvent = {
            id: args.eventId,
            type: args.type,
            source: args.source,
            target: args.target,
            payload: args.payload,
            metadata: args.metadata || {},
            timestamp: new Date(),
          };

          // Publish to Redis streams and store in PostgreSQL
          await this.redisCluster.publishEvent(event);
          await this.postgresStore.storeEvent(event);
          
          return true;
        },

        // Session Mutations
        createSession: async (_: any, { sessionId, data, ttl }: { sessionId: string; data: any; ttl: number }) => {
          const expiresAt = new Date(Date.now() + ttl * 1000);
          
          // Create in both stores
          await this.redisCluster.createSession(sessionId, data, ttl);
          await this.postgresStore.createSession(sessionId, data, expiresAt);
          
          return true;
        },

        deleteSession: async (_: any, { sessionId }: { sessionId: string }) => {
          // Delete from PostgreSQL (Redis will expire automatically)
          return await this.postgresStore.deleteSession(sessionId);
        },

        // Cache Mutations
        cache: async (_: any, { key, value, ttl }: { key: string; value: any; ttl: number }) => {
          await this.redisCluster.cache(key, value, ttl);
          return true;
        },

        // Utility Mutations
        cleanup: async () => {
          await this.postgresStore.cleanup();
          return true;
        },
      },

      // Subscriptions would be implemented with Redis pub/sub or GraphQL subscriptions
      Subscription: {
        eventStream: {
          // Implementation would use Redis streams or WebSocket connections
          subscribe: () => {
            // Placeholder for subscription implementation
            return {};
          },
        },

        contextChanges: {
          // Implementation would use Redis keyspace notifications
          subscribe: () => {
            // Placeholder for subscription implementation
            return {};
          },
        },
      },
    };
  }
}

// GraphQL Server Setup
export class ContextStoreGraphQLServer {
  private server: ApolloServer;
  private resolvers: ContextStoreResolvers;

  constructor(
    redisCluster: MosaicRedisCluster,
    postgresStore: MosaicPostgreSQLStore,
    port: number = 4000
  ) {
    this.resolvers = new ContextStoreResolvers(redisCluster, postgresStore);
    
    this.server = new ApolloServer({
      typeDefs,
      resolvers: this.resolvers.getResolvers(),
      introspection: process.env.NODE_ENV !== 'production',
      plugins: [
        // Add logging and metrics plugins
        {
          requestDidStart() {
            return {
              didResolveOperation(requestContext) {
                console.log(`GraphQL Operation: ${requestContext.request.operationName}`);
              },
              didEncounterErrors(requestContext) {
                console.error('GraphQL Errors:', requestContext.errors);
              },
            };
          },
        },
      ],
    });
  }

  async start(port: number = 4000): Promise<{ url: string }> {
    const { url } = await startStandaloneServer(this.server, {
      listen: { port },
      context: async ({ req }) => {
        // Add authentication and context here
        return {
          // user: await getUserFromToken(req.headers.authorization),
        };
      },
    });

    console.log(`🚀 GraphQL Context Store API ready at ${url}`);
    return { url };
  }

  async stop(): Promise<void> {
    await this.server.stop();
    console.log('✅ GraphQL Context Store API stopped');
  }
}
