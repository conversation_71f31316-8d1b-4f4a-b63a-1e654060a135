/**
 * MOSAIC Context Store - Main Implementation
 * Integrates Redis cluster, PostgreSQL, and GraphQL API
 */

import { MosaicRedisCluster, RedisConfig } from './redis-cluster';
import { MosaicPostgreSQLStore, PostgreSQLConfig } from './postgresql-store';
import { ContextStoreGraphQLServer } from './graphql-api';
import { LifecycleId, ContextEntry, SharedContext } from '../../shared/types';

export interface ContextStoreConfig {
  redis: RedisConfig;
  postgresql: PostgreSQLConfig;
  graphql: {
    enabled: boolean;
    port: number;
  };
  features: {
    caching: boolean;
    persistence: boolean;
    realtime: boolean;
    cleanup_interval: number;
  };
}

export class MosaicContextStore implements SharedContext {
  private redis: MosaicRedisCluster;
  private postgres: MosaicPostgreSQLStore;
  private graphqlServer?: ContextStoreGraphQLServer;
  private config: ContextStoreConfig;
  private cleanupInterval?: NodeJS.Timeout;
  private isInitialized: boolean = false;

  constructor(config: ContextStoreConfig) {
    this.config = config;
    this.redis = new MosaicRedisCluster(config.redis);
    this.postgres = new MosaicPostgreSQLStore(config.postgresql);

    if (config.graphql.enabled) {
      this.graphqlServer = new ContextStoreGraphQLServer(
        this.redis,
        this.postgres,
        config.graphql.port
      );
    }
  }

  async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing MOSAIC Context Store...');

      // Initialize Redis cluster
      if (this.config.features.caching) {
        await this.redis.connect();
        console.log('✅ Redis cluster initialized');
      }

      // Initialize PostgreSQL store
      if (this.config.features.persistence) {
        await this.postgres.initialize();
        console.log('✅ PostgreSQL store initialized');
      }

      // Start GraphQL server
      if (this.graphqlServer) {
        await this.graphqlServer.start(this.config.graphql.port);
        console.log('✅ GraphQL API server started');
      }

      // Setup cleanup interval
      if (this.config.features.cleanup_interval > 0) {
        this.cleanupInterval = setInterval(
          () => this.cleanup(),
          this.config.features.cleanup_interval * 1000
        );
        console.log('✅ Cleanup interval configured');
      }

      this.isInitialized = true;
      console.log('🎉 MOSAIC Context Store fully initialized');

    } catch (error) {
      console.error('❌ Failed to initialize Context Store:', error);
      throw error;
    }
  }

  // SharedContext Interface Implementation
  async get<T>(lifecycleId: LifecycleId, key: string): Promise<T | undefined> {
    if (!this.isInitialized) {
      throw new Error('Context Store not initialized');
    }

    try {
      // Try Redis cache first if caching is enabled
      if (this.config.features.caching) {
        const cached = await this.redis.getContext<T>(lifecycleId, key);
        if (cached !== null) {
          return cached;
        }
      }

      // Fallback to PostgreSQL if persistence is enabled
      if (this.config.features.persistence) {
        const persistent = await this.postgres.getContext<T>(lifecycleId, key);
        
        // Cache the result in Redis if found and caching is enabled
        if (persistent !== null && this.config.features.caching) {
          await this.redis.setContext(lifecycleId, key, persistent, 300); // 5 min cache
        }
        
        return persistent || undefined;
      }

      return undefined;
    } catch (error) {
      console.error(`Failed to get context ${lifecycleId}:${key}:`, error);
      throw error;
    }
  }

  async set<T>(
    lifecycleId: LifecycleId, 
    key: string, 
    value: T, 
    options?: { ttl?: number; metadata?: Record<string, any> }
  ): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Context Store not initialized');
    }

    try {
      const { ttl, metadata = {} } = options || {};

      // Set in Redis cache if caching is enabled
      if (this.config.features.caching) {
        await this.redis.setContext(lifecycleId, key, value, ttl);
      }

      // Set in PostgreSQL if persistence is enabled
      if (this.config.features.persistence) {
        const expiresAt = ttl ? new Date(Date.now() + ttl * 1000) : undefined;
        await this.postgres.setContext(lifecycleId, key, value, metadata, expiresAt);
      }

    } catch (error) {
      console.error(`Failed to set context ${lifecycleId}:${key}:`, error);
      throw error;
    }
  }

  async delete(lifecycleId: LifecycleId, key: string): Promise<boolean> {
    if (!this.isInitialized) {
      throw new Error('Context Store not initialized');
    }

    try {
      let deleted = false;

      // Delete from Redis cache
      if (this.config.features.caching) {
        await this.redis.deleteContext(lifecycleId, key);
        deleted = true;
      }

      // Delete from PostgreSQL
      if (this.config.features.persistence) {
        const pgDeleted = await this.postgres.deleteContext(lifecycleId, key);
        deleted = deleted || pgDeleted;
      }

      return deleted;
    } catch (error) {
      console.error(`Failed to delete context ${lifecycleId}:${key}:`, error);
      throw error;
    }
  }

  async has(lifecycleId: LifecycleId, key: string): Promise<boolean> {
    const value = await this.get(lifecycleId, key);
    return value !== undefined;
  }

  async keys(lifecycleId: LifecycleId, pattern?: string): Promise<string[]> {
    if (!this.isInitialized) {
      throw new Error('Context Store not initialized');
    }

    try {
      // Use PostgreSQL for key listing (more reliable than Redis for this)
      if (this.config.features.persistence) {
        return await this.postgres.listContextKeys(lifecycleId, pattern);
      }

      // Fallback to Redis if only caching is enabled
      if (this.config.features.caching) {
        return await this.redis.listContextKeys(lifecycleId, pattern);
      }

      return [];
    } catch (error) {
      console.error(`Failed to list keys for ${lifecycleId}:`, error);
      throw error;
    }
  }

  async clear(lifecycleId: LifecycleId): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Context Store not initialized');
    }

    try {
      const keys = await this.keys(lifecycleId);
      
      // Delete all keys for the lifecycle
      await Promise.all(
        keys.map(key => this.delete(lifecycleId, key))
      );

      console.log(`✅ Cleared ${keys.length} keys for lifecycle ${lifecycleId}`);
    } catch (error) {
      console.error(`Failed to clear context for ${lifecycleId}:`, error);
      throw error;
    }
  }

  // Extended Context Store Methods
  async getAll(lifecycleId: LifecycleId): Promise<Record<string, any>> {
    const keys = await this.keys(lifecycleId);
    const entries: Record<string, any> = {};

    await Promise.all(
      keys.map(async (key) => {
        const value = await this.get(lifecycleId, key);
        if (value !== undefined) {
          entries[key] = value;
        }
      })
    );

    return entries;
  }

  async setMultiple(
    lifecycleId: LifecycleId, 
    entries: Record<string, any>,
    options?: { ttl?: number; metadata?: Record<string, any> }
  ): Promise<void> {
    await Promise.all(
      Object.entries(entries).map(([key, value]) =>
        this.set(lifecycleId, key, value, options)
      )
    );
  }

  // Session Management
  async createSession(sessionId: string, data: any, ttl: number = 3600): Promise<void> {
    if (this.config.features.caching) {
      await this.redis.createSession(sessionId, data, ttl);
    }

    if (this.config.features.persistence) {
      const expiresAt = new Date(Date.now() + ttl * 1000);
      await this.postgres.createSession(sessionId, data, expiresAt);
    }
  }

  async getSession<T>(sessionId: string): Promise<T | null> {
    // Try Redis first
    if (this.config.features.caching) {
      const session = await this.redis.getSession<T>(sessionId);
      if (session) return session;
    }

    // Fallback to PostgreSQL
    if (this.config.features.persistence) {
      return await this.postgres.getSession<T>(sessionId);
    }

    return null;
  }

  // Cache Management
  async cache(key: string, value: any, ttl: number = 300): Promise<void> {
    if (this.config.features.caching) {
      await this.redis.cache(key, value, ttl);
    }
  }

  async getCached<T>(key: string): Promise<T | null> {
    if (this.config.features.caching) {
      return await this.redis.getCached<T>(key);
    }
    return null;
  }

  // Health and Metrics
  async healthCheck(): Promise<boolean> {
    try {
      const checks = await Promise.all([
        this.config.features.caching ? this.redis.healthCheck() : Promise.resolve(true),
        this.config.features.persistence ? this.postgres.healthCheck() : Promise.resolve(true),
      ]);

      return checks.every(check => check);
    } catch (error) {
      return false;
    }
  }

  getMetrics() {
    const redisMetrics = this.config.features.caching ? this.redis.getMetrics() : null;
    const pgMetrics = this.config.features.persistence ? this.postgres.getMetrics() : null;

    return {
      redis: redisMetrics,
      postgresql: pgMetrics,
      features: this.config.features,
      initialized: this.isInitialized,
    };
  }

  // Cleanup
  private async cleanup(): Promise<void> {
    try {
      if (this.config.features.persistence) {
        await this.postgres.cleanup();
      }
      console.log('✅ Context Store cleanup completed');
    } catch (error) {
      console.error('❌ Context Store cleanup failed:', error);
    }
  }

  // Shutdown
  async shutdown(): Promise<void> {
    try {
      console.log('🛑 Shutting down MOSAIC Context Store...');

      // Clear cleanup interval
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      // Stop GraphQL server
      if (this.graphqlServer) {
        await this.graphqlServer.stop();
      }

      // Disconnect from stores
      if (this.config.features.caching) {
        await this.redis.disconnect();
      }

      if (this.config.features.persistence) {
        await this.postgres.close();
      }

      this.isInitialized = false;
      console.log('✅ MOSAIC Context Store shutdown complete');

    } catch (error) {
      console.error('❌ Error during Context Store shutdown:', error);
      throw error;
    }
  }
}

// Factory function for creating context store with default configuration
export function createContextStore(overrides: Partial<ContextStoreConfig> = {}): MosaicContextStore {
  const defaultConfig: ContextStoreConfig = {
    redis: {
      cluster: {
        enabled: false,
        nodes: [{ host: 'localhost', port: 6379 }],
      },
      single: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
      },
      options: {
        retry_attempts: 3,
        retry_delay: 1000,
        connection_timeout: 5000,
        command_timeout: 3000,
      },
      keyPrefixes: {
        context: 'mosaic:context',
        sessions: 'mosaic:sessions',
        cache: 'mosaic:cache',
        events: 'mosaic:events',
        metrics: 'mosaic:metrics',
      },
    },
    postgresql: {
      connection: {
        host: process.env.POSTGRES_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        database: process.env.POSTGRES_DB || 'mosaic',
        user: process.env.POSTGRES_USER || 'mosaic',
        password: process.env.POSTGRES_PASSWORD || 'mosaic',
        ssl: process.env.POSTGRES_SSL === 'true',
      },
      pool: {
        min: 2,
        max: 10,
        idle_timeout: 30000,
        connection_timeout: 5000,
      },
      schema: {
        context_table: 'mosaic_context',
        events_table: 'mosaic_events',
        sessions_table: 'mosaic_sessions',
        metrics_table: 'mosaic_metrics',
      },
    },
    graphql: {
      enabled: process.env.GRAPHQL_ENABLED !== 'false',
      port: parseInt(process.env.GRAPHQL_PORT || '4000'),
    },
    features: {
      caching: process.env.REDIS_ENABLED !== 'false',
      persistence: process.env.POSTGRES_ENABLED !== 'false',
      realtime: process.env.REALTIME_ENABLED !== 'false',
      cleanup_interval: parseInt(process.env.CLEANUP_INTERVAL || '3600'), // 1 hour
    },
  };

  const config = { ...defaultConfig, ...overrides };
  return new MosaicContextStore(config);
}

export * from './redis-cluster';
export * from './postgresql-store';
export * from './graphql-api';
