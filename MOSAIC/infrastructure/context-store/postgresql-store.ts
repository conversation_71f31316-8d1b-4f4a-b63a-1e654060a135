/**
 * PostgreSQL Store Implementation for MOSAIC Context Store
 * Provides persistent storage for context data and lifecycle state
 */

import { Pool, PoolClient, QueryResult } from 'pg';
import { ContextEntry, LifecycleId, MosaicEvent } from '../../shared/types';

export interface PostgreSQLConfig {
  connection: {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl?: boolean;
  };
  pool: {
    min: number;
    max: number;
    idle_timeout: number;
    connection_timeout: number;
  };
  schema: {
    context_table: string;
    events_table: string;
    sessions_table: string;
    metrics_table: string;
  };
}

export interface ContextRecord {
  id: string;
  lifecycle_id: LifecycleId;
  key: string;
  value: any;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
  expires_at?: Date;
}

export interface EventRecord {
  id: string;
  event_id: string;
  type: string;
  source: LifecycleId;
  target?: LifecycleId;
  payload: any;
  metadata: any;
  created_at: Date;
}

export class MosaicPostgreSQLStore {
  private pool: Pool;
  private config: PostgreSQLConfig;
  private isConnected: boolean = false;
  private metrics = {
    queries: 0,
    inserts: 0,
    updates: 0,
    deletes: 0,
    errors: 0,
    connections: 0,
  };

  constructor(config: PostgreSQLConfig) {
    this.config = config;
    this.pool = new Pool({
      host: config.connection.host,
      port: config.connection.port,
      database: config.connection.database,
      user: config.connection.user,
      password: config.connection.password,
      ssl: config.connection.ssl,
      min: config.pool.min,
      max: config.pool.max,
      idleTimeoutMillis: config.pool.idle_timeout,
      connectionTimeoutMillis: config.pool.connection_timeout,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.pool.on('connect', () => {
      this.metrics.connections++;
      this.isConnected = true;
    });

    this.pool.on('error', (err) => {
      this.metrics.errors++;
      console.error('PostgreSQL pool error:', err);
    });
  }

  async initialize(): Promise<void> {
    try {
      await this.createTables();
      await this.createIndexes();
      console.log('✅ PostgreSQL store initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize PostgreSQL store:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      // Context table
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${this.config.schema.context_table} (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          lifecycle_id VARCHAR(50) NOT NULL,
          key VARCHAR(255) NOT NULL,
          value JSONB NOT NULL,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          expires_at TIMESTAMP WITH TIME ZONE,
          UNIQUE(lifecycle_id, key)
        )
      `);

      // Events table
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${this.config.schema.events_table} (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          event_id VARCHAR(255) UNIQUE NOT NULL,
          type VARCHAR(100) NOT NULL,
          source VARCHAR(50) NOT NULL,
          target VARCHAR(50),
          payload JSONB NOT NULL,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `);

      // Sessions table
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${this.config.schema.sessions_table} (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          session_id VARCHAR(255) UNIQUE NOT NULL,
          data JSONB NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL
        )
      `);

      // Metrics table
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${this.config.schema.metrics_table} (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          metric_name VARCHAR(100) NOT NULL,
          metric_value NUMERIC NOT NULL,
          labels JSONB DEFAULT '{}',
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `);

    } finally {
      client.release();
    }
  }

  private async createIndexes(): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      // Context table indexes
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_context_lifecycle_id 
        ON ${this.config.schema.context_table} (lifecycle_id)
      `);
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_context_key 
        ON ${this.config.schema.context_table} (key)
      `);
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_context_expires_at 
        ON ${this.config.schema.context_table} (expires_at)
      `);

      // Events table indexes
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_events_type 
        ON ${this.config.schema.events_table} (type)
      `);
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_events_source 
        ON ${this.config.schema.events_table} (source)
      `);
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_events_created_at 
        ON ${this.config.schema.events_table} (created_at)
      `);

      // Sessions table indexes
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_sessions_expires_at 
        ON ${this.config.schema.sessions_table} (expires_at)
      `);

      // Metrics table indexes
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_metrics_name_timestamp 
        ON ${this.config.schema.metrics_table} (metric_name, timestamp)
      `);

    } finally {
      client.release();
    }
  }

  // Context Operations
  async setContext(
    lifecycleId: LifecycleId, 
    key: string, 
    value: any, 
    metadata: Record<string, any> = {},
    expiresAt?: Date
  ): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.query(`
        INSERT INTO ${this.config.schema.context_table} 
        (lifecycle_id, key, value, metadata, expires_at)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (lifecycle_id, key) 
        DO UPDATE SET 
          value = EXCLUDED.value,
          metadata = EXCLUDED.metadata,
          updated_at = NOW(),
          expires_at = EXCLUDED.expires_at
      `, [lifecycleId, key, JSON.stringify(value), JSON.stringify(metadata), expiresAt]);
      
      this.metrics.inserts++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to set context: ${error}`);
    } finally {
      client.release();
    }
  }

  async getContext<T>(lifecycleId: LifecycleId, key: string): Promise<T | null> {
    const client = await this.pool.connect();
    
    try {
      const result = await client.query(`
        SELECT value, metadata, created_at, updated_at, expires_at
        FROM ${this.config.schema.context_table}
        WHERE lifecycle_id = $1 AND key = $2
        AND (expires_at IS NULL OR expires_at > NOW())
      `, [lifecycleId, key]);
      
      this.metrics.queries++;
      
      if (result.rows.length > 0) {
        return JSON.parse(result.rows[0].value) as T;
      }
      
      return null;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to get context: ${error}`);
    } finally {
      client.release();
    }
  }

  async deleteContext(lifecycleId: LifecycleId, key: string): Promise<boolean> {
    const client = await this.pool.connect();
    
    try {
      const result = await client.query(`
        DELETE FROM ${this.config.schema.context_table}
        WHERE lifecycle_id = $1 AND key = $2
      `, [lifecycleId, key]);
      
      this.metrics.deletes++;
      return result.rowCount > 0;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to delete context: ${error}`);
    } finally {
      client.release();
    }
  }

  async listContextKeys(lifecycleId: LifecycleId, pattern?: string): Promise<string[]> {
    const client = await this.pool.connect();
    
    try {
      let query = `
        SELECT key FROM ${this.config.schema.context_table}
        WHERE lifecycle_id = $1
        AND (expires_at IS NULL OR expires_at > NOW())
      `;
      
      const params: any[] = [lifecycleId];
      
      if (pattern) {
        query += ' AND key LIKE $2';
        params.push(`%${pattern}%`);
      }
      
      query += ' ORDER BY key';
      
      const result = await client.query(query, params);
      this.metrics.queries++;
      
      return result.rows.map(row => row.key);
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to list context keys: ${error}`);
    } finally {
      client.release();
    }
  }

  // Event Storage
  async storeEvent(event: MosaicEvent): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.query(`
        INSERT INTO ${this.config.schema.events_table}
        (event_id, type, source, target, payload, metadata)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        event.id,
        event.type,
        event.source,
        event.target || null,
        JSON.stringify(event.payload),
        JSON.stringify(event.metadata || {})
      ]);
      
      this.metrics.inserts++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to store event: ${error}`);
    } finally {
      client.release();
    }
  }

  async getEvents(
    lifecycleId?: LifecycleId,
    eventType?: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<EventRecord[]> {
    const client = await this.pool.connect();
    
    try {
      let query = `
        SELECT * FROM ${this.config.schema.events_table}
        WHERE 1=1
      `;
      
      const params: any[] = [];
      let paramIndex = 1;
      
      if (lifecycleId) {
        query += ` AND source = $${paramIndex}`;
        params.push(lifecycleId);
        paramIndex++;
      }
      
      if (eventType) {
        query += ` AND type = $${paramIndex}`;
        params.push(eventType);
        paramIndex++;
      }
      
      query += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit, offset);
      
      const result = await client.query(query, params);
      this.metrics.queries++;
      
      return result.rows.map(row => ({
        ...row,
        payload: JSON.parse(row.payload),
        metadata: JSON.parse(row.metadata),
      }));
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to get events: ${error}`);
    } finally {
      client.release();
    }
  }

  // Session Management
  async createSession(sessionId: string, data: any, expiresAt: Date): Promise<void> {
    const client = await this.pool.connect();

    try {
      await client.query(`
        INSERT INTO ${this.config.schema.sessions_table}
        (session_id, data, expires_at)
        VALUES ($1, $2, $3)
        ON CONFLICT (session_id)
        DO UPDATE SET data = EXCLUDED.data, expires_at = EXCLUDED.expires_at
      `, [sessionId, JSON.stringify(data), expiresAt]);

      this.metrics.inserts++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to create session: ${error}`);
    } finally {
      client.release();
    }
  }

  async getSession<T>(sessionId: string): Promise<T | null> {
    const client = await this.pool.connect();

    try {
      const result = await client.query(`
        SELECT data FROM ${this.config.schema.sessions_table}
        WHERE session_id = $1 AND expires_at > NOW()
      `, [sessionId]);

      this.metrics.queries++;

      if (result.rows.length > 0) {
        return JSON.parse(result.rows[0].data) as T;
      }

      return null;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to get session: ${error}`);
    } finally {
      client.release();
    }
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    const client = await this.pool.connect();

    try {
      const result = await client.query(`
        DELETE FROM ${this.config.schema.sessions_table}
        WHERE session_id = $1
      `, [sessionId]);

      this.metrics.deletes++;
      return result.rowCount > 0;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to delete session: ${error}`);
    } finally {
      client.release();
    }
  }

  // Metrics Storage
  async storeMetric(name: string, value: number, labels: Record<string, any> = {}): Promise<void> {
    const client = await this.pool.connect();

    try {
      await client.query(`
        INSERT INTO ${this.config.schema.metrics_table}
        (metric_name, metric_value, labels)
        VALUES ($1, $2, $3)
      `, [name, value, JSON.stringify(labels)]);

      this.metrics.inserts++;
    } catch (error) {
      this.metrics.errors++;
      throw new Error(`Failed to store metric: ${error}`);
    } finally {
      client.release();
    }
  }

  // Health Check
  async healthCheck(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Cleanup expired records
  async cleanup(): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      // Clean expired context entries
      await client.query(`
        DELETE FROM ${this.config.schema.context_table}
        WHERE expires_at IS NOT NULL AND expires_at < NOW()
      `);
      
      // Clean expired sessions
      await client.query(`
        DELETE FROM ${this.config.schema.sessions_table}
        WHERE expires_at < NOW()
      `);
      
      console.log('✅ PostgreSQL cleanup completed');
    } catch (error) {
      console.error('❌ PostgreSQL cleanup failed:', error);
    } finally {
      client.release();
    }
  }

  // Metrics
  getMetrics() {
    return {
      ...this.metrics,
      pool_total: this.pool.totalCount,
      pool_idle: this.pool.idleCount,
      pool_waiting: this.pool.waitingCount,
      is_connected: this.isConnected,
    };
  }

  async close(): Promise<void> {
    await this.pool.end();
    this.isConnected = false;
    console.log('✅ PostgreSQL store closed');
  }
}
