{"name": "@mosaic/context-store", "version": "1.0.0", "description": "MOSAIC Context Store - Redis cluster, PostgreSQL, and GraphQL API for shared context management", "main": "index.js", "types": "index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "start": "node index.js", "start:dev": "ts-node-dev --respawn --transpile-only index.ts"}, "dependencies": {"@apollo/server": "^4.10.0", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "redis": "^4.6.12", "pg": "^8.11.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.0"}, "keywords": ["mosaic", "context-store", "redis", "postgresql", "graphql", "cache", "persistence"], "author": "ALIAS Organization", "license": "MIT"}