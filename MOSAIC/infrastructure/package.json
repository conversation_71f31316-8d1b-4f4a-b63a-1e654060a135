{"name": "@mosaic/infrastructure", "version": "1.0.0", "description": "MOSAIC Infrastructure Components - Event Bus, Schema Registry, and Dead Letter Queue", "main": "index.ts", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "docker:up": "docker-compose -f docker/docker-compose.yml up -d", "docker:down": "docker-compose -f docker/docker-compose.yml down", "docker:logs": "docker-compose -f docker/docker-compose.yml logs -f", "kafka:topics": "ts-node scripts/create-topics.ts", "kafka:health": "ts-node scripts/health-check.ts"}, "dependencies": {"kafkajs": "^2.2.4", "@kafkajs/confluent-schema-registry": "^3.3.0", "js-yaml": "^4.1.0", "redis": "^4.6.13", "pg": "^8.11.3", "ioredis": "^5.3.2"}, "devDependencies": {"@types/node": "^20.11.17", "@types/js-yaml": "^4.0.9", "@types/pg": "^8.10.9", "typescript": "^5.3.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.1.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0"}, "keywords": ["mosaic", "event-bus", "kafka", "schema-registry", "dead-letter-queue", "microservices", "event-driven"], "author": "ALIAS Organization", "license": "MIT", "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/alias-org/mosaic"}}