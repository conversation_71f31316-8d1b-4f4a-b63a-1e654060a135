/**
 * MOSAIC Infrastructure - Main Entry Point
 * Exports all infrastructure components for the MOSAIC system
 */

// Event Bus Components
export { KafkaEventBus } from './event-bus/event-bus';
export { KafkaConfigManager, getKafkaConfig } from './event-bus/kafka-config';
export { MosaicSchemaRegistry } from './event-bus/schema-registry';
export { DeadLetterQueueManager } from './event-bus/dead-letter-queue';
export { EventRouter } from './event-bus/event-router';

// Types and Interfaces
export type { MosaicKafkaConfig } from './event-bus/kafka-config';
export type { EventSchema } from './event-bus/schema-registry';
export type { DeadLetterRecord, RetryPolicy } from './event-bus/dead-letter-queue';
export type { EventBusConfig, EventMetrics } from './event-bus/event-bus';
export type { RoutingRule, RoutingMetrics } from './event-bus/event-router';

// Infrastructure Manager
export class MosaicInfrastructure {
  private eventBus: KafkaEventBus;
  private schemaRegistry: MosaicSchemaRegistry;
  private dlqManager: DeadLetterQueueManager;
  private eventRouter: EventRouter;
  private isInitialized = false;

  constructor() {
    this.eventBus = new KafkaEventBus();
    this.schemaRegistry = new MosaicSchemaRegistry();
    this.dlqManager = new DeadLetterQueueManager();
    this.eventRouter = new EventRouter();
  }

  /**
   * Initialize all infrastructure components
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('Infrastructure already initialized');
      return;
    }

    try {
      console.log('🚀 Initializing MOSAIC Infrastructure...');

      // Initialize in order of dependencies
      await this.eventBus.initialize();
      console.log('✅ Event Bus initialized');

      await this.schemaRegistry.initialize();
      console.log('✅ Schema Registry initialized');

      await this.dlqManager.initialize();
      console.log('✅ Dead Letter Queue Manager initialized');

      this.isInitialized = true;
      console.log('🎉 MOSAIC Infrastructure initialization complete!');

    } catch (error) {
      console.error('❌ Failed to initialize MOSAIC Infrastructure:', error);
      throw error;
    }
  }

  /**
   * Get the event bus instance
   */
  public getEventBus(): KafkaEventBus {
    if (!this.isInitialized) {
      throw new Error('Infrastructure not initialized. Call initialize() first.');
    }
    return this.eventBus;
  }

  /**
   * Get the schema registry instance
   */
  public getSchemaRegistry(): MosaicSchemaRegistry {
    if (!this.isInitialized) {
      throw new Error('Infrastructure not initialized. Call initialize() first.');
    }
    return this.schemaRegistry;
  }

  /**
   * Get the DLQ manager instance
   */
  public getDLQManager(): DeadLetterQueueManager {
    if (!this.isInitialized) {
      throw new Error('Infrastructure not initialized. Call initialize() first.');
    }
    return this.dlqManager;
  }

  /**
   * Get the event router instance
   */
  public getEventRouter(): EventRouter {
    return this.eventRouter;
  }

  /**
   * Perform health check on all components
   */
  public async healthCheck(): Promise<{
    overall: boolean;
    components: {
      eventBus: boolean;
      kafka: boolean;
    };
  }> {
    try {
      const eventBusHealth = await this.eventBus.healthCheck();
      
      return {
        overall: eventBusHealth,
        components: {
          eventBus: eventBusHealth,
          kafka: eventBusHealth,
        },
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        overall: false,
        components: {
          eventBus: false,
          kafka: false,
        },
      };
    }
  }

  /**
   * Get comprehensive metrics from all components
   */
  public getMetrics(): {
    eventBus: EventMetrics;
    routing: RoutingMetrics;
    dlq: Promise<Record<string, number>>;
  } {
    return {
      eventBus: this.eventBus.getMetrics(),
      routing: this.eventRouter.getMetrics(),
      dlq: this.dlqManager.getDLQStats(),
    };
  }

  /**
   * Gracefully shutdown all infrastructure components
   */
  public async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      console.log('Infrastructure not initialized, nothing to shutdown');
      return;
    }

    try {
      console.log('🔄 Shutting down MOSAIC Infrastructure...');

      // Shutdown in reverse order
      await this.dlqManager.disconnect();
      console.log('✅ DLQ Manager disconnected');

      await this.eventBus.shutdown();
      console.log('✅ Event Bus shutdown');

      this.isInitialized = false;
      console.log('🏁 MOSAIC Infrastructure shutdown complete');

    } catch (error) {
      console.error('❌ Error during infrastructure shutdown:', error);
      throw error;
    }
  }

  /**
   * Check if infrastructure is initialized
   */
  public isReady(): boolean {
    return this.isInitialized;
  }
}

// Singleton instance for easy access
let infrastructureInstance: MosaicInfrastructure | null = null;

/**
 * Get the singleton infrastructure instance
 */
export function getMosaicInfrastructure(): MosaicInfrastructure {
  if (!infrastructureInstance) {
    infrastructureInstance = new MosaicInfrastructure();
  }
  return infrastructureInstance;
}

/**
 * Initialize infrastructure (convenience function)
 */
export async function initializeMosaicInfrastructure(): Promise<MosaicInfrastructure> {
  const infrastructure = getMosaicInfrastructure();
  await infrastructure.initialize();
  return infrastructure;
}

/**
 * Shutdown infrastructure (convenience function)
 */
export async function shutdownMosaicInfrastructure(): Promise<void> {
  if (infrastructureInstance) {
    await infrastructureInstance.shutdown();
    infrastructureInstance = null;
  }
}

// Default export
export default MosaicInfrastructure;
