/**
 * MOSAIC Event Bus Implementation
 * Production-ready Kafka-based event bus for cross-lifecycle communication
 */

import { Producer, Consumer, EachMessagePayload, Kafka } from 'kafkajs';
import { EventEmitter } from 'events';
import { getKafkaConfig } from './kafka-config';
import { MosaicSchemaRegistry } from './schema-registry';
import { DeadLetterQueueManager } from './dead-letter-queue';
import { MosaicEvent, MosaicEventType, EventBus } from '../../shared/types';

export interface EventBusConfig {
  enable_schema_validation: boolean;
  enable_dlq: boolean;
  batch_processing: boolean;
  metrics_enabled: boolean;
}

export interface EventMetrics {
  events_published: number;
  events_consumed: number;
  events_failed: number;
  dlq_events: number;
  processing_time_ms: number;
}

export class KafkaEventBus extends EventEmitter implements EventBus {
  private config = getKafkaConfig();
  private kafka: Kafka;
  private producer: Producer;
  private consumers = new Map<string, Consumer>();
  private schemaRegistry: MosaicSchemaRegistry;
  private dlqManager: DeadLetterQueueManager;
  private metrics: EventMetrics;
  private isInitialized = false;
  private eventBusConfig: EventBusConfig;

  constructor(config?: Partial<EventBusConfig>) {
    super();
    
    this.eventBusConfig = {
      enable_schema_validation: true,
      enable_dlq: true,
      batch_processing: true,
      metrics_enabled: true,
      ...config,
    };

    this.kafka = this.config.getKafkaInstance();
    this.producer = this.kafka.producer(this.config.getProducerConfig());
    this.schemaRegistry = new MosaicSchemaRegistry();
    this.dlqManager = new DeadLetterQueueManager();
    
    this.metrics = {
      events_published: 0,
      events_consumed: 0,
      events_failed: 0,
      dlq_events: 0,
      processing_time_ms: 0,
    };
  }

  /**
   * Initialize the event bus
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Initializing MOSAIC Event Bus...');

      // Create topics if they don't exist
      await this.config.createTopics();

      // Initialize schema registry
      if (this.eventBusConfig.enable_schema_validation) {
        await this.schemaRegistry.initialize();
      }

      // Initialize DLQ manager
      if (this.eventBusConfig.enable_dlq) {
        await this.dlqManager.initialize();
      }

      // Connect producer
      await this.producer.connect();

      this.isInitialized = true;
      console.log('MOSAIC Event Bus initialized successfully');
      
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize event bus:', error);
      throw error;
    }
  }

  /**
   * Publish an event to the bus
   */
  public async publish(event: MosaicEvent): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Event bus not initialized');
    }

    const startTime = Date.now();

    try {
      // Validate event schema if enabled
      if (this.eventBusConfig.enable_schema_validation) {
        const isValid = await this.schemaRegistry.validateEvent(event);
        if (!isValid) {
          throw new Error(`Event validation failed for event ${event.id}`);
        }
      }

      // Determine target topic
      const topic = this.getTopicForEvent(event);
      
      // Prepare message
      const message = {
        key: event.id,
        value: JSON.stringify(event),
        headers: {
          'event-type': event.type,
          'source': event.source,
          'timestamp': event.timestamp.toISOString(),
          'correlation-id': event.correlation_id || '',
          'version': event.version,
        },
      };

      // Publish to Kafka
      await this.producer.send({
        topic,
        messages: [message],
      });

      // Update metrics
      this.metrics.events_published++;
      this.metrics.processing_time_ms += Date.now() - startTime;

      console.log(`Published event ${event.id} to topic ${topic}`);
      this.emit('event-published', event);

    } catch (error) {
      this.metrics.events_failed++;
      console.error(`Failed to publish event ${event.id}:`, error);
      
      // Send to DLQ if enabled
      if (this.eventBusConfig.enable_dlq) {
        await this.dlqManager.sendToDeadLetterQueue(
          event,
          this.getTopicForEvent(event),
          0, // partition
          '0', // offset
          `Publish failed: ${error}`,
          0,
          error instanceof Error ? error.stack : undefined
        );
        this.metrics.dlq_events++;
      }

      throw error;
    }
  }

  /**
   * Subscribe to events
   */
  public async subscribe(
    eventTypes: MosaicEventType[],
    handler: (event: MosaicEvent) => Promise<void>,
    groupId?: string
  ): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Event bus not initialized');
    }

    const consumerGroupId = groupId || `mosaic-consumer-${Date.now()}`;
    const consumer = this.kafka.consumer(this.config.getConsumerConfig(consumerGroupId));
    
    try {
      await consumer.connect();

      // Determine topics to subscribe to
      const topics = this.getTopicsForEventTypes(eventTypes);
      
      await consumer.subscribe({
        topics,
        fromBeginning: false,
      });

      // Start consuming
      await consumer.run({
        eachMessage: async (payload: EachMessagePayload) => {
          await this.processMessage(payload, handler, eventTypes);
        },
      });

      this.consumers.set(consumerGroupId, consumer);
      console.log(`Subscribed to events: ${eventTypes.join(', ')} with group ${consumerGroupId}`);

    } catch (error) {
      console.error('Failed to subscribe to events:', error);
      await consumer.disconnect();
      throw error;
    }
  }

  /**
   * Process incoming message
   */
  private async processMessage(
    payload: EachMessagePayload,
    handler: (event: MosaicEvent) => Promise<void>,
    eventTypes: MosaicEventType[]
  ): Promise<void> {
    const startTime = Date.now();

    try {
      const eventData = JSON.parse(payload.message.value?.toString() || '{}') as MosaicEvent;
      
      // Check if this event type should be processed
      if (!eventTypes.includes(eventData.type)) {
        return;
      }

      // Validate schema if enabled
      if (this.eventBusConfig.enable_schema_validation) {
        const isValid = await this.schemaRegistry.validateEvent(eventData);
        if (!isValid) {
          throw new Error(`Event validation failed for event ${eventData.id}`);
        }
      }

      // Process the event
      await handler(eventData);

      // Update metrics
      this.metrics.events_consumed++;
      this.metrics.processing_time_ms += Date.now() - startTime;

      console.log(`Processed event ${eventData.id} of type ${eventData.type}`);
      this.emit('event-processed', eventData);

    } catch (error) {
      this.metrics.events_failed++;
      console.error('Failed to process message:', error);

      // Send to DLQ if enabled
      if (this.eventBusConfig.enable_dlq) {
        try {
          const eventData = JSON.parse(payload.message.value?.toString() || '{}') as MosaicEvent;
          await this.dlqManager.sendToDeadLetterQueue(
            eventData,
            payload.topic,
            payload.partition,
            payload.message.offset,
            `Processing failed: ${error}`,
            0,
            error instanceof Error ? error.stack : undefined
          );
          this.metrics.dlq_events++;
        } catch (dlqError) {
          console.error('Failed to send to DLQ:', dlqError);
        }
      }

      throw error;
    }
  }

  /**
   * Get topic name for event
   */
  private getTopicForEvent(event: MosaicEvent): string {
    const source = event.source.toLowerCase();
    
    // Map lifecycle sources to topics
    const topicMappings: Record<string, string> = {
      'apex-lc': this.config.getTopicName('apex_events'),
      'aurora-lc': this.config.getTopicName('aurora_events'),
      'prism-lc': this.config.getTopicName('prism_events'),
      'pulse-lc': this.config.getTopicName('pulse_events'),
      'nexus-lc': this.config.getTopicName('nexus_events'),
      'flux-lc': this.config.getTopicName('flux_events'),
      'spark-lc': this.config.getTopicName('spark_events'),
      'shield-lc': this.config.getTopicName('shield_events'),
      'quantum-lc': this.config.getTopicName('quantum_events'),
      'echo-lc': this.config.getTopicName('echo_events'),
      'flow-lc': this.config.getTopicName('flow_events'),
      'system': this.config.getTopicName('system_events'),
    };

    // Check for feature flag events
    if (event.type.startsWith('feature.')) {
      return this.config.getTopicName('feature_flags');
    }

    return topicMappings[source] || this.config.getTopicName('system_events');
  }

  /**
   * Get topics for event types
   */
  private getTopicsForEventTypes(eventTypes: MosaicEventType[]): string[] {
    const topics = new Set<string>();

    for (const eventType of eventTypes) {
      if (eventType.startsWith('apex.')) {
        topics.add(this.config.getTopicName('apex_events'));
      } else if (eventType.startsWith('aurora.')) {
        topics.add(this.config.getTopicName('aurora_events'));
      } else if (eventType.startsWith('prism.')) {
        topics.add(this.config.getTopicName('prism_events'));
      } else if (eventType.startsWith('pulse.')) {
        topics.add(this.config.getTopicName('pulse_events'));
      } else if (eventType.startsWith('feature.')) {
        topics.add(this.config.getTopicName('feature_flags'));
      } else if (eventType.startsWith('system.')) {
        topics.add(this.config.getTopicName('system_events'));
      } else {
        topics.add(this.config.getTopicName('system_events'));
      }
    }

    return Array.from(topics);
  }

  /**
   * Get event bus metrics
   */
  public getMetrics(): EventMetrics {
    return { ...this.metrics };
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      return await this.config.healthCheck();
    } catch (error) {
      console.error('Event bus health check failed:', error);
      return false;
    }
  }

  /**
   * Shutdown the event bus
   */
  public async shutdown(): Promise<void> {
    try {
      console.log('Shutting down MOSAIC Event Bus...');

      // Disconnect all consumers
      for (const [groupId, consumer] of this.consumers) {
        await consumer.disconnect();
        console.log(`Disconnected consumer group: ${groupId}`);
      }
      this.consumers.clear();

      // Disconnect producer
      await this.producer.disconnect();

      // Shutdown DLQ manager
      if (this.eventBusConfig.enable_dlq) {
        await this.dlqManager.disconnect();
      }

      this.isInitialized = false;
      console.log('MOSAIC Event Bus shutdown complete');
      
      this.emit('shutdown');
    } catch (error) {
      console.error('Error during event bus shutdown:', error);
      throw error;
    }
  }
}
