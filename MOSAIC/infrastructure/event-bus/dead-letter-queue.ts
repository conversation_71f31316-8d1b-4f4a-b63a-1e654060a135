/**
 * Dead Letter Queue Handler for MOSAIC Event Bus
 * Manages failed event processing and retry logic
 */

import { Producer, Consumer, EachMessagePayload } from 'kafkajs';
import { getKafkaConfig } from './kafka-config';
import { MosaicEvent } from '../../shared/types';

export interface DeadLetterRecord {
  original_event: MosaicEvent;
  failure_reason: string;
  failure_timestamp: Date;
  retry_count: number;
  original_topic: string;
  original_partition: number;
  original_offset: string;
  stack_trace?: string;
}

export interface RetryPolicy {
  max_retries: number;
  backoff_strategy: 'linear' | 'exponential' | 'fixed';
  base_delay_ms: number;
  max_delay_ms: number;
  jitter: boolean;
}

export class DeadLetterQueueManager {
  private config = getKafkaConfig();
  private producer: Producer;
  private consumer: Consumer;
  private retryPolicy: RetryPolicy;

  constructor(retryPolicy?: Partial<RetryPolicy>) {
    const kafka = this.config.getKafkaInstance();
    this.producer = kafka.producer(this.config.getProducerConfig());
    this.consumer = kafka.consumer(this.config.getConsumerConfig('mosaic-dlq-processor'));
    
    this.retryPolicy = {
      max_retries: 3,
      backoff_strategy: 'exponential',
      base_delay_ms: 1000,
      max_delay_ms: 60000,
      jitter: true,
      ...retryPolicy,
    };
  }

  /**
   * Initialize the DLQ manager
   */
  public async initialize(): Promise<void> {
    try {
      await this.producer.connect();
      await this.consumer.connect();
      
      // Subscribe to all DLQ topics for retry processing
      const dlqTopics = [
        this.config.getTopicName('dlq_apex'),
        this.config.getTopicName('dlq_aurora'),
        this.config.getTopicName('dlq_prism'),
        this.config.getTopicName('dlq_pulse'),
        this.config.getTopicName('dlq_system'),
        this.config.getTopicName('dlq_features'),
      ];

      await this.consumer.subscribe({
        topics: dlqTopics,
        fromBeginning: false,
      });

      // Start processing DLQ messages for retries
      await this.consumer.run({
        eachMessage: this.processDLQMessage.bind(this),
      });

      console.log('Dead Letter Queue manager initialized');
    } catch (error) {
      console.error('Failed to initialize DLQ manager:', error);
      throw error;
    }
  }

  /**
   * Send failed event to dead letter queue
   */
  public async sendToDeadLetterQueue(
    event: MosaicEvent,
    originalTopic: string,
    originalPartition: number,
    originalOffset: string,
    failureReason: string,
    retryCount: number = 0,
    stackTrace?: string
  ): Promise<void> {
    try {
      const dlqTopic = this.getDLQTopicForOriginal(originalTopic);
      
      const deadLetterRecord: DeadLetterRecord = {
        original_event: event,
        failure_reason: failureReason,
        failure_timestamp: new Date(),
        retry_count: retryCount,
        original_topic: originalTopic,
        original_partition: originalPartition,
        original_offset: originalOffset,
        stack_trace: stackTrace,
      };

      await this.producer.send({
        topic: dlqTopic,
        messages: [{
          key: event.id,
          value: JSON.stringify(deadLetterRecord),
          headers: {
            'original-topic': originalTopic,
            'failure-reason': failureReason,
            'retry-count': retryCount.toString(),
            'failure-timestamp': new Date().toISOString(),
          },
        }],
      });

      console.log(`Event ${event.id} sent to DLQ: ${dlqTopic}`);
    } catch (error) {
      console.error('Failed to send event to DLQ:', error);
      // This is critical - if we can't send to DLQ, we need to log and alert
      await this.handleDLQFailure(event, failureReason, error);
    }
  }

  /**
   * Process messages from dead letter queue for retry
   */
  private async processDLQMessage(payload: EachMessagePayload): Promise<void> {
    try {
      const deadLetterRecord: DeadLetterRecord = JSON.parse(payload.message.value?.toString() || '{}');
      
      // Check if we should retry this message
      if (this.shouldRetry(deadLetterRecord)) {
        await this.retryMessage(deadLetterRecord);
      } else {
        await this.handlePermanentFailure(deadLetterRecord);
      }
    } catch (error) {
      console.error('Failed to process DLQ message:', error);
    }
  }

  /**
   * Determine if a message should be retried
   */
  private shouldRetry(record: DeadLetterRecord): boolean {
    if (record.retry_count >= this.retryPolicy.max_retries) {
      return false;
    }

    // Check if enough time has passed for retry
    const timeSinceFailure = Date.now() - record.failure_timestamp.getTime();
    const requiredDelay = this.calculateRetryDelay(record.retry_count);
    
    return timeSinceFailure >= requiredDelay;
  }

  /**
   * Calculate retry delay based on policy
   */
  private calculateRetryDelay(retryCount: number): number {
    let delay: number;

    switch (this.retryPolicy.backoff_strategy) {
      case 'linear':
        delay = this.retryPolicy.base_delay_ms * (retryCount + 1);
        break;
      case 'exponential':
        delay = this.retryPolicy.base_delay_ms * Math.pow(2, retryCount);
        break;
      case 'fixed':
      default:
        delay = this.retryPolicy.base_delay_ms;
        break;
    }

    // Apply jitter if enabled
    if (this.retryPolicy.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    // Cap at max delay
    return Math.min(delay, this.retryPolicy.max_delay_ms);
  }

  /**
   * Retry a failed message
   */
  private async retryMessage(record: DeadLetterRecord): Promise<void> {
    try {
      // Send the original event back to its original topic
      await this.producer.send({
        topic: record.original_topic,
        messages: [{
          key: record.original_event.id,
          value: JSON.stringify(record.original_event),
          headers: {
            'retry-count': (record.retry_count + 1).toString(),
            'original-failure': record.failure_reason,
            'retry-timestamp': new Date().toISOString(),
          },
        }],
      });

      console.log(`Retrying event ${record.original_event.id} (attempt ${record.retry_count + 1})`);
    } catch (error) {
      console.error('Failed to retry message:', error);
      
      // Send back to DLQ with incremented retry count
      await this.sendToDeadLetterQueue(
        record.original_event,
        record.original_topic,
        record.original_partition,
        record.original_offset,
        `Retry failed: ${error}`,
        record.retry_count + 1,
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Handle permanently failed messages
   */
  private async handlePermanentFailure(record: DeadLetterRecord): Promise<void> {
    console.error(`Event ${record.original_event.id} permanently failed after ${record.retry_count} retries`);
    
    // Log to monitoring system
    await this.logPermanentFailure(record);
    
    // Could send to a separate topic for manual investigation
    // await this.sendToManualReviewTopic(record);
  }

  /**
   * Handle DLQ failures (critical error)
   */
  private async handleDLQFailure(event: MosaicEvent, originalFailure: string, dlqError: any): Promise<void> {
    const criticalError = {
      event_id: event.id,
      original_failure: originalFailure,
      dlq_failure: dlqError.message,
      timestamp: new Date().toISOString(),
    };

    // Log to file system as last resort
    console.error('CRITICAL: DLQ failure - event lost:', JSON.stringify(criticalError, null, 2));
    
    // Could write to local file, send to external monitoring, etc.
  }

  /**
   * Log permanent failure for monitoring
   */
  private async logPermanentFailure(record: DeadLetterRecord): Promise<void> {
    const failureLog = {
      event_id: record.original_event.id,
      event_type: record.original_event.type,
      source: record.original_event.source,
      failure_reason: record.failure_reason,
      retry_count: record.retry_count,
      original_topic: record.original_topic,
      timestamp: new Date().toISOString(),
    };

    // Send to monitoring/alerting system
    console.error('Permanent failure logged:', JSON.stringify(failureLog, null, 2));
  }

  /**
   * Get DLQ topic name for original topic
   */
  private getDLQTopicForOriginal(originalTopic: string): string {
    const topicMappings: Record<string, string> = {
      [this.config.getTopicName('apex_events')]: this.config.getTopicName('dlq_apex'),
      [this.config.getTopicName('aurora_events')]: this.config.getTopicName('dlq_aurora'),
      [this.config.getTopicName('prism_events')]: this.config.getTopicName('dlq_prism'),
      [this.config.getTopicName('pulse_events')]: this.config.getTopicName('dlq_pulse'),
      [this.config.getTopicName('system_events')]: this.config.getTopicName('dlq_system'),
      [this.config.getTopicName('feature_flags')]: this.config.getTopicName('dlq_features'),
      [this.config.getTopicName('feature_feedback')]: this.config.getTopicName('dlq_features'),
      [this.config.getTopicName('feature_analytics')]: this.config.getTopicName('dlq_features'),
    };

    return topicMappings[originalTopic] || this.config.getTopicName('dlq_system');
  }

  /**
   * Get DLQ statistics
   */
  public async getDLQStats(): Promise<Record<string, number>> {
    // This would typically query Kafka for partition info and message counts
    // For now, return placeholder stats
    return {
      dlq_apex_messages: 0,
      dlq_aurora_messages: 0,
      dlq_prism_messages: 0,
      dlq_pulse_messages: 0,
      dlq_system_messages: 0,
      dlq_features_messages: 0,
    };
  }

  /**
   * Cleanup resources
   */
  public async disconnect(): Promise<void> {
    try {
      await this.producer.disconnect();
      await this.consumer.disconnect();
      console.log('DLQ manager disconnected');
    } catch (error) {
      console.error('Error disconnecting DLQ manager:', error);
    }
  }
}
