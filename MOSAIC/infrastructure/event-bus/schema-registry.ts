/**
 * Schema Registry for MOSAIC Event Bus
 * Handles event schema validation and evolution
 */

import { SchemaRegistry, SchemaType } from '@kafkajs/confluent-schema-registry';
import { getKafkaConfig } from './kafka-config';
import { MosaicEvent, MosaicEventType } from '../../shared/types';

export interface EventSchema {
  type: 'record';
  name: string;
  namespace: string;
  fields: Array<{
    name: string;
    type: any;
    default?: any;
  }>;
}

export class MosaicSchemaRegistry {
  private registry: SchemaRegistry;
  private config = getKafkaConfig();
  private schemaCache = new Map<string, number>();

  constructor() {
    this.registry = new SchemaRegistry({
      host: this.config.getConfig().schema_registry.url,
      timeout: this.config.getConfig().schema_registry.timeout,
    });
  }

  /**
   * Initialize schema registry with MOSAIC event schemas
   */
  public async initialize(): Promise<void> {
    try {
      await this.registerMosaicEventSchemas();
      console.log('Schema registry initialized successfully');
    } catch (error) {
      console.error('Failed to initialize schema registry:', error);
      throw error;
    }
  }

  /**
   * Register all MOSAIC event schemas
   */
  private async registerMosaicEventSchemas(): Promise<void> {
    const schemas = [
      this.getMosaicEventSchema(),
      this.getApexEventSchema(),
      this.getAuroraEventSchema(),
      this.getPrismEventSchema(),
      this.getPulseEventSchema(),
      this.getSystemEventSchema(),
      this.getFeatureEventSchema(),
    ];

    for (const schema of schemas) {
      await this.registerSchema(schema.name, schema);
    }
  }

  /**
   * Register a schema with the registry
   */
  public async registerSchema(subject: string, schema: EventSchema): Promise<number> {
    try {
      const { id } = await this.registry.register({
        type: SchemaType.AVRO,
        schema: JSON.stringify(schema),
      }, { subject });

      this.schemaCache.set(subject, id);
      console.log(`Registered schema ${subject} with ID ${id}`);
      return id;
    } catch (error) {
      console.error(`Failed to register schema ${subject}:`, error);
      throw error;
    }
  }

  /**
   * Encode event data using schema
   */
  public async encode(subject: string, data: any): Promise<Buffer> {
    try {
      const schemaId = this.schemaCache.get(subject);
      if (!schemaId) {
        throw new Error(`Schema not found for subject: ${subject}`);
      }

      return await this.registry.encode(schemaId, data);
    } catch (error) {
      console.error(`Failed to encode data for subject ${subject}:`, error);
      throw error;
    }
  }

  /**
   * Decode event data using schema
   */
  public async decode(buffer: Buffer): Promise<any> {
    try {
      return await this.registry.decode(buffer);
    } catch (error) {
      console.error('Failed to decode event data:', error);
      throw error;
    }
  }

  /**
   * Validate event against schema
   */
  public async validateEvent(event: MosaicEvent): Promise<boolean> {
    try {
      const subject = this.getSubjectForEventType(event.type);
      await this.encode(subject, event);
      return true;
    } catch (error) {
      console.error('Event validation failed:', error);
      return false;
    }
  }

  /**
   * Get subject name for event type
   */
  private getSubjectForEventType(eventType: MosaicEventType): string {
    const typeToSubject: Record<MosaicEventType, string> = {
      'apex.feature.created': 'apex-event-value',
      'apex.feature.deployed': 'apex-event-value',
      'apex.feature.tested': 'apex-event-value',
      'aurora.customer.health_updated': 'aurora-event-value',
      'aurora.customer.journey_milestone': 'aurora-event-value',
      'aurora.feedback.received': 'aurora-event-value',
      'prism.knowledge.captured': 'prism-event-value',
      'prism.insight.generated': 'prism-event-value',
      'prism.pattern.recognized': 'prism-event-value',
      'pulse.system.health_check': 'pulse-event-value',
      'pulse.resource.allocated': 'pulse-event-value',
      'pulse.optimization.completed': 'pulse-event-value',
      'system.lifecycle.started': 'system-event-value',
      'system.lifecycle.stopped': 'system-event-value',
      'system.error.occurred': 'system-event-value',
      'feature.flag.toggled': 'feature-event-value',
      'feature.experiment.started': 'feature-event-value',
      'feature.feedback.collected': 'feature-event-value',
    };

    return typeToSubject[eventType] || 'mosaic-event-value';
  }

  // Schema Definitions

  private getMosaicEventSchema(): EventSchema {
    return {
      type: 'record',
      name: 'MosaicEvent',
      namespace: 'com.alias.mosaic.events',
      fields: [
        { name: 'id', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'source', type: 'string' },
        { name: 'timestamp', type: { type: 'long', logicalType: 'timestamp-millis' } },
        { name: 'payload', type: ['null', 'string'], default: null },
        { name: 'metadata', type: ['null', 'string'], default: null },
        { name: 'correlation_id', type: ['null', 'string'], default: null },
        { name: 'version', type: 'string', default: '1.0' },
      ],
    };
  }

  private getApexEventSchema(): EventSchema {
    return {
      type: 'record',
      name: 'ApexEvent',
      namespace: 'com.alias.mosaic.apex',
      fields: [
        { name: 'id', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'source', type: 'string' },
        { name: 'timestamp', type: { type: 'long', logicalType: 'timestamp-millis' } },
        { name: 'feature_id', type: ['null', 'string'], default: null },
        { name: 'persona', type: ['null', 'string'], default: null },
        { name: 'workflow_stage', type: ['null', 'string'], default: null },
        { name: 'payload', type: ['null', 'string'], default: null },
        { name: 'metadata', type: ['null', 'string'], default: null },
      ],
    };
  }

  private getAuroraEventSchema(): EventSchema {
    return {
      type: 'record',
      name: 'AuroraEvent',
      namespace: 'com.alias.mosaic.aurora',
      fields: [
        { name: 'id', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'source', type: 'string' },
        { name: 'timestamp', type: { type: 'long', logicalType: 'timestamp-millis' } },
        { name: 'customer_id', type: ['null', 'string'], default: null },
        { name: 'health_score', type: ['null', 'double'], default: null },
        { name: 'journey_stage', type: ['null', 'string'], default: null },
        { name: 'payload', type: ['null', 'string'], default: null },
        { name: 'metadata', type: ['null', 'string'], default: null },
      ],
    };
  }

  private getPrismEventSchema(): EventSchema {
    return {
      type: 'record',
      name: 'PrismEvent',
      namespace: 'com.alias.mosaic.prism',
      fields: [
        { name: 'id', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'source', type: 'string' },
        { name: 'timestamp', type: { type: 'long', logicalType: 'timestamp-millis' } },
        { name: 'knowledge_type', type: ['null', 'string'], default: null },
        { name: 'confidence_score', type: ['null', 'double'], default: null },
        { name: 'tags', type: ['null', { type: 'array', items: 'string' }], default: null },
        { name: 'payload', type: ['null', 'string'], default: null },
        { name: 'metadata', type: ['null', 'string'], default: null },
      ],
    };
  }

  private getPulseEventSchema(): EventSchema {
    return {
      type: 'record',
      name: 'PulseEvent',
      namespace: 'com.alias.mosaic.pulse',
      fields: [
        { name: 'id', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'source', type: 'string' },
        { name: 'timestamp', type: { type: 'long', logicalType: 'timestamp-millis' } },
        { name: 'system_component', type: ['null', 'string'], default: null },
        { name: 'metric_type', type: ['null', 'string'], default: null },
        { name: 'value', type: ['null', 'double'], default: null },
        { name: 'payload', type: ['null', 'string'], default: null },
        { name: 'metadata', type: ['null', 'string'], default: null },
      ],
    };
  }

  private getSystemEventSchema(): EventSchema {
    return {
      type: 'record',
      name: 'SystemEvent',
      namespace: 'com.alias.mosaic.system',
      fields: [
        { name: 'id', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'source', type: 'string' },
        { name: 'timestamp', type: { type: 'long', logicalType: 'timestamp-millis' } },
        { name: 'severity', type: ['null', 'string'], default: null },
        { name: 'component', type: ['null', 'string'], default: null },
        { name: 'error_code', type: ['null', 'string'], default: null },
        { name: 'payload', type: ['null', 'string'], default: null },
        { name: 'metadata', type: ['null', 'string'], default: null },
      ],
    };
  }

  private getFeatureEventSchema(): EventSchema {
    return {
      type: 'record',
      name: 'FeatureEvent',
      namespace: 'com.alias.mosaic.features',
      fields: [
        { name: 'id', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'source', type: 'string' },
        { name: 'timestamp', type: { type: 'long', logicalType: 'timestamp-millis' } },
        { name: 'feature_key', type: ['null', 'string'], default: null },
        { name: 'user_id', type: ['null', 'string'], default: null },
        { name: 'company_id', type: ['null', 'string'], default: null },
        { name: 'enabled', type: ['null', 'boolean'], default: null },
        { name: 'payload', type: ['null', 'string'], default: null },
        { name: 'metadata', type: ['null', 'string'], default: null },
      ],
    };
  }
}
