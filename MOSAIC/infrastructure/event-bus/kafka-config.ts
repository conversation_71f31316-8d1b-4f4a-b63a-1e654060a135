/**
 * Kafka Configuration for MOSAIC Event Bus
 * Provides centralized configuration management for Kafka infrastructure
 */

import { Kafka, KafkaConfig, ProducerConfig, ConsumerConfig, AdminConfig } from 'kafkajs';
import { readFileSync } from 'fs';
import { join } from 'path';
import yaml from 'js-yaml';

export interface MosaicKafkaConfig {
  kafka: {
    brokers: string[];
    client_id: string;
    connection_timeout: number;
    request_timeout: number;
    retry: {
      initial_retry_time: number;
      retries: number;
      max_retry_time: number;
    };
    topics: Record<string, string>;
    topic_config: {
      num_partitions: number;
      replication_factor: number;
      cleanup_policy: string;
      retention_ms: number;
      segment_ms: number;
      max_message_bytes: number;
    };
    consumer_groups: Record<string, string>;
  };
  schema_registry: {
    url: string;
    timeout: number;
    compatibility: string;
    schemas: Record<string, string>;
  };
  event_bus: {
    batch_size: number;
    batch_timeout: number;
    max_concurrent_consumers: number;
    max_retries: number;
    retry_backoff: string;
    dlq_enabled: boolean;
    metrics_enabled: boolean;
    health_check_interval: number;
    routing: {
      default_partition_strategy: string;
      lifecycle_partition_strategy: string;
      system_partition_strategy: string;
    };
  };
}

export class KafkaConfigManager {
  private config: MosaicKafkaConfig;
  private kafka: Kafka;

  constructor(configPath?: string) {
    this.config = this.loadConfig(configPath);
    this.kafka = this.createKafkaInstance();
  }

  private loadConfig(configPath?: string): MosaicKafkaConfig {
    const defaultPath = join(__dirname, '../config/infrastructure.yaml');
    const path = configPath || defaultPath;
    
    try {
      const configFile = readFileSync(path, 'utf8');
      const config = yaml.load(configFile) as MosaicKafkaConfig;
      
      // Override with environment variables if present
      this.applyEnvironmentOverrides(config);
      
      return config;
    } catch (error) {
      console.error('Failed to load Kafka configuration:', error);
      throw new Error(`Unable to load configuration from ${path}`);
    }
  }

  private applyEnvironmentOverrides(config: MosaicKafkaConfig): void {
    // Kafka Brokers
    if (process.env.KAFKA_BROKERS) {
      config.kafka.brokers = process.env.KAFKA_BROKERS.split(',');
    }

    // Schema Registry URL
    if (process.env.SCHEMA_REGISTRY_URL) {
      config.schema_registry.url = process.env.SCHEMA_REGISTRY_URL;
    }

    // Client ID
    if (process.env.KAFKA_CLIENT_ID) {
      config.kafka.client_id = process.env.KAFKA_CLIENT_ID;
    }

    // Environment-specific settings
    const env = process.env.NODE_ENV || 'development';
    if (env === 'production') {
      config.kafka.retry.retries = 10;
      config.event_bus.max_retries = 5;
      config.event_bus.health_check_interval = 15000;
    }
  }

  private createKafkaInstance(): Kafka {
    const kafkaConfig: KafkaConfig = {
      clientId: this.config.kafka.client_id,
      brokers: this.config.kafka.brokers,
      connectionTimeout: this.config.kafka.connection_timeout,
      requestTimeout: this.config.kafka.request_timeout,
      retry: {
        initialRetryTime: this.config.kafka.retry.initial_retry_time,
        retries: this.config.kafka.retry.retries,
        maxRetryTime: this.config.kafka.retry.max_retry_time,
      },
      logLevel: process.env.NODE_ENV === 'development' ? 2 : 1, // INFO in dev, WARN in prod
    };

    return new Kafka(kafkaConfig);
  }

  public getKafkaInstance(): Kafka {
    return this.kafka;
  }

  public getConfig(): MosaicKafkaConfig {
    return this.config;
  }

  public getProducerConfig(): ProducerConfig {
    return {
      maxInFlightRequests: 1,
      idempotent: true,
      transactionTimeout: 30000,
      retry: {
        retries: this.config.kafka.retry.retries,
      },
    };
  }

  public getConsumerConfig(groupId: string): ConsumerConfig {
    return {
      groupId,
      sessionTimeout: 30000,
      rebalanceTimeout: 60000,
      heartbeatInterval: 3000,
      maxBytesPerPartition: 1048576, // 1MB
      minBytes: 1,
      maxBytes: 10485760, // 10MB
      maxWaitTimeInMs: 5000,
      retry: {
        retries: this.config.kafka.retry.retries,
      },
    };
  }

  public getAdminConfig(): AdminConfig {
    return {
      retry: {
        retries: this.config.kafka.retry.retries,
      },
    };
  }

  public getTopicName(topicKey: string): string {
    return this.config.kafka.topics[topicKey] || topicKey;
  }

  public getConsumerGroup(groupKey: string): string {
    return this.config.kafka.consumer_groups[groupKey] || groupKey;
  }

  public getSchemaSubject(schemaKey: string): string {
    return this.config.schema_registry.schemas[schemaKey] || schemaKey;
  }

  public async createTopics(): Promise<void> {
    const admin = this.kafka.admin(this.getAdminConfig());
    
    try {
      await admin.connect();
      
      const topics = Object.values(this.config.kafka.topics).map(topicName => ({
        topic: topicName,
        numPartitions: this.config.kafka.topic_config.num_partitions,
        replicationFactor: this.config.kafka.topic_config.replication_factor,
        configEntries: [
          { name: 'cleanup.policy', value: this.config.kafka.topic_config.cleanup_policy },
          { name: 'retention.ms', value: this.config.kafka.topic_config.retention_ms.toString() },
          { name: 'segment.ms', value: this.config.kafka.topic_config.segment_ms.toString() },
          { name: 'max.message.bytes', value: this.config.kafka.topic_config.max_message_bytes.toString() },
        ],
      }));

      await admin.createTopics({
        topics,
        waitForLeaders: true,
        timeout: 30000,
      });

      console.log('Successfully created Kafka topics');
    } catch (error) {
      console.error('Failed to create Kafka topics:', error);
      throw error;
    } finally {
      await admin.disconnect();
    }
  }

  public async healthCheck(): Promise<boolean> {
    const admin = this.kafka.admin(this.getAdminConfig());
    
    try {
      await admin.connect();
      await admin.listTopics();
      await admin.disconnect();
      return true;
    } catch (error) {
      console.error('Kafka health check failed:', error);
      return false;
    }
  }
}

// Singleton instance
let configManager: KafkaConfigManager | null = null;

export function getKafkaConfig(): KafkaConfigManager {
  if (!configManager) {
    configManager = new KafkaConfigManager();
  }
  return configManager;
}
