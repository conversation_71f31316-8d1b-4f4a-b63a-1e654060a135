/**
 * Event Router for MOSAIC Event Bus
 * Handles intelligent event routing and filtering
 */

import { MosaicEvent, MosaicEventType } from '../../shared/types';
import { getKafkaConfig } from './kafka-config';

export interface RoutingRule {
  id: string;
  name: string;
  condition: (event: MosaicEvent) => boolean;
  target_topics: string[];
  priority: number;
  enabled: boolean;
  metadata?: Record<string, any>;
}

export interface RoutingMetrics {
  rules_evaluated: number;
  rules_matched: number;
  events_routed: number;
  routing_time_ms: number;
}

export class EventRouter {
  private config = getKafkaConfig();
  private rules: Map<string, RoutingRule> = new Map();
  private metrics: RoutingMetrics;

  constructor() {
    this.metrics = {
      rules_evaluated: 0,
      rules_matched: 0,
      events_routed: 0,
      routing_time_ms: 0,
    };

    // Initialize default routing rules
    this.initializeDefaultRules();
  }

  /**
   * Initialize default routing rules for MOSAIC lifecycles
   */
  private initializeDefaultRules(): void {
    const defaultRules: RoutingRule[] = [
      // APEX-LC Feature Development Events
      {
        id: 'apex-feature-events',
        name: 'Route APEX feature events to Aurora for customer impact tracking',
        condition: (event) => event.source === 'apex-lc' && event.type.includes('feature'),
        target_topics: [
          this.config.getTopicName('aurora_events'),
          this.config.getTopicName('prism_events'),
        ],
        priority: 10,
        enabled: true,
        metadata: { description: 'Cross-lifecycle feature tracking' },
      },

      // Aurora Customer Health Events
      {
        id: 'aurora-health-critical',
        name: 'Route critical customer health events to all lifecycles',
        condition: (event) => 
          event.source === 'aurora-lc' && 
          event.type === 'aurora.customer.health_updated' &&
          event.payload && JSON.parse(event.payload).health_score < 0.3,
        target_topics: [
          this.config.getTopicName('apex_events'),
          this.config.getTopicName('pulse_events'),
          this.config.getTopicName('system_events'),
        ],
        priority: 20,
        enabled: true,
        metadata: { description: 'Critical customer health alerts' },
      },

      // PRISM Knowledge Events
      {
        id: 'prism-insights',
        name: 'Route PRISM insights to relevant lifecycles',
        condition: (event) => 
          event.source === 'prism-lc' && 
          event.type === 'prism.insight.generated',
        target_topics: [
          this.config.getTopicName('apex_events'),
          this.config.getTopicName('aurora_events'),
          this.config.getTopicName('pulse_events'),
        ],
        priority: 15,
        enabled: true,
        metadata: { description: 'Knowledge sharing across lifecycles' },
      },

      // Feature Flag Events (Bucket.co integration)
      {
        id: 'feature-flag-changes',
        name: 'Route feature flag changes to customer success',
        condition: (event) => event.type === 'feature.flag.toggled',
        target_topics: [
          this.config.getTopicName('aurora_events'),
          this.config.getTopicName('prism_events'),
        ],
        priority: 12,
        enabled: true,
        metadata: { description: 'Feature flag impact tracking' },
      },

      // System Health Events
      {
        id: 'system-health-critical',
        name: 'Route critical system events to PULSE for orchestration',
        condition: (event) => 
          event.type === 'system.error.occurred' &&
          event.payload && JSON.parse(event.payload).severity === 'critical',
        target_topics: [
          this.config.getTopicName('pulse_events'),
        ],
        priority: 25,
        enabled: true,
        metadata: { description: 'Critical system error handling' },
      },

      // Customer Feedback Events
      {
        id: 'customer-feedback-routing',
        name: 'Route customer feedback to development and knowledge management',
        condition: (event) => 
          event.source === 'aurora-lc' && 
          event.type === 'aurora.feedback.received',
        target_topics: [
          this.config.getTopicName('apex_events'),
          this.config.getTopicName('prism_events'),
        ],
        priority: 18,
        enabled: true,
        metadata: { description: 'Customer feedback integration' },
      },
    ];

    for (const rule of defaultRules) {
      this.rules.set(rule.id, rule);
    }

    console.log(`Initialized ${defaultRules.length} default routing rules`);
  }

  /**
   * Route an event based on configured rules
   */
  public async routeEvent(event: MosaicEvent): Promise<string[]> {
    const startTime = Date.now();
    const matchedTopics = new Set<string>();

    try {
      // Get all enabled rules sorted by priority (higher priority first)
      const enabledRules = Array.from(this.rules.values())
        .filter(rule => rule.enabled)
        .sort((a, b) => b.priority - a.priority);

      for (const rule of enabledRules) {
        this.metrics.rules_evaluated++;

        try {
          if (rule.condition(event)) {
            this.metrics.rules_matched++;
            
            // Add target topics to the set
            for (const topic of rule.target_topics) {
              matchedTopics.add(topic);
            }

            console.log(`Event ${event.id} matched routing rule: ${rule.name}`);
          }
        } catch (error) {
          console.error(`Error evaluating routing rule ${rule.id}:`, error);
        }
      }

      const topics = Array.from(matchedTopics);
      this.metrics.events_routed++;
      this.metrics.routing_time_ms += Date.now() - startTime;

      return topics;
    } catch (error) {
      console.error('Error during event routing:', error);
      return [];
    }
  }

  /**
   * Add a new routing rule
   */
  public addRule(rule: RoutingRule): void {
    this.rules.set(rule.id, rule);
    console.log(`Added routing rule: ${rule.name}`);
  }

  /**
   * Remove a routing rule
   */
  public removeRule(ruleId: string): boolean {
    const removed = this.rules.delete(ruleId);
    if (removed) {
      console.log(`Removed routing rule: ${ruleId}`);
    }
    return removed;
  }

  /**
   * Update a routing rule
   */
  public updateRule(ruleId: string, updates: Partial<RoutingRule>): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      return false;
    }

    const updatedRule = { ...rule, ...updates };
    this.rules.set(ruleId, updatedRule);
    console.log(`Updated routing rule: ${ruleId}`);
    return true;
  }

  /**
   * Enable/disable a routing rule
   */
  public toggleRule(ruleId: string, enabled: boolean): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      return false;
    }

    rule.enabled = enabled;
    console.log(`${enabled ? 'Enabled' : 'Disabled'} routing rule: ${ruleId}`);
    return true;
  }

  /**
   * Get all routing rules
   */
  public getRules(): RoutingRule[] {
    return Array.from(this.rules.values());
  }

  /**
   * Get a specific routing rule
   */
  public getRule(ruleId: string): RoutingRule | undefined {
    return this.rules.get(ruleId);
  }

  /**
   * Get routing metrics
   */
  public getMetrics(): RoutingMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset routing metrics
   */
  public resetMetrics(): void {
    this.metrics = {
      rules_evaluated: 0,
      rules_matched: 0,
      events_routed: 0,
      routing_time_ms: 0,
    };
  }

  /**
   * Test a rule against an event (for debugging)
   */
  public testRule(ruleId: string, event: MosaicEvent): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      throw new Error(`Rule not found: ${ruleId}`);
    }

    try {
      return rule.condition(event);
    } catch (error) {
      console.error(`Error testing rule ${ruleId}:`, error);
      return false;
    }
  }

  /**
   * Validate a routing rule
   */
  public validateRule(rule: RoutingRule): string[] {
    const errors: string[] = [];

    if (!rule.id || rule.id.trim() === '') {
      errors.push('Rule ID is required');
    }

    if (!rule.name || rule.name.trim() === '') {
      errors.push('Rule name is required');
    }

    if (typeof rule.condition !== 'function') {
      errors.push('Rule condition must be a function');
    }

    if (!Array.isArray(rule.target_topics) || rule.target_topics.length === 0) {
      errors.push('Rule must have at least one target topic');
    }

    if (typeof rule.priority !== 'number' || rule.priority < 0) {
      errors.push('Rule priority must be a non-negative number');
    }

    if (typeof rule.enabled !== 'boolean') {
      errors.push('Rule enabled flag must be a boolean');
    }

    return errors;
  }

  /**
   * Export rules configuration
   */
  public exportRules(): string {
    const rulesData = Array.from(this.rules.values()).map(rule => ({
      ...rule,
      condition: rule.condition.toString(), // Convert function to string
    }));

    return JSON.stringify(rulesData, null, 2);
  }

  /**
   * Import rules configuration
   */
  public importRules(rulesJson: string): void {
    try {
      const rulesData = JSON.parse(rulesJson);
      
      for (const ruleData of rulesData) {
        // Convert condition string back to function
        const conditionFunc = new Function('event', `return (${ruleData.condition})(event)`);
        
        const rule: RoutingRule = {
          ...ruleData,
          condition: conditionFunc,
        };

        const errors = this.validateRule(rule);
        if (errors.length === 0) {
          this.rules.set(rule.id, rule);
        } else {
          console.error(`Invalid rule ${rule.id}:`, errors);
        }
      }

      console.log(`Imported ${rulesData.length} routing rules`);
    } catch (error) {
      console.error('Failed to import routing rules:', error);
      throw error;
    }
  }
}
