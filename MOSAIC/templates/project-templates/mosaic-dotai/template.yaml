# MOSAIC + DotAI Project Template
# Standard template for all ALIAS projects with AI-powered development

name: mosaic-dotai
description: "Full MOSAIC integration with DotAI task management and AI assistance"
version: 1.0.0
author: ALIAS Team

# Template configuration
config:
  ai_model: claude-3-5-sonnet
  default_agents:
    - flow_guardian
    - code_reviewer
    - test_generator
    - doc_curator
  
  lifecycle_stages:
    - 01-discovery
    - 02-qualification
    - 03-architecture
    - 04-development
    - 05-testing
    - 06-deployment
    - 07-monitoring
    - 08-optimization
    - 09-scaling
    - 10-evolution
    - 11-sunset

# Project structure
structure:
  directories:
    # DotAI configuration
    - path: .dotai
      subdirs:
        - context
        - features
        - tasks
        - templates
      files:
        - name: project.json
          template: dotai/project.json.tpl
        
    # Claude configuration
    - path: .claude
      subdirs:
        - commands
        - templates
        - history
      files:
        - name: config.json
          template: claude/config.json.tpl
        - name: commands/feature.js
          template: claude/commands/feature.js.tpl
        - name: commands/task.js
          template: claude/commands/task.js.tpl
    
    # MOSAIC configuration
    - path: .mosaic
      subdirs:
        - agents
        - lifecycles
        - workflows
        - config
      files:
        - name: agents/config.json
          template: mosaic/agents.json.tpl
        - name: lifecycles/current.json
          content: |
            {
              "stage": "01-discovery",
              "started": "{{timestamp}}",
              "tasks": []
            }
    
    # Source code
    - path: src
      subdirs:
        - components
        - lib
        - types
        - utils
      files:
        - name: types/dotai.d.ts
          template: src/types/dotai.d.ts.tpl
        - name: lib/mosaic-client.ts
          template: src/lib/mosaic-client.ts.tpl
    
    # Documentation
    - path: docs
      subdirs:
        - features
        - architecture
        - api
        - guides
      files:
        - name: README.md
          template: docs/README.md.tpl
        - name: PRD.md
          content: |
            # Product Requirements Document
            
            > Generated by AI - Update as needed
            
            ## Project: {{project_name}}
            
            *This document will be automatically updated by Claude as features are developed*
    
    # Tests
    - path: tests
      subdirs:
        - unit
        - integration
        - e2e
      files:
        - name: vitest.config.ts
          template: tests/vitest.config.ts.tpl
    
    # Scripts
    - path: scripts
      files:
        - name: generate-docs.sh
          template: scripts/generate-docs.sh.tpl
          executable: true
    
    # Configuration
    - path: config
      files:
        - name: ai.config.ts
          template: config/ai.config.ts.tpl

# Root files
files:
  - name: package.json
    template: package.json.tpl
    
  - name: tsconfig.json
    template: tsconfig.json.tpl
    
  - name: .eslintrc.json
    content: |
      {
        "extends": [
          "next/core-web-vitals",
          "@typescript-eslint/recommended"
        ],
        "rules": {
          "@typescript-eslint/no-unused-vars": "error",
          "@typescript-eslint/no-explicit-any": "warn"
        }
      }
  
  - name: .prettierrc
    content: |
      {
        "semi": true,
        "trailingComma": "es5",
        "singleQuote": true,
        "printWidth": 80,
        "tabWidth": 2
      }
  
  - name: .gitignore
    template: gitignore.tpl
  
  - name: README.md
    template: README.md.tpl
  
  - name: .env.example
    content: |
      # MOSAIC Configuration
      MOSAIC_API_URL=https://mosaic.internal.company.com
      MOSAIC_API_KEY=your-api-key
      
      # AI Configuration
      ANTHROPIC_API_KEY=your-anthropic-key
      OPENAI_API_KEY=your-openai-key
      
      # DotAI Settings
      DOTAI_AUTO_COMMIT=false
      DOTAI_BRANCH_STRATEGY=feature-based

# VS Code / Cursor settings
vscode:
  settings:
    dotai.enabled: true
    dotai.features:
      contextAwareness: true
      taskTracking: true
      autoDocumentation: true
    mosaic.integration: true
    mosaic.agents:
      enabled: true
      autoSuggest: true
    ai.model: "{{ai_model}}"
  
  extensions:
    - anthropic.claude
    - mosaic.mosaic-tools
    - dotai.dotai-assistant
    - dbaeumer.vscode-eslint
    - esbenp.prettier-vscode

# Initial tasks
initial_tasks:
  - id: TASK-001
    description: "Set up development environment"
    lifecycle: 01-discovery
    domain: SAD
    status: in_progress
  
  - id: TASK-002
    description: "Configure CI/CD pipeline"
    lifecycle: 03-architecture
    domain: SAD
    status: pending
  
  - id: TASK-003
    description: "Create initial documentation"
    lifecycle: 01-discovery
    domain: KAD
    status: pending

# GitLab CI/CD template
gitlab_ci:
  stages:
    - validate
    - test
    - build
    - deploy
  
  variables:
    NODE_VERSION: "20"
  
  include:
    - template: Security/SAST.gitlab-ci.yml
    - template: Security/Secret-Detection.gitlab-ci.yml

# Automation scripts
scripts:
  post_create: |
    #!/bin/bash
    echo "🤖 Running post-creation setup..."
    
    # Initialize DotAI
    dotai init --project "{{project_name}}"
    
    # Configure Claude
    claude config set project "{{project_name}}"
    claude config set model "{{ai_model}}"
    
    # Set up MOSAIC agents
    mosaic agent init all
    
    # Generate initial documentation
    claude generate prd --initial
    
    echo "✅ AI-powered development environment ready!"

# Template variables (replaced during scaffolding)
variables:
  - name: project_name
    prompt: "Project name"
    default: "my-mosaic-app"
  
  - name: project_description
    prompt: "Project description"
    default: "AI-powered MOSAIC application"
  
  - name: ai_model
    prompt: "AI model"
    default: "claude-3-5-sonnet"
    options:
      - claude-3-5-sonnet
      - claude-3-opus
      - gpt-4-turbo
  
  - name: primary_domain
    prompt: "Primary PRISM-ICL domain"
    default: "SAD"
    options:
      - SAD
      - KAD
      - IKD
      - MLD
      - TKD
      - MCD
      - ESD
      - UPD

# Feature flags
features:
  dotai_integration: true
  claude_commands: true
  cursor_support: true
  mosaic_agents: true
  lifecycle_automation: true
  ai_documentation: true
  smart_commits: true
  pr_automation: true