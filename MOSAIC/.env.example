# ALIAS MOSAIC Environment Configuration
# Copy this file to .env and update with your actual values

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================

# Application Environment
NODE_ENV=development
APP_NAME=ALIAS MOSAIC
APP_VERSION=1.0.0
APP_URL=http://localhost:3000

# API Configuration
API_URL=http://localhost:3000/api
API_VERSION=v1

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# Convex Database (Primary Real-time Database)
CONVEX_DEPLOYMENT=dev:mosaic-12345
NEXT_PUBLIC_CONVEX_URL=https://your-convex-deployment.convex.cloud

# PostgreSQL (Structured Data)
DATABASE_URL=postgresql://mosaic:password@localhost:5432/mosaic_dev
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Redis (Cache and Sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# ============================================================================
# AI AND AGENT CONFIGURATION
# ============================================================================

# OpenAI API
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4096

# Anthropic Claude API
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# Google Gemini API (for PRISM-ICL)
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-2.5-pro

# Agent Configuration
AGENT_ORCHESTRATOR_URL=http://localhost:8080
AGENT_MAX_CONCURRENT=10
AGENT_TIMEOUT=300000

# PRISM-ICL Configuration
PRISM_ICL_ENABLED=true
PRISM_ICL_DOMAINS=SAD,KAD,IKD,MLD,TKD,MCD,ESD,UPD
PRISM_ICL_QUANTUM_COMMANDS=true

# ============================================================================
# GITLAB INTEGRATION
# ============================================================================

# GitLab Configuration
GITLAB_URL=https://gitlab.alias.dev
GITLAB_TOKEN=glpat-your-gitlab-token
GITLAB_PROJECT_ID=123
GITLAB_WEBHOOK_SECRET=your-webhook-secret

# GitLab Duo
GITLAB_DUO_ENABLED=true
GITLAB_DUO_API_KEY=your-duo-api-key

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

# Authentication
AUTH_SECRET=your-auth-secret-min-32-chars
JWT_SECRET=your-jwt-secret-min-32-chars
JWT_EXPIRES_IN=7d

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Encryption
ENCRYPTION_KEY=your-encryption-key-32-chars
CRYPTO_ALGORITHM=aes-256-gcm

# ============================================================================
# MONITORING AND OBSERVABILITY
# ============================================================================

# Better Stack
BETTERSTACK_TOKEN=your-betterstack-token
BETTERSTACK_SOURCE_TOKEN=your-source-token

# Sentry Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development

# Analytics
ANALYTICS_ENABLED=true
ANALYTICS_PROVIDER=mixpanel
MIXPANEL_TOKEN=your-mixpanel-token

# ============================================================================
# EXTERNAL SERVICES
# ============================================================================

# Email Service
EMAIL_PROVIDER=resend
RESEND_API_KEY=re_your-resend-api-key
FROM_EMAIL=<EMAIL>

# File Storage
STORAGE_PROVIDER=cloudflare-r2
CLOUDFLARE_R2_ACCOUNT_ID=your-account-id
CLOUDFLARE_R2_ACCESS_KEY_ID=your-access-key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-secret-key
CLOUDFLARE_R2_BUCKET=mosaic-storage

# CDN and Performance
CLOUDFLARE_ZONE_ID=your-zone-id
CLOUDFLARE_API_TOKEN=your-cf-api-token

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================

# Development Tools
DEBUG=mosaic:*
LOG_LEVEL=debug
PRETTIER_CHECK=true
ESLINT_CHECK=true

# Testing
TEST_DATABASE_URL=postgresql://mosaic:password@localhost:5432/mosaic_test
TEST_REDIS_URL=redis://localhost:6379/1
PLAYWRIGHT_HEADLESS=true

# Hot Reloading
FAST_REFRESH=true
TURBOPACK=false

# ============================================================================
# PRODUCTION CONFIGURATION (Production Environment Only)
# ============================================================================

# SSL/TLS
FORCE_HTTPS=true
HSTS_MAX_AGE=********

# Security Headers
CSP_ENABLED=true
FRAME_OPTIONS=DENY
CONTENT_TYPE_OPTIONS=nosniff

# Performance
COMPRESSION_ENABLED=true
STATIC_CACHE_MAX_AGE=********
API_RATE_LIMIT=1000

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
TRACING_ENABLED=true

# ============================================================================
# FEATURE FLAGS
# ============================================================================

# Core Features
FEATURE_AGENTS_ENABLED=true
FEATURE_PRISM_ICL_ENABLED=true
FEATURE_LIFECYCLE_AUTOMATION=true
FEATURE_REAL_TIME_COLLABORATION=true

# Advanced Features
FEATURE_QUANTUM_COMMANDS=true
FEATURE_RECURSIVE_ENHANCEMENT=true
FEATURE_CROSS_DOMAIN_RESONANCE=true
FEATURE_AUTO_OPTIMIZATION=true

# Experimental Features
FEATURE_PREDICTIVE_ANALYTICS=false
FEATURE_AUTONOMOUS_AGENTS=false
FEATURE_QUANTUM_COMPUTING=false

# ============================================================================
# LOCALE AND INTERNATIONALIZATION
# ============================================================================

# Default Locale
DEFAULT_LOCALE=en-US
SUPPORTED_LOCALES=en-US,en-AU,en-GB

# Timezone
DEFAULT_TIMEZONE=Australia/Sydney
FORCE_TIMEZONE=false

# ============================================================================
# COMPLIANCE AND LEGAL
# ============================================================================

# Data Protection
GDPR_COMPLIANCE=true
CCPA_COMPLIANCE=true
DATA_RETENTION_DAYS=2555

# Audit and Compliance
AUDIT_LOGGING=true
COMPLIANCE_MONITORING=true
SOC2_COMPLIANCE=true

# ============================================================================
# INFRASTRUCTURE CONFIGURATION
# ============================================================================

# Kubernetes
K8S_NAMESPACE=mosaic
K8S_CLUSTER=alias-production
K8S_CONFIG_PATH=/etc/kubernetes/config

# Docker
DOCKER_REGISTRY=registry.gitlab.alias.dev
DOCKER_IMAGE_TAG=latest

# Network
CORS_ORIGINS=https://mosaic.alias.dev,https://app.alias.dev
TRUSTED_PROXIES=10.0.0.0/8,172.16.0.0/12,192.168.0.0/16

# Resource Limits
MAX_MEMORY=2048MB
MAX_CPU=1000m
MAX_CONNECTIONS=1000

# ============================================================================
# BACKUP AND DISASTER RECOVERY
# ============================================================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=90
BACKUP_STORAGE=s3://alias-backups/mosaic

# Disaster Recovery
DR_ENABLED=true
DR_RTO=1h
DR_RPO=15m
DR_REGION=ap-southeast-2