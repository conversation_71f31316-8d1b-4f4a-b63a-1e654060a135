# ALIAS MOSAIC GitLab CI/CD Pipeline
# Comprehensive pipeline for testing, security, and deployment

variables:
  NODE_VERSION: "20"
  DOCKER_IMAGE: "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
  STAGING_URL: "https://mosaic-staging.alias.dev"
  PRODUCTION_URL: "https://mosaic.alias.dev"

# Pipeline stages
stages:
  - validate
  - test
  - security
  - build
  - deploy-staging
  - deploy-production
  - monitor

# Global before_script
before_script:
  - echo "Starting MOSAIC pipeline for commit $CI_COMMIT_SHA"
  - echo "Branch: $CI_COMMIT_REF_NAME"

# ============================================================================
# VALIDATION STAGE
# ============================================================================

lint:
  stage: validate
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run lint
    - npm run type-check
  artifacts:
    reports:
      junit: lint-results.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

code-quality:
  stage: validate
  image: 
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  only:
    - merge_requests
    - main

validate-lifecycles:
  stage: validate
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run validate:lifecycles
  artifacts:
    reports:
      junit: lifecycle-validation.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================================================
# TESTING STAGE  
# ============================================================================

unit-tests:
  stage: test
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run test:unit -- --coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)%/'
  artifacts:
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

integration-tests:
  stage: test
  image: node:${NODE_VERSION}
  services:
    - postgres:15
    - redis:7
  variables:
    POSTGRES_DB: mosaic_test
    POSTGRES_USER: mosaic
    POSTGRES_PASSWORD: test_password
    REDIS_URL: redis://redis:6379
  script:
    - npm ci
    - npm run test:integration
  artifacts:
    reports:
      junit: integration-test-results.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

e2e-tests:
  stage: test
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  script:
    - npm ci
    - npm run build
    - npm run test:e2e
  artifacts:
    when: always
    paths:
      - playwright-report/
      - test-results/
    expire_in: 1 week
    reports:
      junit: e2e-results.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

performance-tests:
  stage: test
  image: grafana/k6:latest
  script:
    - k6 run tests/performance/load-test.js
  artifacts:
    reports:
      performance: performance-report.json
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "schedule"

# ============================================================================
# SECURITY STAGE
# ============================================================================

sast:
  stage: security
  include:
    - template: Security/SAST.gitlab-ci.yml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

secret-detection:
  stage: security
  include:
    - template: Security/Secret-Detection.gitlab-ci.yml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

dependency-scanning:
  stage: security
  include:
    - template: Security/Dependency-Scanning.gitlab-ci.yml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

license-scanning:
  stage: security
  include:
    - template: Security/License-Scanning.gitlab-ci.yml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

container-scanning:
  stage: security
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock 
      -v $PWD:/tmp/.trivyignore 
      aquasec/trivy image --format template --template "@contrib/gitlab.tpl" 
      -o gl-container-scanning-report.json $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================================================
# BUILD STAGE
# ============================================================================

build-app:
  stage: build
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run build
  artifacts:
    paths:
      - .next/
      - dist/
    expire_in: 1 hour
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

build-docker:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# ============================================================================
# DEPLOYMENT STAGES
# ============================================================================

deploy-staging:
  stage: deploy-staging
  image: 
    name: bitnami/kubectl:latest
    entrypoint: ['']
  environment:
    name: staging
    url: $STAGING_URL
  script:
    - kubectl config use-context $KUBE_CONTEXT_STAGING
    - envsubst < k8s/staging.yaml | kubectl apply -f -
    - kubectl rollout status deployment/mosaic-staging -n mosaic-staging
    - echo "Deployed to staging: $STAGING_URL"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  needs:
    - build-docker
    - unit-tests
    - integration-tests
    - sast

staging-health-check:
  stage: deploy-staging
  image: curlimages/curl:latest
  script:
    - sleep 30  # Wait for deployment to stabilize
    - curl -f $STAGING_URL/health || exit 1
    - curl -f $STAGING_URL/api/health || exit 1
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  needs:
    - deploy-staging

deploy-production:
  stage: deploy-production
  image:
    name: bitnami/kubectl:latest
    entrypoint: ['']
  environment:
    name: production
    url: $PRODUCTION_URL
  script:
    - kubectl config use-context $KUBE_CONTEXT_PRODUCTION
    - envsubst < k8s/production.yaml | kubectl apply -f -
    - kubectl rollout status deployment/mosaic-production -n mosaic-production
    - echo "Deployed to production: $PRODUCTION_URL"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
  needs:
    - staging-health-check
    - e2e-tests
    - performance-tests

production-health-check:
  stage: deploy-production
  image: curlimages/curl:latest
  script:
    - sleep 60  # Wait for deployment to stabilize
    - curl -f $PRODUCTION_URL/health || exit 1
    - curl -f $PRODUCTION_URL/api/health || exit 1
    - echo "Production deployment successful!"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
  needs:
    - deploy-production

# ============================================================================
# MONITORING STAGE
# ============================================================================

deploy-monitoring:
  stage: monitor
  image: 
    name: bitnami/kubectl:latest
    entrypoint: ['']
  script:
    - kubectl config use-context $KUBE_CONTEXT_PRODUCTION
    - kubectl apply -f monitoring/prometheus.yaml
    - kubectl apply -f monitoring/grafana.yaml
    - kubectl apply -f monitoring/alertmanager.yaml
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
  needs:
    - production-health-check

lifecycle-metrics:
  stage: monitor
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run metrics:collect
    - npm run metrics:report
  artifacts:
    reports:
      metrics: lifecycle-metrics.json
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

# ============================================================================
# WORKFLOW RULES
# ============================================================================

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - if: $CI_COMMIT_TAG

# ============================================================================
# INCLUDES
# ============================================================================

include:
  - local: '.gitlab/ci/agents.yml'      # Agent-specific pipelines
  - local: '.gitlab/ci/lifecycles.yml' # Lifecycle validation
  - local: '.gitlab/ci/security.yml'   # Enhanced security scanning
  - local: '.gitlab/ci/deploy.yml'     # Advanced deployment strategies