/**
 * AURORA-LC - Autonomous User Relationship & Outcome Realization Architecture
 * Customer success and relationship management lifecycle
 */

import { EventEmitter } from 'events';
import { MosaicEvent, LifecycleId, SharedContext } from '../shared/types';
import { EventBuilder, EventBus } from '../shared/events';

// AURORA-LC Specific Types
export interface Customer {
  id: string;
  name: string;
  email: string;
  company?: string;
  tier: 'free' | 'starter' | 'professional' | 'enterprise';
  status: 'active' | 'inactive' | 'churned' | 'at_risk';
  onboarded_at: Date;
  last_activity: Date;
  lifetime_value: number;
  health_score: number; // 0-100
  satisfaction_score?: number; // 1-10
  success_manager?: string;
  metadata: Record<string, any>;
}

export interface CustomerJourney {
  customer_id: string;
  stage: 'awareness' | 'trial' | 'onboarding' | 'adoption' | 'expansion' | 'renewal' | 'advocacy';
  milestones: JourneyMilestone[];
  current_milestone?: string;
  completion_percentage: number;
  estimated_completion: Date;
  blockers: string[];
  next_actions: string[];
}

export interface JourneyMilestone {
  id: string;
  name: string;
  description: string;
  stage: string;
  required: boolean;
  completed: boolean;
  completed_at?: Date;
  success_criteria: string[];
  automation_triggers: string[];
  estimated_duration: number; // hours
}

export interface CustomerFeedback {
  id: string;
  customer_id: string;
  type: 'feature_request' | 'bug_report' | 'satisfaction' | 'testimonial' | 'complaint';
  category: 'product' | 'service' | 'support' | 'billing' | 'general';
  priority: 'low' | 'medium' | 'high' | 'critical';
  sentiment: 'positive' | 'neutral' | 'negative';
  confidence: number; // 0-1
  content: string;
  source: 'email' | 'chat' | 'survey' | 'call' | 'social' | 'review';
  created_at: Date;
  resolved: boolean;
  resolution?: string;
  follow_up_required: boolean;
  tags: string[];
}

export interface EngagementActivity {
  id: string;
  customer_id: string;
  type: 'email' | 'call' | 'meeting' | 'demo' | 'training' | 'check_in' | 'support';
  subject: string;
  description: string;
  outcome: 'successful' | 'neutral' | 'concerning' | 'escalation_needed';
  scheduled_at?: Date;
  completed_at?: Date;
  duration?: number; // minutes
  participants: string[];
  notes: string;
  follow_up_actions: string[];
  satisfaction_rating?: number; // 1-5
}

export interface HealthMetrics {
  customer_id: string;
  overall_score: number; // 0-100
  engagement_score: number;
  product_adoption_score: number;
  satisfaction_score: number;
  support_score: number;
  billing_score: number;
  risk_factors: RiskFactor[];
  opportunities: Opportunity[];
  last_calculated: Date;
}

export interface RiskFactor {
  type: 'low_engagement' | 'support_issues' | 'billing_problems' | 'feature_gaps' | 'competitor_interest';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detected_at: Date;
  mitigation_actions: string[];
  auto_escalate: boolean;
}

export interface Opportunity {
  type: 'upsell' | 'cross_sell' | 'expansion' | 'advocacy' | 'referral';
  potential_value: number;
  confidence: number; // 0-1
  description: string;
  recommended_actions: string[];
  timeline: string;
}

// AURORA-LC Main Class
export class AuroraLifecycle extends EventEmitter {
  private eventBus: EventBus;
  private context: SharedContext;
  private customers: Map<string, Customer> = new Map();
  private journeys: Map<string, CustomerJourney> = new Map();
  private feedback: Map<string, CustomerFeedback> = new Map();
  private activities: Map<string, EngagementActivity> = new Map();
  private healthMetrics: Map<string, HealthMetrics> = new Map();

  constructor(eventBus: EventBus, context: SharedContext) {
    super();
    this.eventBus = eventBus;
    this.context = context;
    this.setupEventHandlers();
    this.initializeCustomerData();
    this.startHealthMonitoring();
    this.startAutomationEngine();
  }

  // Setup Event Handlers
  private setupEventHandlers(): void {
    // Listen for feature deliveries from APEX-LC
    this.eventBus.subscribe('apex.feature.delivered', this.handleFeatureDelivery.bind(this));
    this.eventBus.subscribe('apex.deployment.completed', this.notifyCustomersOfUpdate.bind(this));

    // Listen for customer interactions
    this.eventBus.subscribe('aurora.feedback.received', this.processFeedback.bind(this));
    this.eventBus.subscribe('aurora.activity.completed', this.recordActivity.bind(this));
    this.eventBus.subscribe('aurora.customer.registered', this.onboardNewCustomer.bind(this));

    // Listen for system events
    this.eventBus.subscribe('pulse.health.check.required', this.reportHealth.bind(this));
    this.eventBus.subscribe('prism.knowledge.query', this.provideCustomerInsights.bind(this));
  }

  // Initialize Customer Data
  private async initializeCustomerData(): Promise<void> {
    // Load existing customers from context store
    const existingCustomers = await this.context.get<Customer[]>('aurora.customers') || [];
    existingCustomers.forEach(customer => {
      this.customers.set(customer.id, customer);
    });

    // Load customer journeys
    const existingJourneys = await this.context.get<CustomerJourney[]>('aurora.journeys') || [];
    existingJourneys.forEach(journey => {
      this.journeys.set(journey.customer_id, journey);
    });

    // Load feedback
    const existingFeedback = await this.context.get<CustomerFeedback[]>('aurora.feedback') || [];
    existingFeedback.forEach(feedback => {
      this.feedback.set(feedback.id, feedback);
    });

    console.log(`🌅 AURORA-LC initialized with ${this.customers.size} customers`);
  }

  // Start Health Monitoring
  private startHealthMonitoring(): void {
    // Calculate health scores every hour
    setInterval(async () => {
      await this.calculateAllHealthScores();
      await this.identifyAtRiskCustomers();
      await this.identifyOpportunities();
    }, 3600000); // 1 hour

    // Daily comprehensive health assessment
    setInterval(async () => {
      await this.performDailyHealthAssessment();
    }, 86400000); // 24 hours
  }

  // Start Automation Engine
  private startAutomationEngine(): void {
    // Process automation triggers every 15 minutes
    setInterval(async () => {
      await this.processAutomationTriggers();
      await this.executeScheduledActivities();
      await this.sendProactiveEngagements();
    }, 900000); // 15 minutes
  }

  // Customer Onboarding
  async onboardNewCustomer(event: MosaicEvent): Promise<void> {
    const customerData = event.payload;

    const customer: Customer = {
      id: customerData.id || `customer_${Date.now()}`,
      name: customerData.name,
      email: customerData.email,
      company: customerData.company,
      tier: customerData.tier || 'free',
      status: 'active',
      onboarded_at: new Date(),
      last_activity: new Date(),
      lifetime_value: 0,
      health_score: 75, // Start with good health
      success_manager: await this.assignSuccessManager(customerData),
      metadata: customerData.metadata || {}
    };

    // Store customer
    this.customers.set(customer.id, customer);
    await this.persistCustomers();

    // Create customer journey
    const journey = await this.createCustomerJourney(customer);
    this.journeys.set(customer.id, journey);
    await this.persistJourneys();

    // Send welcome sequence
    await this.triggerWelcomeSequence(customer);

    // Publish onboarding event
    const onboardingEvent = EventBuilder.create('aurora.customer.onboarded')
      .source('aurora-lc')
      .payload({
        customer_id: customer.id,
        tier: customer.tier,
        journey_id: journey.customer_id,
        success_manager: customer.success_manager
      })
      .priority('medium')
      .build();

    await this.eventBus.publish(onboardingEvent);

    console.log(`👋 Customer onboarded: ${customer.name} (${customer.email})`);
  }

  // Process Customer Feedback
  async processFeedback(event: MosaicEvent): Promise<void> {
    const feedbackData = event.payload;

    const feedback: CustomerFeedback = {
      id: feedbackData.id || `feedback_${Date.now()}`,
      customer_id: feedbackData.customer_id,
      type: feedbackData.type,
      category: feedbackData.category,
      priority: await this.assessFeedbackPriority(feedbackData),
      sentiment: await this.analyzeSentiment(feedbackData.content),
      confidence: 0.85,
      content: feedbackData.content,
      source: feedbackData.source,
      created_at: new Date(),
      resolved: false,
      follow_up_required: true,
      tags: await this.extractFeedbackTags(feedbackData.content)
    };

    // Store feedback
    this.feedback.set(feedback.id, feedback);
    await this.persistFeedback();

    // Update customer health score
    await this.updateCustomerHealthFromFeedback(feedback);

    // Route feedback to appropriate teams
    await this.routeFeedback(feedback);

    // Send to PRISM-LC for knowledge capture
    const knowledgeEvent = EventBuilder.create('prism.feedback.analysis')
      .source('aurora-lc')
      .target('prism-lc')
      .payload({
        feedback_id: feedback.id,
        customer_tier: this.customers.get(feedback.customer_id)?.tier,
        sentiment: feedback.sentiment,
        category: feedback.category,
        content: feedback.content
      })
      .priority('medium')
      .build();

    await this.eventBus.publish(knowledgeEvent);

    console.log(`📝 Feedback processed: ${feedback.type} from ${feedback.customer_id}`);
  }

  // Handle Feature Delivery Notifications
  async handleFeatureDelivery(event: MosaicEvent): Promise<void> {
    const featureData = event.payload;

    // Identify customers who would benefit from this feature
    const interestedCustomers = await this.identifyInterestedCustomers(featureData);

    // Send personalized notifications
    for (const customer of interestedCustomers) {
      await this.sendFeatureNotification(customer, featureData);

      // Update customer journey if this addresses a milestone
      await this.checkJourneyMilestoneCompletion(customer.id, featureData);
    }

    // Publish feature notification event
    const notificationEvent = EventBuilder.create('aurora.feature.notification.sent')
      .source('aurora-lc')
      .payload({
        feature_id: featureData.feature_id,
        customers_notified: interestedCustomers.length,
        notification_type: 'feature_delivery'
      })
      .priority('low')
      .build();

    await this.eventBus.publish(notificationEvent);
  }

  // Calculate Customer Health Scores
  async calculateCustomerHealth(customerId: string): Promise<HealthMetrics> {
    const customer = this.customers.get(customerId);
    if (!customer) {
      throw new Error(`Customer ${customerId} not found`);
    }

    // Calculate component scores
    const engagementScore = await this.calculateEngagementScore(customer);
    const adoptionScore = await this.calculateProductAdoptionScore(customer);
    const satisfactionScore = await this.calculateSatisfactionScore(customer);
    const supportScore = await this.calculateSupportScore(customer);
    const billingScore = await this.calculateBillingScore(customer);

    // Calculate overall health score (weighted average)
    const overallScore = Math.round(
      (engagementScore * 0.25) +
      (adoptionScore * 0.30) +
      (satisfactionScore * 0.20) +
      (supportScore * 0.15) +
      (billingScore * 0.10)
    );

    // Identify risk factors and opportunities
    const riskFactors = await this.identifyRiskFactors(customer, {
      engagement: engagementScore,
      adoption: adoptionScore,
      satisfaction: satisfactionScore,
      support: supportScore,
      billing: billingScore
    });

    const opportunities = await this.identifyCustomerOpportunities(customer, {
      engagement: engagementScore,
      adoption: adoptionScore,
      satisfaction: satisfactionScore
    });

    const healthMetrics: HealthMetrics = {
      customer_id: customerId,
      overall_score: overallScore,
      engagement_score: engagementScore,
      product_adoption_score: adoptionScore,
      satisfaction_score: satisfactionScore,
      support_score: supportScore,
      billing_score: billingScore,
      risk_factors: riskFactors,
      opportunities: opportunities,
      last_calculated: new Date()
    };

    // Update customer health score
    customer.health_score = overallScore;
    await this.persistCustomers();

    // Store health metrics
    this.healthMetrics.set(customerId, healthMetrics);
    await this.persistHealthMetrics();

    // Trigger alerts if health is concerning
    if (overallScore < 60) {
      await this.triggerHealthAlert(customer, healthMetrics);
    }

    return healthMetrics;
  }

  // Customer Journey Management
  async createCustomerJourney(customer: Customer): Promise<CustomerJourney> {
    const milestones = await this.getJourneyMilestones(customer.tier);

    const journey: CustomerJourney = {
      customer_id: customer.id,
      stage: 'onboarding',
      milestones,
      current_milestone: milestones[0]?.id,
      completion_percentage: 0,
      estimated_completion: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)), // 30 days
      blockers: [],
      next_actions: ['Complete profile setup', 'Schedule onboarding call']
    };

    return journey;
  }

  async updateJourneyProgress(customerId: string, milestoneId: string): Promise<void> {
    const journey = this.journeys.get(customerId);
    if (!journey) return;

    // Mark milestone as completed
    const milestone = journey.milestones.find(m => m.id === milestoneId);
    if (milestone && !milestone.completed) {
      milestone.completed = true;
      milestone.completed_at = new Date();

      // Update journey progress
      const completedMilestones = journey.milestones.filter(m => m.completed).length;
      journey.completion_percentage = Math.round((completedMilestones / journey.milestones.length) * 100);

      // Find next milestone
      const nextMilestone = journey.milestones.find(m => !m.completed);
      journey.current_milestone = nextMilestone?.id;

      // Update stage if necessary
      if (nextMilestone) {
        journey.stage = nextMilestone.stage as any;
      } else {
        journey.stage = 'advocacy'; // All milestones completed
      }

      await this.persistJourneys();

      // Publish journey progress event
      const progressEvent = EventBuilder.create('aurora.journey.milestone.completed')
        .source('aurora-lc')
        .payload({
          customer_id: customerId,
          milestone_id: milestoneId,
          milestone_name: milestone.name,
          completion_percentage: journey.completion_percentage,
          current_stage: journey.stage
        })
        .priority('medium')
        .build();

      await this.eventBus.publish(progressEvent);

      // Trigger automation for milestone completion
      await this.triggerMilestoneAutomation(customerId, milestone);
    }
  }

  // Proactive Engagement
  async sendProactiveEngagements(): Promise<void> {
    for (const [customerId, customer] of this.customers) {
      const healthMetrics = this.healthMetrics.get(customerId);
      if (!healthMetrics) continue;

      // Check for engagement opportunities
      const engagementNeeded = await this.assessEngagementNeeds(customer, healthMetrics);

      if (engagementNeeded.length > 0) {
        for (const engagement of engagementNeeded) {
          await this.scheduleEngagement(customer, engagement);
        }
      }
    }
  }

  // Event Handlers
  private async notifyCustomersOfUpdate(event: MosaicEvent): Promise<void> {
    const deploymentData = event.payload;

    // Send update notifications to relevant customers
    const affectedCustomers = await this.getAffectedCustomers(deploymentData);

    for (const customer of affectedCustomers) {
      await this.sendUpdateNotification(customer, deploymentData);
    }
  }

  private async reportHealth(event: MosaicEvent): Promise<void> {
    const overallHealth = await this.calculateOverallCustomerHealth();

    const healthReport = {
      total_customers: this.customers.size,
      healthy_customers: Array.from(this.customers.values()).filter(c => c.health_score >= 80).length,
      at_risk_customers: Array.from(this.customers.values()).filter(c => c.health_score < 60).length,
      average_health_score: overallHealth.average_score,
      churn_risk: overallHealth.churn_risk,
      expansion_opportunities: overallHealth.expansion_opportunities
    };

    const responseEvent = EventBuilder.create('aurora.health.report')
      .source('aurora-lc')
      .target(event.source)
      .payload(healthReport)
      .correlationId(event.id)
      .build();

    await this.eventBus.publish(responseEvent);
  }

  private async recordActivity(event: MosaicEvent): Promise<void> {
    const activityData = event.payload;

    // Record customer activity
    const customer = this.customers.get(activityData.customer_id);
    if (!customer) {
      console.warn(`Customer ${activityData.customer_id} not found for activity recording`);
      return;
    }

    // Add activity to customer record
    const activity: EngagementActivity = {
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      customer_id: activityData.customer_id,
      type: activityData.type || 'general',
      timestamp: new Date(),
      outcome: activityData.outcome || 'successful',
      notes: activityData.notes || '',
      follow_up_actions: activityData.follow_up_actions || []
    };

    customer.activities.push(activity);

    // Update customer in context store
    await this.context.set(`customer:${customer.id}`, customer);

    // Publish activity recorded event
    const activityEvent = new EventBuilder()
      .setType('aurora.activity.recorded')
      .setSource('aurora-lc')
      .setPayload({ customer_id: customer.id, activity })
      .build();

    await this.eventBus.publish(activityEvent);
  }

  private async provideCustomerInsights(event: MosaicEvent): Promise<void> {
    const query = event.payload;

    // Analyze customer data for insights
    const insights = await this.generateCustomerInsights(query);

    const insightsEvent = EventBuilder.create('aurora.customer.insights')
      .source('aurora-lc')
      .target('prism-lc')
      .payload({
        query_id: event.id,
        insights,
        customer_count: this.customers.size,
        data_points: this.feedback.size + this.activities.size
      })
      .correlationId(event.id)
      .build();

    await this.eventBus.publish(insightsEvent);
  }

  // Utility Methods
  private async assignSuccessManager(customerData: any): Promise<string> {
    // Simple round-robin assignment (in production, would consider workload, expertise, etc.)
    const managers = ['alice.smith', 'bob.johnson', 'carol.williams'];
    return managers[Math.floor(Math.random() * managers.length)];
  }

  private async assessFeedbackPriority(feedbackData: any): Promise<'low' | 'medium' | 'high' | 'critical'> {
    // Assess priority based on customer tier, sentiment, and content
    if (feedbackData.type === 'bug_report' && feedbackData.severity === 'critical') return 'critical';
    if (feedbackData.customer_tier === 'enterprise') return 'high';
    if (feedbackData.sentiment === 'negative') return 'medium';
    return 'low';
  }

  private async analyzeSentiment(content: string): Promise<'positive' | 'neutral' | 'negative'> {
    // Simple sentiment analysis (in production, would use ML model)
    const positiveWords = ['great', 'excellent', 'love', 'amazing', 'perfect', 'wonderful'];
    const negativeWords = ['terrible', 'awful', 'hate', 'broken', 'frustrated', 'disappointed'];

    const lowerContent = content.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerContent.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerContent.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private async extractFeedbackTags(content: string): Promise<string[]> {
    // Extract relevant tags from feedback content
    const tags: string[] = [];
    const lowerContent = content.toLowerCase();

    if (lowerContent.includes('performance') || lowerContent.includes('slow')) tags.push('performance');
    if (lowerContent.includes('ui') || lowerContent.includes('interface')) tags.push('ui');
    if (lowerContent.includes('feature') || lowerContent.includes('functionality')) tags.push('feature');
    if (lowerContent.includes('bug') || lowerContent.includes('error')) tags.push('bug');
    if (lowerContent.includes('billing') || lowerContent.includes('payment')) tags.push('billing');

    return tags;
  }

  private async updateCustomerHealthFromFeedback(feedback: CustomerFeedback): Promise<void> {
    const customer = this.customers.get(feedback.customer_id);
    if (!customer) return;

    // Adjust health score based on feedback sentiment and type
    let adjustment = 0;

    switch (feedback.sentiment) {
      case 'positive':
        adjustment = feedback.type === 'testimonial' ? 5 : 2;
        break;
      case 'negative':
        adjustment = feedback.type === 'complaint' ? -10 : -5;
        break;
      case 'neutral':
        adjustment = 0;
        break;
    }

    customer.health_score = Math.max(0, Math.min(100, customer.health_score + adjustment));
    await this.persistCustomers();
  }

  private async routeFeedback(feedback: CustomerFeedback): Promise<void> {
    // Route feedback to appropriate teams/systems
    if (feedback.type === 'feature_request') {
      const featureRequestEvent = EventBuilder.create('apex.feature.request.received')
        .source('aurora-lc')
        .target('apex-lc')
        .payload({
          customer_id: feedback.customer_id,
          feedback_id: feedback.id,
          priority: feedback.priority,
          content: feedback.content,
          customer_tier: this.customers.get(feedback.customer_id)?.tier
        })
        .priority(feedback.priority)
        .build();

      await this.eventBus.publish(featureRequestEvent);
    }

    if (feedback.type === 'bug_report') {
      // Route to development team
      console.log(`🐛 Bug report routed to development team: ${feedback.id}`);
    }

    if (feedback.priority === 'critical') {
      // Escalate immediately
      await this.escalateFeedback(feedback);
    }
  }

  private async escalateFeedback(feedback: CustomerFeedback): Promise<void> {
    const escalationEvent = EventBuilder.create('aurora.feedback.escalated')
      .source('aurora-lc')
      .payload({
        feedback_id: feedback.id,
        customer_id: feedback.customer_id,
        priority: feedback.priority,
        type: feedback.type,
        escalation_reason: 'Critical priority feedback'
      })
      .priority('critical')
      .build();

    await this.eventBus.publish(escalationEvent);
  }

  private async identifyInterestedCustomers(featureData: any): Promise<Customer[]> {
    // Identify customers who would be interested in this feature
    const interestedCustomers: Customer[] = [];

    for (const [id, customer] of this.customers) {
      // Check if customer has requested similar features
      const relevantFeedback = Array.from(this.feedback.values()).filter(f =>
        f.customer_id === id &&
        f.type === 'feature_request' &&
        this.isFeatureRelevant(f.content, featureData)
      );

      if (relevantFeedback.length > 0) {
        interestedCustomers.push(customer);
      }
    }

    return interestedCustomers;
  }

  private isFeatureRelevant(feedbackContent: string, featureData: any): boolean {
    // Simple relevance check (in production, would use semantic similarity)
    const keywords = featureData.title.toLowerCase().split(' ');
    const content = feedbackContent.toLowerCase();

    return keywords.some(keyword => content.includes(keyword));
  }

  private async sendFeatureNotification(customer: Customer, featureData: any): Promise<void> {
    // Send personalized feature notification
    console.log(`📧 Sending feature notification to ${customer.email}: ${featureData.title}`);

    // Record activity
    const activity: EngagementActivity = {
      id: `activity_${Date.now()}_${customer.id}`,
      customer_id: customer.id,
      type: 'email',
      subject: `New Feature: ${featureData.title}`,
      description: `Notification about new feature delivery`,
      outcome: 'successful',
      completed_at: new Date(),
      participants: [customer.success_manager || 'system'],
      notes: `Automated feature notification sent`,
      follow_up_actions: ['Monitor feature adoption']
    };

    this.activities.set(activity.id, activity);
    await this.persistActivities();
  }

  private async checkJourneyMilestoneCompletion(customerId: string, featureData: any): Promise<void> {
    const journey = this.journeys.get(customerId);
    if (!journey) return;

    // Check if this feature addresses any pending milestones
    const relevantMilestone = journey.milestones.find(m =>
      !m.completed &&
      m.automation_triggers.some(trigger =>
        trigger.includes('feature_delivery') &&
        this.isFeatureRelevant(m.description, featureData)
      )
    );

    if (relevantMilestone) {
      await this.updateJourneyProgress(customerId, relevantMilestone.id);
    }
  }

  private async calculateEngagementScore(customer: Customer): Promise<number> {
    // Calculate engagement based on recent activities
    const recentActivities = Array.from(this.activities.values()).filter(a =>
      a.customer_id === customer.id &&
      a.completed_at &&
      a.completed_at > new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)) // Last 30 days
    );

    const baseScore = Math.min(100, recentActivities.length * 10);
    const daysSinceLastActivity = Math.floor((Date.now() - customer.last_activity.getTime()) / (24 * 60 * 60 * 1000));
    const recencyPenalty = Math.min(50, daysSinceLastActivity * 2);

    return Math.max(0, baseScore - recencyPenalty);
  }

  private async calculateProductAdoptionScore(customer: Customer): Promise<number> {
    // Mock product adoption calculation
    // In production, would analyze feature usage, API calls, etc.
    return 75 + Math.floor(Math.random() * 25);
  }

  private async calculateSatisfactionScore(customer: Customer): Promise<number> {
    // Calculate satisfaction based on feedback sentiment
    const customerFeedback = Array.from(this.feedback.values()).filter(f => f.customer_id === customer.id);

    if (customerFeedback.length === 0) return 70; // Neutral default

    const sentimentScores = customerFeedback.map(f => {
      switch (f.sentiment) {
        case 'positive': return 90;
        case 'neutral': return 70;
        case 'negative': return 30;
        default: return 70;
      }
    });

    return Math.round(sentimentScores.reduce((a, b) => a + b, 0) / sentimentScores.length);
  }

  private async calculateSupportScore(customer: Customer): Promise<number> {
    // Calculate support score based on support interactions
    const supportActivities = Array.from(this.activities.values()).filter(a =>
      a.customer_id === customer.id && a.type === 'support'
    );

    if (supportActivities.length === 0) return 85; // Good default

    const avgSatisfaction = supportActivities
      .filter(a => a.satisfaction_rating)
      .reduce((sum, a) => sum + (a.satisfaction_rating || 0), 0) / supportActivities.length;

    return Math.round((avgSatisfaction / 5) * 100);
  }

  private async calculateBillingScore(customer: Customer): Promise<number> {
    // Mock billing score (in production, would check payment history, etc.)
    return customer.tier === 'free' ? 100 : 90 + Math.floor(Math.random() * 10);
  }

  private async identifyRiskFactors(customer: Customer, scores: any): Promise<RiskFactor[]> {
    const riskFactors: RiskFactor[] = [];

    if (scores.engagement < 40) {
      riskFactors.push({
        type: 'low_engagement',
        severity: scores.engagement < 20 ? 'critical' : 'high',
        description: 'Customer engagement has significantly decreased',
        detected_at: new Date(),
        mitigation_actions: ['Schedule check-in call', 'Send re-engagement campaign'],
        auto_escalate: scores.engagement < 20
      });
    }

    if (scores.satisfaction < 50) {
      riskFactors.push({
        type: 'support_issues',
        severity: 'high',
        description: 'Customer satisfaction is below acceptable threshold',
        detected_at: new Date(),
        mitigation_actions: ['Review recent support interactions', 'Schedule satisfaction survey'],
        auto_escalate: true
      });
    }

    return riskFactors;
  }

  private async identifyCustomerOpportunities(customer: Customer, scores: any): Promise<Opportunity[]> {
    const opportunities: Opportunity[] = [];

    if (scores.satisfaction > 80 && scores.adoption > 70) {
      opportunities.push({
        type: 'upsell',
        potential_value: customer.tier === 'starter' ? 500 : 1000,
        confidence: 0.7,
        description: 'High satisfaction and adoption indicate upsell potential',
        recommended_actions: ['Present upgrade options', 'Schedule expansion call'],
        timeline: '30 days'
      });
    }

    if (scores.satisfaction > 85) {
      opportunities.push({
        type: 'advocacy',
        potential_value: 0,
        confidence: 0.8,
        description: 'Highly satisfied customer could become advocate',
        recommended_actions: ['Request testimonial', 'Invite to case study'],
        timeline: '14 days'
      });
    }

    return opportunities;
  }

  private async triggerHealthAlert(customer: Customer, healthMetrics: HealthMetrics): Promise<void> {
    const alertEvent = EventBuilder.create('aurora.customer.health.alert')
      .source('aurora-lc')
      .payload({
        customer_id: customer.id,
        health_score: healthMetrics.overall_score,
        risk_factors: healthMetrics.risk_factors,
        success_manager: customer.success_manager,
        alert_level: healthMetrics.overall_score < 40 ? 'critical' : 'warning'
      })
      .priority('high')
      .build();

    await this.eventBus.publish(alertEvent);
  }

  private async getJourneyMilestones(tier: string): Promise<JourneyMilestone[]> {
    const baseMilestones: JourneyMilestone[] = [
      {
        id: 'profile_setup',
        name: 'Complete Profile Setup',
        description: 'Customer completes their profile information',
        stage: 'onboarding',
        required: true,
        completed: false,
        success_criteria: ['Profile 100% complete', 'Contact information verified'],
        automation_triggers: ['profile.completed'],
        estimated_duration: 0.5
      },
      {
        id: 'first_login',
        name: 'First Successful Login',
        description: 'Customer logs in for the first time',
        stage: 'onboarding',
        required: true,
        completed: false,
        success_criteria: ['Successful authentication', 'Dashboard accessed'],
        automation_triggers: ['auth.first_login'],
        estimated_duration: 0.25
      },
      {
        id: 'feature_exploration',
        name: 'Explore Core Features',
        description: 'Customer explores and uses core platform features',
        stage: 'adoption',
        required: true,
        completed: false,
        success_criteria: ['At least 3 features used', 'Tutorial completed'],
        automation_triggers: ['feature.usage_threshold'],
        estimated_duration: 2
      }
    ];

    // Add tier-specific milestones
    if (tier === 'enterprise') {
      baseMilestones.push({
        id: 'integration_setup',
        name: 'API Integration Setup',
        description: 'Enterprise customer sets up API integrations',
        stage: 'adoption',
        required: true,
        completed: false,
        success_criteria: ['API key generated', 'First API call successful'],
        automation_triggers: ['api.first_call'],
        estimated_duration: 4
      });
    }

    return baseMilestones;
  }

  private async triggerWelcomeSequence(customer: Customer): Promise<void> {
    // Send welcome email sequence
    console.log(`📧 Triggering welcome sequence for ${customer.email}`);

    const welcomeEvent = EventBuilder.create('aurora.welcome.sequence.started')
      .source('aurora-lc')
      .payload({
        customer_id: customer.id,
        tier: customer.tier,
        sequence_type: 'onboarding'
      })
      .priority('medium')
      .build();

    await this.eventBus.publish(welcomeEvent);
  }

  private async triggerMilestoneAutomation(customerId: string, milestone: JourneyMilestone): Promise<void> {
    // Trigger automation based on milestone completion
    for (const trigger of milestone.automation_triggers) {
      const automationEvent = EventBuilder.create(`aurora.automation.${trigger}`)
        .source('aurora-lc')
        .payload({
          customer_id: customerId,
          milestone_id: milestone.id,
          milestone_name: milestone.name,
          trigger: trigger
        })
        .priority('medium')
        .build();

      await this.eventBus.publish(automationEvent);
    }
  }

  private async assessEngagementNeeds(customer: Customer, healthMetrics: HealthMetrics): Promise<string[]> {
    const needs: string[] = [];

    if (healthMetrics.engagement_score < 50) {
      needs.push('re_engagement_campaign');
    }

    if (healthMetrics.risk_factors.length > 0) {
      needs.push('risk_mitigation_call');
    }

    if (healthMetrics.opportunities.length > 0) {
      needs.push('opportunity_discussion');
    }

    // Check for milestone blockers
    const journey = this.journeys.get(customer.id);
    if (journey && journey.blockers.length > 0) {
      needs.push('blocker_resolution');
    }

    return needs;
  }

  private async scheduleEngagement(customer: Customer, engagementType: string): Promise<void> {
    const activity: EngagementActivity = {
      id: `activity_${Date.now()}_${customer.id}`,
      customer_id: customer.id,
      type: this.getActivityType(engagementType),
      subject: this.getEngagementSubject(engagementType),
      description: `Proactive engagement: ${engagementType}`,
      outcome: 'successful',
      scheduled_at: new Date(Date.now() + (24 * 60 * 60 * 1000)), // Tomorrow
      participants: [customer.success_manager || 'system'],
      notes: 'Automatically scheduled based on health metrics',
      follow_up_actions: []
    };

    this.activities.set(activity.id, activity);
    await this.persistActivities();

    console.log(`📅 Scheduled ${engagementType} for ${customer.email}`);
  }

  private getActivityType(engagementType: string): 'email' | 'call' | 'meeting' | 'demo' | 'training' | 'check_in' | 'support' {
    switch (engagementType) {
      case 're_engagement_campaign': return 'email';
      case 'risk_mitigation_call': return 'call';
      case 'opportunity_discussion': return 'meeting';
      case 'blocker_resolution': return 'support';
      default: return 'check_in';
    }
  }

  private getEngagementSubject(engagementType: string): string {
    switch (engagementType) {
      case 're_engagement_campaign': return 'We miss you! Let\'s reconnect';
      case 'risk_mitigation_call': return 'Quick check-in to ensure your success';
      case 'opportunity_discussion': return 'Exciting opportunities for your account';
      case 'blocker_resolution': return 'Let\'s resolve any challenges you\'re facing';
      default: return 'Customer success check-in';
    }
  }

  // Persistence Methods
  private async persistCustomers(): Promise<void> {
    const customers = Array.from(this.customers.values());
    await this.context.set('aurora.customers', customers);
  }

  private async persistJourneys(): Promise<void> {
    const journeys = Array.from(this.journeys.values());
    await this.context.set('aurora.journeys', journeys);
  }

  private async persistFeedback(): Promise<void> {
    const feedback = Array.from(this.feedback.values());
    await this.context.set('aurora.feedback', feedback);
  }

  private async persistActivities(): Promise<void> {
    const activities = Array.from(this.activities.values());
    await this.context.set('aurora.activities', activities);
  }

  private async persistHealthMetrics(): Promise<void> {
    const metrics = Array.from(this.healthMetrics.values());
    await this.context.set('aurora.health_metrics', metrics);
  }

  // Additional utility methods for completeness
  private async calculateAllHealthScores(): Promise<void> {
    for (const [customerId] of this.customers) {
      await this.calculateCustomerHealth(customerId);
    }
  }

  private async identifyAtRiskCustomers(): Promise<void> {
    const atRiskCustomers = Array.from(this.customers.values()).filter(c => c.health_score < 60);

    if (atRiskCustomers.length > 0) {
      const alertEvent = EventBuilder.create('aurora.customers.at_risk')
        .source('aurora-lc')
        .payload({
          count: atRiskCustomers.length,
          customers: atRiskCustomers.map(c => ({ id: c.id, name: c.name, health_score: c.health_score }))
        })
        .priority('high')
        .build();

      await this.eventBus.publish(alertEvent);
    }
  }

  private async identifyOpportunities(): Promise<void> {
    const opportunities = Array.from(this.healthMetrics.values())
      .flatMap(hm => hm.opportunities)
      .filter(opp => opp.confidence > 0.7);

    if (opportunities.length > 0) {
      const opportunityEvent = EventBuilder.create('aurora.opportunities.identified')
        .source('aurora-lc')
        .payload({
          count: opportunities.length,
          total_potential_value: opportunities.reduce((sum, opp) => sum + opp.potential_value, 0)
        })
        .priority('medium')
        .build();

      await this.eventBus.publish(opportunityEvent);
    }
  }

  private async performDailyHealthAssessment(): Promise<void> {
    console.log('🏥 Performing daily customer health assessment...');

    await this.calculateAllHealthScores();
    await this.identifyAtRiskCustomers();
    await this.identifyOpportunities();

    const healthSummary = {
      total_customers: this.customers.size,
      average_health: Array.from(this.customers.values()).reduce((sum, c) => sum + c.health_score, 0) / this.customers.size,
      at_risk_count: Array.from(this.customers.values()).filter(c => c.health_score < 60).length,
      opportunity_count: Array.from(this.healthMetrics.values()).flatMap(hm => hm.opportunities).length
    };

    console.log(`📊 Health Summary: ${healthSummary.total_customers} customers, avg health: ${healthSummary.average_health.toFixed(1)}`);
  }

  private async processAutomationTriggers(): Promise<void> {
    // Process any pending automation triggers
    for (const [customerId, journey] of this.journeys) {
      for (const milestone of journey.milestones) {
        if (!milestone.completed) {
          // Check if automation conditions are met
          await this.checkAutomationConditions(customerId, milestone);
        }
      }
    }
  }

  private async checkAutomationConditions(customerId: string, milestone: JourneyMilestone): Promise<void> {
    // Check if conditions are met for milestone automation
    // This would integrate with actual system events and user actions
    // For now, we'll simulate some basic checks

    const customer = this.customers.get(customerId);
    if (!customer) return;

    // Example: Check if enough time has passed for certain milestones
    const daysSinceOnboarding = Math.floor((Date.now() - customer.onboarded_at.getTime()) / (24 * 60 * 60 * 1000));

    if (milestone.id === 'first_login' && daysSinceOnboarding > 1) {
      // Send reminder if customer hasn't logged in after 1 day
      await this.sendMilestoneReminder(customer, milestone);
    }
  }

  private async sendMilestoneReminder(customer: Customer, milestone: JourneyMilestone): Promise<void> {
    console.log(`📧 Sending milestone reminder to ${customer.email}: ${milestone.name}`);

    const reminderEvent = EventBuilder.create('aurora.milestone.reminder')
      .source('aurora-lc')
      .payload({
        customer_id: customer.id,
        milestone_id: milestone.id,
        milestone_name: milestone.name,
        reminder_type: 'automated'
      })
      .priority('low')
      .build();

    await this.eventBus.publish(reminderEvent);
  }

  private async executeScheduledActivities(): Promise<void> {
    const now = new Date();
    const scheduledActivities = Array.from(this.activities.values()).filter(a =>
      a.scheduled_at &&
      a.scheduled_at <= now &&
      !a.completed_at
    );

    for (const activity of scheduledActivities) {
      await this.executeActivity(activity);
    }
  }

  private async executeActivity(activity: EngagementActivity): Promise<void> {
    // Execute the scheduled activity
    activity.completed_at = new Date();
    activity.outcome = 'successful';

    console.log(`✅ Executed activity: ${activity.subject} for customer ${activity.customer_id}`);

    await this.persistActivities();

    // Publish activity completion event
    const completionEvent = EventBuilder.create('aurora.activity.completed')
      .source('aurora-lc')
      .payload({
        activity_id: activity.id,
        customer_id: activity.customer_id,
        type: activity.type,
        outcome: activity.outcome
      })
      .priority('low')
      .build();

    await this.eventBus.publish(completionEvent);
  }

  private async getAffectedCustomers(deploymentData: any): Promise<Customer[]> {
    // Determine which customers are affected by a deployment
    // For now, return all active customers
    return Array.from(this.customers.values()).filter(c => c.status === 'active');
  }

  private async sendUpdateNotification(customer: Customer, deploymentData: any): Promise<void> {
    console.log(`📧 Sending update notification to ${customer.email}: ${deploymentData.version}`);

    const activity: EngagementActivity = {
      id: `activity_${Date.now()}_${customer.id}`,
      customer_id: customer.id,
      type: 'email',
      subject: `Platform Update: ${deploymentData.version}`,
      description: 'Notification about platform update',
      outcome: 'successful',
      completed_at: new Date(),
      participants: ['system'],
      notes: 'Automated update notification',
      follow_up_actions: []
    };

    this.activities.set(activity.id, activity);
    await this.persistActivities();
  }

  private async calculateOverallCustomerHealth(): Promise<any> {
    const customers = Array.from(this.customers.values());
    const totalHealth = customers.reduce((sum, c) => sum + c.health_score, 0);
    const averageScore = customers.length > 0 ? totalHealth / customers.length : 0;

    return {
      average_score: averageScore,
      churn_risk: customers.filter(c => c.health_score < 40).length,
      expansion_opportunities: Array.from(this.healthMetrics.values())
        .flatMap(hm => hm.opportunities)
        .filter(opp => opp.type === 'upsell' || opp.type === 'expansion').length
    };
  }

  private async generateCustomerInsights(query: any): Promise<any> {
    // Generate insights based on customer data
    const insights = {
      customer_segments: this.analyzeCustomerSegments(),
      satisfaction_trends: this.analyzeSatisfactionTrends(),
      churn_predictors: this.identifyChurnPredictors(),
      growth_opportunities: this.identifyGrowthOpportunities()
    };

    return insights;
  }

  private analyzeCustomerSegments(): any {
    const customers = Array.from(this.customers.values());
    return {
      by_tier: {
        free: customers.filter(c => c.tier === 'free').length,
        starter: customers.filter(c => c.tier === 'starter').length,
        professional: customers.filter(c => c.tier === 'professional').length,
        enterprise: customers.filter(c => c.tier === 'enterprise').length
      },
      by_health: {
        healthy: customers.filter(c => c.health_score >= 80).length,
        at_risk: customers.filter(c => c.health_score < 60).length,
        critical: customers.filter(c => c.health_score < 40).length
      }
    };
  }

  private analyzeSatisfactionTrends(): any {
    const feedback = Array.from(this.feedback.values());
    const last30Days = feedback.filter(f =>
      f.created_at > new Date(Date.now() - (30 * 24 * 60 * 60 * 1000))
    );

    return {
      total_feedback: feedback.length,
      recent_feedback: last30Days.length,
      sentiment_distribution: {
        positive: last30Days.filter(f => f.sentiment === 'positive').length,
        neutral: last30Days.filter(f => f.sentiment === 'neutral').length,
        negative: last30Days.filter(f => f.sentiment === 'negative').length
      }
    };
  }

  private identifyChurnPredictors(): any {
    const atRiskCustomers = Array.from(this.customers.values()).filter(c => c.health_score < 60);

    return {
      at_risk_count: atRiskCustomers.length,
      common_risk_factors: ['low_engagement', 'support_issues', 'billing_problems'],
      predicted_churn_30_days: atRiskCustomers.filter(c => c.health_score < 40).length
    };
  }

  private identifyGrowthOpportunities(): any {
    const opportunities = Array.from(this.healthMetrics.values()).flatMap(hm => hm.opportunities);

    return {
      total_opportunities: opportunities.length,
      potential_revenue: opportunities.reduce((sum, opp) => sum + opp.potential_value, 0),
      by_type: {
        upsell: opportunities.filter(o => o.type === 'upsell').length,
        cross_sell: opportunities.filter(o => o.type === 'cross_sell').length,
        expansion: opportunities.filter(o => o.type === 'expansion').length,
        advocacy: opportunities.filter(o => o.type === 'advocacy').length
      }
    };
  }
}

export default AuroraLifecycle;