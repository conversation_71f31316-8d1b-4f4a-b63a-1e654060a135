#!/bin/bash

# ALIAS Project Scaffolding Script with DotAI Integration
# Creates a new ALIAS/MOSAIC project with full AI-powered development setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
PROJECT_NAME=""
PROJECT_TYPE="web-application"
TEMPLATE="mosaic-dotai"
SKIP_GIT=false
SKIP_INSTALL=false
AI_MODEL="claude-3-5-sonnet"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_ai() {
    echo -e "${MAGENTA}[AI]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
ALIAS Project Scaffolding with DotAI Integration

Creates a new ALIAS/MOSAIC project with AI-powered development workflow including:
- DotAI task management and documentation
- Claude Code CLI integration  
- Cursor IDE configuration
- MOSAIC agent integration
- 11-stage lifecycle automation

Usage: $0 [OPTIONS] <project-name>

OPTIONS:
    -t, --type TYPE         Project type (default: web-application)
                           Options: web-application, api-service, agent,
                                   mobile-app, cli-tool, library
    
    -T, --template TEMPLATE Template to use (default: mosaic-dotai)
                           Options: mosaic-dotai, minimal, enterprise
    
    -m, --model MODEL      AI model for Claude (default: claude-3-5-sonnet)
    
    --skip-git             Skip git initialization
    --skip-install         Skip npm install
    -h, --help            Show this help message

EXAMPLES:
    # Create a web application
    $0 my-awesome-app

    # Create an API service
    $0 -t api-service my-api

    # Create with enterprise template
    $0 -T enterprise my-enterprise-app

    # Quick creation without installs
    $0 --skip-install my-quick-project

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            PROJECT_TYPE="$2"
            shift 2
            ;;
        -T|--template)
            TEMPLATE="$2"
            shift 2
            ;;
        -m|--model)
            AI_MODEL="$2"
            shift 2
            ;;
        --skip-git)
            SKIP_GIT=true
            shift
            ;;
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            PROJECT_NAME="$1"
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$PROJECT_NAME" ]]; then
    print_error "Project name is required"
    show_usage
    exit 1
fi

# Validate project doesn't already exist
if [[ -d "$PROJECT_NAME" ]]; then
    print_error "Directory $PROJECT_NAME already exists"
    exit 1
fi

# Show configuration
echo "🚀 ALIAS Project Scaffolding with DotAI"
echo "======================================"
echo ""
echo "Project Name: $PROJECT_NAME"
echo "Project Type: $PROJECT_TYPE"
echo "Template: $TEMPLATE"
echo "AI Model: $AI_MODEL"
echo ""

# Create project directory
print_status "Creating project directory..."
mkdir -p "$PROJECT_NAME"
cd "$PROJECT_NAME"

# Create base directory structure
print_status "Creating ALIAS project structure..."
mkdir -p {src,tests/{unit,integration,e2e},docs/{features,architecture,api},scripts,config}
mkdir -p .claude/{commands,templates}
mkdir -p .dotai/{context,features,tasks}
mkdir -p .mosaic/{agents,lifecycles,workflows}
mkdir -p .vscode
mkdir -p .gitlab/{issue_templates,merge_request_templates}

# Create DotAI configuration
print_ai "Configuring DotAI integration..."
cat > .dotai/project.json << EOF
{
  "name": "$PROJECT_NAME",
  "type": "$PROJECT_TYPE",
  "version": "0.1.0",
  "created": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "integration": {
    "mosaic": {
      "enabled": true,
      "lifecycle": "01-discovery",
      "agents": ["flow_guardian", "code_reviewer", "test_generator"],
      "quantum_commands": true
    },
    "claude": {
      "model": "$AI_MODEL",
      "context_window": "enhanced",
      "custom_commands": true,
      "auto_documentation": true
    },
    "cursor": {
      "ai_mode": "advanced",
      "mosaic_snippets": true,
      "agent_assistance": true,
      "inline_suggestions": true
    }
  },
  "workflows": {
    "feature_development": "mosaic-11-stage",
    "task_management": "prism-icl-domains",
    "documentation": "ai-automated",
    "testing": "ai-assisted"
  },
  "settings": {
    "auto_commit": false,
    "branch_strategy": "feature-based",
    "pr_automation": true,
    "quality_gates": true
  }
}
EOF

# Create Claude configuration
print_ai "Setting up Claude Code configuration..."
cat > .claude/config.json << EOF
{
  "version": "1.0.0",
  "project": "$PROJECT_NAME",
  "model": "$AI_MODEL",
  "commands": {
    "feature": {
      "description": "Create a new feature with AI assistance",
      "workflow": "dotai-feature-creation"
    },
    "task": {
      "description": "Manage development tasks",
      "workflow": "mosaic-task-management"
    },
    "generate": {
      "description": "Generate code, docs, or tests",
      "templates": ["component", "api", "test", "doc"]
    },
    "review": {
      "description": "AI-powered code review",
      "agent": "code_reviewer"
    },
    "lifecycle": {
      "description": "Manage MOSAIC lifecycle progression",
      "stages": ["discovery", "qualification", "architecture", "development", "testing", "deployment"]
    }
  },
  "integrations": {
    "dotai": true,
    "mosaic": true,
    "cursor": true,
    "gitlab": true
  }
}
EOF

# Create MOSAIC agent configuration
print_status "Configuring MOSAIC agents..."
cat > .mosaic/agents/config.json << EOF
{
  "agents": [
    {
      "id": "flow_guardian",
      "name": "Flow Guardian",
      "domain": "ESD",
      "purpose": "Protect developer flow state",
      "triggers": ["deep_work", "focus_mode", "interruption_shield"],
      "dotai_integration": true
    },
    {
      "id": "code_reviewer",
      "name": "Code Reviewer",
      "domain": "SAD",
      "purpose": "Automated code review and suggestions",
      "triggers": ["pr_created", "code_pushed", "review_requested"],
      "quality_checks": ["style", "security", "performance", "best_practices"]
    },
    {
      "id": "test_generator",
      "name": "Test Generator",
      "domain": "SAD",
      "purpose": "Generate comprehensive test suites",
      "triggers": ["code_complete", "coverage_low", "test_requested"],
      "coverage_target": 80
    },
    {
      "id": "doc_curator",
      "name": "Documentation Curator",
      "domain": "KAD",
      "purpose": "Maintain up-to-date documentation",
      "triggers": ["feature_complete", "api_changed", "doc_requested"],
      "auto_update": true
    }
  ]
}
EOF

# Create package.json with DotAI scripts
print_status "Creating package.json..."
cat > package.json << EOF
{
  "name": "$PROJECT_NAME",
  "version": "0.1.0",
  "description": "ALIAS project with DotAI integration",
  "type": "module",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "test": "vitest",
    "test:watch": "vitest --watch",
    "test:coverage": "vitest --coverage",
    "lint": "eslint . --fix",
    "lint:check": "eslint .",
    "type-check": "tsc --noEmit",
    "validate": "npm run lint:check && npm run type-check && npm run test",
    "claude": "claude",
    "feature": "claude create feature",
    "task": "claude task",
    "generate:prd": "claude generate prd",
    "generate:docs": "claude generate docs",
    "lifecycle:current": "claude lifecycle status",
    "lifecycle:advance": "claude lifecycle advance",
    "agent": "mosaic agent",
    "agent:status": "mosaic agent status --all"
  },
  "dependencies": {
    "next": "^15.1.0",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@anthropic-ai/sdk": "^0.20.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^19.0.0",
    "@types/react-dom": "^19.0.0",
    "typescript": "^5.3.0",
    "eslint": "^8.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "vitest": "^1.0.0",
    "@vitest/coverage-v8": "^1.0.0",
    "prettier": "^3.0.0",
    "@mosaic/cli": "^1.0.0",
    "@claude/cli": "^1.0.0",
    "dotai": "^1.0.0"
  }
}
EOF

# Create TypeScript configuration
print_status "Creating TypeScript configuration..."
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/agents/*": ["./.mosaic/agents/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
EOF

# Create VS Code / Cursor configuration
print_status "Configuring Cursor IDE..."
cat > .vscode/settings.json << EOF
{
  "dotai.enabled": true,
  "dotai.features": {
    "contextAwareness": true,
    "taskTracking": true,
    "autoDocumentation": true,
    "featureManagement": true
  },
  "mosaic.integration": true,
  "mosaic.agents": {
    "enabled": true,
    "autoSuggest": true,
    "inlineAssistance": true,
    "codeReview": true
  },
  "ai.model": "$AI_MODEL",
  "ai.contextWindow": "enhanced",
  "ai.temperature": 0.7,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true
}
EOF

# Create initial feature structure
print_ai "Creating initial feature structure..."
cat > .dotai/features/initial-setup.json << EOF
{
  "id": "FEAT-001",
  "name": "initial-setup",
  "description": "Initial project setup and configuration",
  "status": "in_progress",
  "created": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "tasks": [
    {
      "id": "TASK-001",
      "description": "Configure development environment",
      "lifecycle": "01-discovery",
      "domain": "SAD",
      "status": "completed"
    },
    {
      "id": "TASK-002",
      "description": "Set up CI/CD pipeline",
      "lifecycle": "03-architecture",
      "domain": "SAD",
      "status": "pending"
    },
    {
      "id": "TASK-003",
      "description": "Create initial documentation",
      "lifecycle": "01-discovery",
      "domain": "KAD",
      "status": "pending"
    }
  ]
}
EOF

# Create README with AI assistance prompt
print_ai "Generating initial README..."
cat > README.md << EOF
# $PROJECT_NAME

> AI-powered ALIAS project with DotAI integration and MOSAIC lifecycle management

## 🚀 Quick Start

\`\`\`bash
# Install dependencies
npm install

# Start development with AI assistance
npm run dev

# Create a new feature
npm run feature "Add user authentication"

# Check current lifecycle stage
npm run lifecycle:current
\`\`\`

## 🤖 AI-Powered Development

This project includes full DotAI integration for AI-assisted development:

### Claude Commands
- \`claude create feature "<description>"\` - Create a new feature with AI-generated PRD
- \`claude create task "<description>"\` - Add a development task
- \`claude generate prd\` - Generate/update product requirements document
- \`claude review\` - Run AI code review on current changes

### MOSAIC Agents
- **Flow Guardian** - Protects your development flow
- **Code Reviewer** - Automated code quality checks
- **Test Generator** - AI-powered test creation
- **Doc Curator** - Keeps documentation up-to-date

### Task Management
All tasks are tracked through DotAI and mapped to MOSAIC lifecycle stages:
1. Discovery
2. Qualification
3. Architecture
4. Development
5. Testing
6. Deployment
7. Monitoring
8. Optimization
9. Scaling
10. Evolution
11. Sunset

## 📁 Project Structure

\`\`\`
$PROJECT_NAME/
├── .claude/          # Claude Code configuration
├── .dotai/           # DotAI task and feature management
├── .mosaic/          # MOSAIC agents and workflows
├── src/              # Source code
├── tests/            # Test suites
├── docs/             # Documentation
└── scripts/          # Build and utility scripts
\`\`\`

## 🔧 Development Workflow

1. **Create Feature**: \`claude create feature "Feature description"\`
2. **Work on Tasks**: Tasks are automatically created and tracked
3. **Get AI Help**: Use Cursor with inline AI assistance
4. **Review Code**: Automatic AI review on commits
5. **Generate Tests**: \`claude generate tests\`
6. **Update Docs**: Documentation updates automatically

## 📚 Documentation

- [Product Requirements](docs/PRD.md)
- [Architecture Guide](docs/architecture/README.md)
- [API Reference](docs/api/README.md)
- [Feature Documentation](docs/features/README.md)

## 🤝 Contributing

This project uses AI-assisted development. When contributing:

1. Use \`claude create feature\` for new features
2. Let AI agents review your code
3. Ensure all quality gates pass
4. Documentation is auto-generated

---

*Built with ❤️ using ALIAS MOSAIC and DotAI*
EOF

# Create .gitignore
print_status "Creating .gitignore..."
cat > .gitignore << 'EOF'
# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output/

# Next.js
.next/
out/
build/

# Production
dist/

# Misc
.DS_Store
*.pem
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json
.idea/
*.swp
*.swo

# DotAI
.dotai/context/current.json
.dotai/temp/

# MOSAIC
.mosaic/logs/
.mosaic/cache/

# Claude
.claude/history/
.claude/temp/
EOF

# Create initial source files based on project type
print_status "Creating initial source files..."
case $PROJECT_TYPE in
    web-application)
        mkdir -p src/app
        cat > src/app/page.tsx << 'EOF'
export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <h1 className="text-4xl font-bold">Welcome to ALIAS MOSAIC</h1>
      <p className="mt-4 text-xl">AI-powered development with DotAI integration</p>
    </main>
  );
}
EOF
        ;;
    api-service)
        mkdir -p src/routes
        cat > src/index.ts << 'EOF'
import { Hono } from 'hono';

const app = new Hono();

app.get('/', (c) => {
  return c.json({
    message: 'Welcome to ALIAS MOSAIC API',
    version: '0.1.0',
    features: ['dotai', 'mosaic', 'claude']
  });
});

export default app;
EOF
        ;;
    *)
        cat > src/index.ts << 'EOF'
// ALIAS MOSAIC Project with DotAI Integration
export const hello = () => {
  console.log('Hello from ALIAS MOSAIC with DotAI!');
};
EOF
        ;;
esac

# Initialize git repository
if [[ "$SKIP_GIT" == false ]]; then
    print_status "Initializing git repository..."
    git init
    git add .
    git commit -m "feat: initial ALIAS project with DotAI integration

- Complete MOSAIC project structure
- DotAI task management integration  
- Claude Code CLI configuration
- Cursor IDE setup with AI assistance
- MOSAIC agent configurations
- 11-stage lifecycle framework

🤖 Generated with ALIAS scaffolding script"
fi

# Install dependencies
if [[ "$SKIP_INSTALL" == false ]]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
else
    print_warning "Skipping npm install - run 'npm install' manually"
fi

# Final setup steps
print_ai "Running AI initialization..."
cat > .dotai/context/current.json << EOF
{
  "feature": "initial-setup",
  "task": "TASK-001",
  "lifecycle": "01-discovery",
  "agents": {
    "active": ["flow_guardian"],
    "available": ["code_reviewer", "test_generator", "doc_curator"]
  },
  "last_updated": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

# Success message
echo ""
print_success "🎉 ALIAS project created successfully!"
echo ""
echo "📁 Project: ${CYAN}$PROJECT_NAME${NC}"
echo "🤖 AI Model: ${MAGENTA}$AI_MODEL${NC}"
echo "🔧 Type: ${BLUE}$PROJECT_TYPE${NC}"
echo ""
echo "Next steps:"
echo "  1. ${GREEN}cd $PROJECT_NAME${NC}"
echo "  2. ${GREEN}cursor .${NC} (or code . for VS Code)"
echo "  3. ${GREEN}npm run dev${NC}"
echo ""
echo "AI Commands:"
echo "  • ${MAGENTA}claude create feature \"Your feature description\"${NC}"
echo "  • ${MAGENTA}claude generate prd${NC}"
echo "  • ${MAGENTA}claude task list${NC}"
echo ""
echo "📚 Documentation: ${BLUE}docs/README.md${NC}"
echo "🤝 Start building with AI! Use natural language with Claude."
echo ""
echo "${CYAN}Happy coding with ALIAS MOSAIC + DotAI! 🚀${NC}"