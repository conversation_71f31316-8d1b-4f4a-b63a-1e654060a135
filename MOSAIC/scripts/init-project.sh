#!/bin/bash

# ALIAS MOSAIC Project Initialization Script
# This script sets up a new MOSAIC project with all necessary configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PROJECT_NAME=""
PROJECT_TYPE="web-application"
GITLAB_GROUP="alias"
SKIP_GITLAB=false
SKIP_CONVEX=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
ALIAS MOSAIC Project Initialization Script

Usage: $0 [OPTIONS]

OPTIONS:
    -n, --name PROJECT_NAME     Project name (required)
    -t, --type PROJECT_TYPE     Project type: web-application, api-service, 
                               mobile-application, documentation-site, agent
                               (default: web-application)
    -g, --group GITLAB_GROUP    GitLab group (default: alias)
    --skip-gitlab              Skip GitLab repository creation
    --skip-convex              Skip Convex database setup
    -h, --help                 Show this help message

EXAMPLES:
    $0 -n my-mosaic-app
    $0 -n my-api-service -t api-service
    $0 -n my-docs-site -t documentation-site --skip-convex

PROJECT TYPES:
    web-application     Next.js web application with Convex
    api-service         Hono API service with PostgreSQL
    mobile-application  React Native application
    documentation-site  Fumadocs documentation site
    agent              MOSAIC agent with PRISM-ICL integration

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            PROJECT_NAME="$2"
            shift 2
            ;;
        -t|--type)
            PROJECT_TYPE="$2"
            shift 2
            ;;
        -g|--group)
            GITLAB_GROUP="$2"
            shift 2
            ;;
        --skip-gitlab)
            SKIP_GITLAB=true
            shift
            ;;
        --skip-convex)
            SKIP_CONVEX=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$PROJECT_NAME" ]]; then
    print_error "Project name is required"
    show_usage
    exit 1
fi

# Validate project type
case $PROJECT_TYPE in
    web-application|api-service|mobile-application|documentation-site|agent)
        ;;
    *)
        print_error "Invalid project type: $PROJECT_TYPE"
        show_usage
        exit 1
        ;;
esac

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is required but not installed"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $NODE_VERSION -lt 20 ]]; then
        print_error "Node.js 20+ is required (found: $(node --version))"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is required but not installed"
        exit 1
    fi
    
    # Check git
    if ! command -v git &> /dev/null; then
        print_error "git is required but not installed"
        exit 1
    fi
    
    # Check GitLab CLI if not skipping GitLab
    if [[ $SKIP_GITLAB == false ]] && ! command -v glab &> /dev/null; then
        print_warning "GitLab CLI not found. Install with: npm install -g @gitlab/cli"
        print_warning "Or use --skip-gitlab to skip repository creation"
    fi
    
    print_success "Prerequisites check completed"
}

# Create project directory structure
create_project_structure() {
    print_status "Creating project structure for $PROJECT_NAME..."
    
    if [[ -d "$PROJECT_NAME" ]]; then
        print_error "Directory $PROJECT_NAME already exists"
        exit 1
    fi
    
    mkdir -p "$PROJECT_NAME"
    cd "$PROJECT_NAME"
    
    # Create base directory structure
    mkdir -p {.gitlab/{issue_templates,merge_request_templates,ci},docs/{architecture,guides,api},src,tests/{unit,integration,e2e},scripts,config,.vscode}
    
    # Create project-type specific structure
    case $PROJECT_TYPE in
        web-application)
            mkdir -p {src/{app,components,lib,types},public,styles}
            ;;
        api-service)
            mkdir -p {src/{routes,middleware,services,models},migrations,seeds}
            ;;
        mobile-application)
            mkdir -p {src/{screens,components,navigation,services},assets/{images,fonts}}
            ;;
        documentation-site)
            mkdir -p {content/{guides,api,reference},components/docs}
            ;;
        agent)
            mkdir -p {src/{agents,domains,commands,tools},config/agents}
            ;;
    esac
    
    print_success "Project structure created"
}

# Initialize package.json
init_package_json() {
    print_status "Initializing package.json..."
    
    cat > package.json << EOF
{
  "name": "@alias/$PROJECT_NAME",
  "version": "0.1.0",
  "description": "MOSAIC $PROJECT_TYPE project",
  "keywords": ["mosaic", "alias", "$PROJECT_TYPE"],
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "eslint . --ext .ts,.tsx,.js,.jsx --fix",
    "lint:check": "eslint . --ext .ts,.tsx,.js,.jsx",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "test:watch": "vitest --watch",
    "validate": "npm run lint:check && npm run type-check && npm run test"
  },
  "dependencies": {},
  "devDependencies": {}
}
EOF
    
    # Add project-type specific dependencies
    case $PROJECT_TYPE in
        web-application)
            npm install next@15.1.0 react@19.0.0 react-dom@19.0.0 @types/react @types/react-dom typescript tailwindcss
            if [[ $SKIP_CONVEX == false ]]; then
                npm install convex @convex-dev/react
            fi
            ;;
        api-service)
            npm install hono @hono/node-server zod
            npm install --save-dev @types/node typescript tsx
            ;;
        documentation-site)
            npm install fumadocs-ui fumadocs-core next@15.1.0 react@19.0.0
            ;;
        agent)
            npm install @anthropic-ai/sdk openai zod
            ;;
    esac
    
    # Add common dev dependencies
    npm install --save-dev eslint prettier @typescript-eslint/eslint-plugin @typescript-eslint/parser vitest
    
    print_success "package.json initialized"
}

# Create configuration files
create_config_files() {
    print_status "Creating configuration files..."
    
    # TypeScript config
    cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
EOF
    
    # ESLint config
    cat > .eslintrc.json << 'EOF'
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn"
  }
}
EOF
    
    # Prettier config
    cat > .prettierrc << 'EOF'
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": false,
  "printWidth": 80,
  "tabWidth": 2
}
EOF
    
    # Environment file
    cat > .env.example << 'EOF'
# MOSAIC Project Environment Configuration

# Application
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=MOSAIC Project
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database (if applicable)
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# AI Services (if applicable)
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# GitLab Integration
GITLAB_TOKEN=your-gitlab-token
**********************-project-id

# Monitoring
SENTRY_DSN=your-sentry-dsn
EOF
    
    print_success "Configuration files created"
}

# Create GitLab CI/CD configuration
create_gitlab_ci() {
    print_status "Creating GitLab CI/CD configuration..."
    
    cat > .gitlab-ci.yml << 'EOF'
stages:
  - validate
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "20"

validate:
  stage: validate
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run lint:check
    - npm run type-check
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

test:
  stage: test
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run test
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

build:
  stage: build
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run build
  artifacts:
    paths:
      - .next/
    expire_in: 1 hour
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
EOF
    
    print_success "GitLab CI/CD configuration created"
}

# Create README
create_readme() {
    print_status "Creating README.md..."
    
    cat > README.md << EOF
# $PROJECT_NAME

MOSAIC $PROJECT_TYPE built with the ALIAS framework.

## 🚀 Quick Start

### Prerequisites

- Node.js 20+
- npm or yarn

### Installation

\`\`\`bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development server
npm run dev
\`\`\`

## 📋 Available Scripts

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run start\` - Start production server
- \`npm run lint\` - Run ESLint
- \`npm run type-check\` - Run TypeScript checking
- \`npm run test\` - Run tests
- \`npm run validate\` - Run all validation checks

## 🏗️ Project Structure

\`\`\`
$PROJECT_NAME/
├── src/                 # Source code
├── docs/                # Documentation
├── tests/               # Test files
├── scripts/             # Build and deployment scripts
└── config/              # Configuration files
\`\`\`

## 🔧 Development

This project follows MOSAIC architecture principles:

1. **Modularity First**: Self-contained, replaceable components
2. **Real-Time by Default**: Reactive, synchronized data flows
3. **AI-Augmented**: Every workflow enhanced by AI agents
4. **GitLab-Centric**: Single source of truth for development

## 📚 Documentation

- [MOSAIC Architecture](https://docs.alias.dev/mosaic)
- [Development Guide](docs/guides/development.md)
- [API Reference](docs/api/README.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for your changes
5. Run validation: \`npm run validate\`
6. Create a merge request

## 📄 License

This project is licensed under the MIT License.
EOF
    
    print_success "README.md created"
}

# Initialize Git repository
init_git() {
    print_status "Initializing Git repository..."
    
    git init
    
    cat > .gitignore << 'EOF'
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/

# Testing
coverage/
.nyc_output/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history
EOF
    
    git add .
    git commit -m "feat: initial MOSAIC $PROJECT_TYPE project setup

- Add project structure and configuration
- Add package.json with dependencies
- Add TypeScript, ESLint, and Prettier configuration
- Add GitLab CI/CD pipeline
- Add comprehensive README and documentation

🤖 Generated with ALIAS MOSAIC initialization script"
    
    print_success "Git repository initialized"
}

# Create GitLab repository
create_gitlab_repo() {
    if [[ $SKIP_GITLAB == true ]]; then
        print_warning "Skipping GitLab repository creation"
        return
    fi
    
    if ! command -v glab &> /dev/null; then
        print_warning "GitLab CLI not found. Skipping repository creation."
        print_warning "Create repository manually at: https://gitlab.alias.dev/$GITLAB_GROUP/$PROJECT_NAME"
        return
    fi
    
    print_status "Creating GitLab repository..."
    
    # Create GitLab repository
    glab repo create "$GITLAB_GROUP/$PROJECT_NAME" \
        --description "MOSAIC $PROJECT_TYPE project" \
        --visibility internal \
        --defaultBranch main
    
    # Add GitLab remote
    git remote add origin "********************:$GITLAB_GROUP/$PROJECT_NAME.git"
    
    # Push to GitLab
    git push -u origin main
    
    print_success "GitLab repository created and pushed"
}

# Setup Convex database
setup_convex() {
    if [[ $SKIP_CONVEX == true ]] || [[ $PROJECT_TYPE == "api-service" ]]; then
        print_warning "Skipping Convex setup"
        return
    fi
    
    print_status "Setting up Convex database..."
    
    # Create Convex directory and schema
    mkdir -p convex
    
    cat > convex/schema.ts << 'EOF'
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Example table - customize for your needs
  documents: defineTable({
    title: v.string(),
    content: v.string(),
    author: v.string(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_author", ["author"]),
});
EOF
    
    print_success "Convex setup completed"
    print_warning "Run 'npx convex dev' to start Convex development"
}

# Main execution
main() {
    echo "🚀 ALIAS MOSAIC Project Initialization"
    echo "======================================"
    echo ""
    echo "Project Name: $PROJECT_NAME"
    echo "Project Type: $PROJECT_TYPE"
    echo "GitLab Group: $GITLAB_GROUP"
    echo ""
    
    check_prerequisites
    create_project_structure
    init_package_json
    create_config_files
    create_gitlab_ci
    create_readme
    init_git
    setup_convex
    create_gitlab_repo
    
    echo ""
    echo "🎉 Project initialization completed!"
    echo ""
    echo "Next steps:"
    echo "1. cd $PROJECT_NAME"
    echo "2. cp .env.example .env"
    echo "3. Edit .env with your configuration"
    echo "4. npm run dev"
    echo ""
    echo "📚 Documentation: https://docs.alias.dev/mosaic"
    echo "🔗 Repository: https://gitlab.alias.dev/$GITLAB_GROUP/$PROJECT_NAME"
    echo ""
    echo "Happy coding! 🚀"
}

# Run main function
main "$@"