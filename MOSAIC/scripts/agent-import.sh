#!/bin/bash

# ALIAS MOSAIC Agent Import Script
# Import Letta AI agent files (.af) into MOSAIC ecosystem

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
AGENT_FILE=""
DOMAIN=""
LIFECYCLE=""
VALIDATE_ONLY=false
OUTPUT_DIR="./agents/imported"
CHECKPOINT=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
ALIAS MOSAIC Agent Import Script

Import Letta AI agent files (.af) into the MOSAIC ecosystem with automatic
domain classification, lifecycle assignment, and quantum command generation.

Usage: $0 [OPTIONS] <agent-file.af>

OPTIONS:
    -d, --domain DOMAIN      Override PRISM-ICL domain assignment
                            (SAD, KAD, IKD, MLD, TKD, MCD, ESD, UPD)
    -l, --lifecycle STAGE    Assign to specific lifecycle stage (1-11)
    -v, --validate          Validate agent file without importing
    -o, --output DIR        Output directory (default: ./agents/imported)
    -c, --checkpoint        Create GitLab checkpoint after import
    -h, --help             Show this help message

EXAMPLES:
    # Import with automatic domain detection
    $0 research_assistant.af

    # Import with specific domain and lifecycle
    $0 -d KAD -l 3 knowledge_agent.af

    # Validate agent file before import
    $0 --validate agent.af

    # Import with GitLab checkpoint
    $0 --checkpoint production_agent.af

AGENT FILE FORMAT:
    The Letta agent file (.af) should be a valid JSON file containing:
    - Agent metadata (name, description, timestamps)
    - Model configuration (provider, parameters)
    - Memory (messages, blocks)
    - System prompts and rules
    - Tool definitions
    - Environment variables

DOMAIN MAPPING:
    SAD - System Architecture Domain
    KAD - Knowledge Architecture Domain
    IKD - Inferential Knowledge Domain
    MLD - Meta-Learning Domain
    TKD - Temporal Knowledge Domain
    MCD - Multi-Criteria Decision Domain
    ESD - Emergent Systems Domain
    UPD - Unified Persistence Domain

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -l|--lifecycle)
            LIFECYCLE="$2"
            shift 2
            ;;
        -v|--validate)
            VALIDATE_ONLY=true
            shift
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -c|--checkpoint)
            CHECKPOINT=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            AGENT_FILE="$1"
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$AGENT_FILE" ]]; then
    print_error "Agent file is required"
    show_usage
    exit 1
fi

if [[ ! -f "$AGENT_FILE" ]]; then
    print_error "Agent file not found: $AGENT_FILE"
    exit 1
fi

# Validate domain if specified
if [[ -n "$DOMAIN" ]]; then
    case $DOMAIN in
        SAD|KAD|IKD|MLD|TKD|MCD|ESD|UPD)
            ;;
        *)
            print_error "Invalid domain: $DOMAIN"
            show_usage
            exit 1
            ;;
    esac
fi

# Validate lifecycle if specified
if [[ -n "$LIFECYCLE" ]]; then
    if ! [[ "$LIFECYCLE" =~ ^[1-9]$|^1[0-1]$ ]]; then
        print_error "Invalid lifecycle stage: $LIFECYCLE (must be 1-11)"
        exit 1
    fi
fi

# Function to validate agent file structure
validate_agent_file() {
    local file="$1"
    
    print_status "Validating agent file structure..."
    
    # Check if file is valid JSON
    if ! jq empty "$file" 2>/dev/null; then
        print_error "Invalid JSON format"
        return 1
    fi
    
    # Check required fields
    local required_fields=("version" "metadata" "model" "memory" "system")
    for field in "${required_fields[@]}"; do
        if ! jq -e ".$field" "$file" >/dev/null 2>&1; then
            print_error "Missing required field: $field"
            return 1
        fi
    done
    
    # Check metadata fields
    if ! jq -e ".metadata.name" "$file" >/dev/null 2>&1; then
        print_error "Missing required metadata.name"
        return 1
    fi
    
    # Check model configuration
    if ! jq -e ".model.provider" "$file" >/dev/null 2>&1; then
        print_error "Missing required model.provider"
        return 1
    fi
    
    print_success "Agent file validation passed"
    return 0
}

# Function to analyze agent for domain classification
analyze_agent_domain() {
    local file="$1"
    
    print_status "Analyzing agent for domain classification..."
    
    # Extract content for analysis
    local tools=$(jq -r '.tools[]?.name // empty' "$file" 2>/dev/null | tr '\n' ' ')
    local prompts=$(jq -r '.system.prompts[]?.content // empty' "$file" 2>/dev/null | tr '\n' ' ')
    local description=$(jq -r '.metadata.description // empty' "$file" 2>/dev/null)
    
    local all_content="${tools} ${prompts} ${description}"
    
    # Domain detection logic
    if [[ "$all_content" =~ (system|architecture|infrastructure) ]]; then
        echo "SAD"
    elif [[ "$all_content" =~ (knowledge|search|retrieval|information) ]]; then
        echo "KAD"
    elif [[ "$all_content" =~ (inference|reasoning|logic|deduction) ]]; then
        echo "IKD"
    elif [[ "$all_content" =~ (learning|training|optimization|improvement) ]]; then
        echo "MLD"
    elif [[ "$all_content" =~ (temporal|time|schedule|sequence) ]]; then
        echo "TKD"
    elif [[ "$all_content" =~ (decision|choice|criteria|evaluation) ]]; then
        echo "MCD"
    elif [[ "$all_content" =~ (emergent|pattern|complex|adaptive) ]]; then
        echo "ESD"
    elif [[ "$all_content" =~ (persistence|storage|memory|state) ]]; then
        echo "UPD"
    else
        echo "KAD" # Default to Knowledge Architecture Domain
    fi
}

# Function to determine lifecycle stage
determine_lifecycle_stage() {
    local file="$1"
    
    # Count messages and tools
    local message_count=$(jq '.memory.messages | length' "$file" 2>/dev/null || echo 0)
    local tool_count=$(jq '.tools | length' "$file" 2>/dev/null || echo 0)
    
    # Determine stage based on agent maturity
    if [[ $message_count -lt 10 && $tool_count -lt 3 ]]; then
        echo 1  # Discovery
    elif [[ $message_count -lt 50 && $tool_count -lt 5 ]]; then
        echo 2  # Qualification
    elif [[ $message_count -lt 100 ]]; then
        echo 3  # Architecture
    elif [[ $message_count -lt 500 ]]; then
        echo 4  # Development
    elif [[ $message_count -lt 1000 ]]; then
        echo 5  # Testing
    else
        echo 6  # Deployment
    fi
}

# Function to import agent
import_agent() {
    local file="$1"
    local agent_name=$(jq -r '.metadata.name' "$file")
    local safe_name=$(echo "$agent_name" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9-]/-/g')
    
    print_status "Importing agent: $agent_name"
    
    # Determine domain and lifecycle
    local detected_domain=$(analyze_agent_domain "$file")
    local detected_lifecycle=$(determine_lifecycle_stage "$file")
    
    local final_domain="${DOMAIN:-$detected_domain}"
    local final_lifecycle="${LIFECYCLE:-$detected_lifecycle}"
    
    print_status "Domain: $final_domain (${DOMAIN:+specified}${DOMAIN:-detected})"
    print_status "Lifecycle: Stage $final_lifecycle (${LIFECYCLE:+specified}${LIFECYCLE:-detected})"
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    
    # Generate MOSAIC agent configuration
    local timestamp=$(date +%s)
    local mosaic_id="mosaic-${safe_name}-${timestamp}"
    local output_file="$OUTPUT_DIR/${mosaic_id}.json"
    
    print_status "Generating MOSAIC agent configuration..."
    
    # Create MOSAIC agent structure
    cat > "$output_file" << EOF
{
  "id": "$mosaic_id",
  "name": "$agent_name",
  "description": $(jq '.metadata.description // ""' "$file"),
  "domain": {
    "primary": "$final_domain",
    "secondary": []
  },
  "lifecycle": {
    "stage": $final_lifecycle,
    "substage": "imported"
  },
  "metadata": {
    "created": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "updated": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "version": "1.0.0",
    "source": {
      "type": "letta-af",
      "file": "$(basename "$file")",
      "importDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
    }
  },
  "originalAgent": $(cat "$file")
}
EOF
    
    print_success "Agent imported successfully: $mosaic_id"
    print_status "Output file: $output_file"
    
    # Create agent directory structure
    local agent_dir="$OUTPUT_DIR/$mosaic_id"
    mkdir -p "$agent_dir"/{config,memory,tools,checkpoints}
    
    # Extract and save components
    jq '.memory' "$file" > "$agent_dir/memory/initial.json"
    jq '.tools // []' "$file" > "$agent_dir/tools/definitions.json"
    jq '.system' "$file" > "$agent_dir/config/system.json"
    
    # Generate quantum commands
    generate_quantum_commands "$file" "$agent_dir/config/quantum-commands.json"
    
    # Create README
    cat > "$agent_dir/README.md" << EOF
# $agent_name

**MOSAIC Agent ID**: $mosaic_id  
**Domain**: $final_domain  
**Lifecycle Stage**: $final_lifecycle  
**Import Date**: $(date)  

## Overview

This agent was imported from a Letta AI agent file (.af) and has been converted
to the MOSAIC agent format with PRISM-ICL domain classification and quantum
command generation.

## Original Description

$(jq -r '.metadata.description // "No description provided"' "$file")

## Capabilities

$(jq -r '.tools[]?.description // empty' "$file" 2>/dev/null | sed 's/^/- /')

## Usage

\`\`\`bash
# Load agent
mosaic agent load $mosaic_id

# Execute quantum command
mosaic agent exec $mosaic_id !STATUS

# View agent details
mosaic agent info $mosaic_id
\`\`\`

## Files

- \`config/system.json\` - System prompts and rules
- \`config/quantum-commands.json\` - Generated quantum commands
- \`memory/initial.json\` - Initial memory state
- \`tools/definitions.json\` - Tool definitions
- \`checkpoints/\` - Agent state checkpoints

## Domain Classification

Primary Domain: **$final_domain**

This classification was based on analysis of:
- Tool definitions and capabilities
- System prompts and behavioral rules
- Agent description and metadata

## Lifecycle Stage

Current Stage: **$final_lifecycle**

This assignment was based on:
- Message history length: $(jq '.memory.messages | length' "$file") messages
- Tool count: $(jq '.tools | length // 0' "$file") tools
- Overall agent maturity indicators
EOF
    
    # Create GitLab checkpoint if requested
    if [[ "$CHECKPOINT" == true ]]; then
        create_gitlab_checkpoint "$mosaic_id" "$agent_dir"
    fi
}

# Function to generate quantum commands
generate_quantum_commands() {
    local file="$1"
    local output="$2"
    
    print_status "Generating quantum commands..."
    
    # Extract tools and generate commands
    jq '[
        .tools[]? | {
            command: ("!" + (.name | ascii_upcase)),
            description: .description,
            handler: ("handle" + .name),
            parameters: .parameters
        }
    ] + [
        {
            command: "!STATUS",
            description: "Get agent status and health",
            handler: "handleStatus",
            parameters: {}
        },
        {
            command: "!HELP",
            description: "Show available commands",
            handler: "handleHelp",
            parameters: {}
        }
    ]' "$file" > "$output"
}

# Function to create GitLab checkpoint
create_gitlab_checkpoint() {
    local agent_id="$1"
    local agent_dir="$2"
    
    print_status "Creating GitLab checkpoint..."
    
    if ! command -v git &> /dev/null; then
        print_warning "Git not installed, skipping checkpoint"
        return
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        print_warning "Not in a git repository, skipping checkpoint"
        return
    fi
    
    # Create checkpoint branch
    local branch="agent-checkpoint/$agent_id"
    git checkout -b "$branch" 2>/dev/null || git checkout "$branch"
    
    # Add agent files
    git add "$agent_dir"
    
    # Commit checkpoint
    git commit -m "checkpoint: Import agent $agent_id

- Source: Letta AI agent file
- Domain: $(jq -r '.domain.primary' "$OUTPUT_DIR/${agent_id}.json")
- Lifecycle: Stage $(jq -r '.lifecycle.stage' "$OUTPUT_DIR/${agent_id}.json")
- Import date: $(date)

🤖 Generated by MOSAIC agent import script"
    
    print_success "GitLab checkpoint created on branch: $branch"
}

# Main execution
main() {
    echo "🤖 ALIAS MOSAIC Agent Import"
    echo "============================"
    echo ""
    echo "Agent File: $AGENT_FILE"
    echo ""
    
    # Validate agent file
    if ! validate_agent_file "$AGENT_FILE"; then
        exit 1
    fi
    
    # Extract agent name
    local agent_name=$(jq -r '.metadata.name' "$AGENT_FILE")
    print_status "Agent Name: $agent_name"
    
    if [[ "$VALIDATE_ONLY" == true ]]; then
        print_success "Validation complete - agent file is valid"
        
        # Show detected values
        local detected_domain=$(analyze_agent_domain "$AGENT_FILE")
        local detected_lifecycle=$(determine_lifecycle_stage "$AGENT_FILE")
        
        echo ""
        echo "Detected Configuration:"
        echo "- Domain: $detected_domain"
        echo "- Lifecycle Stage: $detected_lifecycle"
        echo "- Tools: $(jq '.tools | length // 0' "$AGENT_FILE")"
        echo "- Messages: $(jq '.memory.messages | length' "$AGENT_FILE")"
        
        exit 0
    fi
    
    # Import agent
    import_agent "$AGENT_FILE"
    
    echo ""
    echo "🎉 Agent import completed!"
    echo ""
    echo "Next steps:"
    echo "1. Review the imported agent in: $OUTPUT_DIR"
    echo "2. Test the agent: mosaic agent test $mosaic_id"
    echo "3. Deploy to environment: mosaic agent deploy $mosaic_id --env development"
    echo ""
}

# Run main function
main "$@"