# APEX-LC Configuration
# Autonomous Persona-Enhanced eXecution Lifecycle

lifecycle:
  id: "apex-lc"
  name: "APEX-LC"
  full_name: "Autonomous Persona-Enhanced eXecution Lifecycle"
  version: "1.0.0"
  status: "active"
  phase: 1
  priority: 3

# Mission and Objectives
mission:
  primary: "Idea → Production pipeline in <8 hours"
  success_rate: "95%"
  automation_level: "95%"
  quality_threshold: 8.0

# Domain Scope
domain:
  primary: "Software Development"
  secondary: "Technical Execution"
  responsibilities:
    - "Automated development workflows"
    - "CI/CD orchestration and deployment"
    - "Code quality and security validation"
    - "Technical debt management"
    - "Performance optimization"
    - "Testing automation"

# AI Personas
personas:
  analyst:
    name: "Technical Analyst"
    role: "Requirements analysis and technical feasibility"
    model: "claude-3.5-sonnet"
    capabilities:
      - "Requirement extraction and clarification"
      - "Technical feasibility assessment"
      - "Architecture recommendation"
      - "Risk analysis"
    system_prompt: |
      You are a senior technical analyst specializing in rapid requirement analysis and technical feasibility assessment.
      Your goal is to quickly understand requirements and provide clear technical recommendations within 30 minutes.
      Focus on practical solutions that can be implemented within the 8-hour APEX pipeline.

  product_lead:
    name: "Product Lead"
    role: "Product strategy and user experience"
    model: "claude-3.5-sonnet"
    capabilities:
      - "User story creation"
      - "Feature prioritization"
      - "UX/UI guidance"
      - "Product roadmap alignment"
    system_prompt: |
      You are a product lead focused on rapid product development and user experience optimization.
      Your role is to ensure features align with user needs and business objectives while maintaining development velocity.
      Prioritize MVP features that deliver maximum value within the 8-hour development window.

  designer:
    name: "UX/UI Designer"
    role: "User interface and experience design"
    model: "claude-3.5-sonnet"
    capabilities:
      - "UI component design"
      - "User flow optimization"
      - "Accessibility compliance"
      - "Design system adherence"
    system_prompt: |
      You are a UX/UI designer specializing in rapid prototyping and design system implementation.
      Focus on creating intuitive, accessible interfaces using established design patterns and components.
      Ensure designs can be implemented quickly within the APEX development timeline.

  architect:
    name: "Solution Architect"
    role: "Technical architecture and system design"
    model: "claude-3.5-sonnet"
    capabilities:
      - "System architecture design"
      - "Technology stack selection"
      - "Integration planning"
      - "Scalability assessment"
    system_prompt: |
      You are a solution architect specializing in rapid system design and technology selection.
      Design scalable, maintainable architectures using proven patterns and technologies.
      Prioritize solutions that can be implemented quickly while maintaining long-term viability.

  developer:
    name: "Full-Stack Developer"
    role: "Code implementation and testing"
    model: "claude-3.5-sonnet"
    capabilities:
      - "Full-stack development"
      - "Test-driven development"
      - "Code review and optimization"
      - "Deployment automation"
    system_prompt: |
      You are a senior full-stack developer specializing in rapid, high-quality code implementation.
      Use test-driven development and follow established coding standards and patterns.
      Focus on clean, maintainable code that can be deployed quickly and safely.

# Workflow Configuration
workflows:
  idea_to_mvp:
    name: "Idea to MVP Pipeline"
    total_time: "8 hours"
    success_rate: "95%"
    phases:
      discovery:
        duration: "30 minutes"
        personas: ["analyst", "product_lead"]
        deliverables:
          - "Technical specification"
          - "User stories"
          - "Architecture overview"
          - "Risk assessment"
      
      architecture:
        duration: "45 minutes"
        personas: ["architect", "designer"]
        deliverables:
          - "System design"
          - "API specification"
          - "Database schema"
          - "UI/UX mockups"
      
      development:
        duration: "4-5 hours"
        personas: ["developer"]
        deliverables:
          - "Feature implementation"
          - "Test suite"
          - "Documentation"
          - "Code review"
      
      testing:
        duration: "1 hour"
        personas: ["developer"]
        deliverables:
          - "Unit tests"
          - "Integration tests"
          - "Performance tests"
          - "Security scan"
      
      deployment:
        duration: "1 hour"
        personas: ["developer"]
        deliverables:
          - "Production deployment"
          - "Health monitoring"
          - "Performance metrics"
          - "Rollback plan"

# Technology Stack
tech_stack:
  frontend:
    framework: "next-15"
    ui_library: "react-19"
    styling: "tailwind-4"
    state_management: "zustand"
    testing: "vitest"
  
  backend:
    framework: "hono"
    api_layer: "trpc"
    database: "convex"
    authentication: "nextauth"
    validation: "zod"
  
  infrastructure:
    containerization: "docker"
    orchestration: "kubernetes"
    ci_cd: "gitlab"
    monitoring: "prometheus"
    logging: "grafana"
  
  ai_tools:
    primary_model: "claude-3.5-sonnet"
    code_generation: "github-copilot"
    testing: "automated-test-generation"
    documentation: "ai-doc-generation"

# Integration Points
integrations:
  prism_lc:
    purpose: "Knowledge retrieval and documentation"
    events:
      subscribes:
        - "prism.knowledge.updated"
        - "prism.pattern.recognized"
      publishes:
        - "apex.documentation.required"
        - "apex.knowledge.captured"
  
  aurora_lc:
    purpose: "Customer feedback and feature requests"
    events:
      subscribes:
        - "aurora.feature.requested"
        - "aurora.feedback.received"
      publishes:
        - "apex.feature.delivered"
        - "apex.deployment.completed"
  
  shield_lc:
    purpose: "Security requirements and compliance"
    events:
      subscribes:
        - "shield.security.requirement"
        - "shield.vulnerability.detected"
      publishes:
        - "apex.security.scan.completed"
        - "apex.compliance.validated"
  
  quantum_lc:
    purpose: "Resource allocation and cost optimization"
    events:
      subscribes:
        - "quantum.resource.allocated"
        - "quantum.budget.updated"
      publishes:
        - "apex.resource.requested"
        - "apex.cost.estimated"
  
  pulse_lc:
    purpose: "System coordination and monitoring"
    events:
      subscribes:
        - "pulse.coordination.required"
        - "pulse.optimization.suggested"
      publishes:
        - "apex.health.status"
        - "apex.metrics.updated"

# Quality Gates
quality_gates:
  code_quality:
    threshold: 8.0
    metrics:
      - "sonar_quality_gate"
      - "technical_debt_ratio"
      - "code_coverage"
      - "maintainability_index"
  
  performance:
    threshold: 90
    metrics:
      - "lighthouse_score"
      - "web_vitals"
      - "api_response_time"
      - "bundle_size"
  
  security:
    threshold: "A"
    metrics:
      - "security_rating"
      - "vulnerability_count"
      - "dependency_check"
      - "secrets_detection"

# Success Metrics
metrics:
  velocity:
    idea_to_production: "<8 hours"
    deployment_frequency: "multiple per day"
    lead_time: "<4 hours"
    change_failure_rate: "<5%"
  
  quality:
    first_time_deployment_success: ">95%"
    test_coverage: ">90%"
    bug_escape_rate: "<1%"
    customer_satisfaction: ">95%"
  
  efficiency:
    automation_rate: ">95%"
    manual_intervention: "<5%"
    resource_utilization: ">80%"
    cost_per_feature: "decreasing"

# Event Definitions
events:
  publishes:
    - "apex.deployment.started"
    - "apex.deployment.completed"
    - "apex.deployment.failed"
    - "apex.test.completed"
    - "apex.quality.gate.passed"
    - "apex.quality.gate.failed"
    - "apex.feature.delivered"
    - "apex.security.scan.completed"
    - "apex.performance.baseline.established"
  
  subscribes:
    - "feature.requested"
    - "security.requirement.updated"
    - "resource.allocated"
    - "knowledge.updated"
    - "customer.feedback.received"

# Automation Rules
automation:
  triggers:
    - event: "feature.requested"
      action: "start_development_workflow"
      conditions:
        - "requirements_complete"
        - "resources_available"
    
    - event: "code.committed"
      action: "run_ci_pipeline"
      conditions:
        - "branch_is_feature"
        - "tests_exist"
    
    - event: "quality.gate.passed"
      action: "deploy_to_staging"
      conditions:
        - "all_tests_pass"
        - "security_scan_clean"
    
    - event: "staging.validated"
      action: "deploy_to_production"
      conditions:
        - "manual_approval"
        - "deployment_window"

# Monitoring and Alerting
monitoring:
  health_checks:
    - "deployment_pipeline_status"
    - "test_execution_health"
    - "code_quality_trends"
    - "performance_metrics"
  
  alerts:
    - condition: "deployment_failure_rate > 5%"
      severity: "critical"
      action: "escalate_to_team"
    
    - condition: "test_coverage < 90%"
      severity: "warning"
      action: "notify_developer"
    
    - condition: "response_time > 500ms"
      severity: "warning"
      action: "performance_investigation"
