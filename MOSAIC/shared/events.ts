/**
 * MOSAIC Event System
 * Cross-lifecycle event communication and coordination
 */

import { MosaicEvent, EventMetadata, LifecycleId, Priority } from './types';

// Event Type Definitions
export type EventType = 
  // APEX-LC Events
  | 'apex.deployment.started'
  | 'apex.deployment.completed'
  | 'apex.deployment.failed'
  | 'apex.test.completed'
  | 'apex.quality.gate.passed'
  | 'apex.quality.gate.failed'
  | 'apex.feature.delivered'
  
  // PRISM-LC Events
  | 'prism.knowledge.captured'
  | 'prism.documentation.generated'
  | 'prism.insight.discovered'
  | 'prism.pattern.recognized'
  | 'prism.search.completed'
  
  // AURORA-LC Events
  | 'aurora.customer.onboarded'
  | 'aurora.feedback.received'
  | 'aurora.satisfaction.measured'
  | 'aurora.churn.predicted'
  | 'aurora.upsell.opportunity'
  
  // PULSE-LC Events
  | 'pulse.coordination.required'
  | 'pulse.resource.allocated'
  | 'pulse.optimization.completed'
  | 'pulse.alert.triggered'
  | 'pulse.health.check.completed'
  
  // Cross-Lifecycle Events
  | 'lifecycle.started'
  | 'lifecycle.stopped'
  | 'lifecycle.health.changed'
  | 'workflow.triggered'
  | 'workflow.completed'
  | 'resource.requested'
  | 'resource.allocated'
  | 'data.updated'
  | 'error.occurred';

// Event Payload Interfaces
export interface DeploymentEvent {
  deployment_id: string;
  environment: string;
  version: string;
  status: 'started' | 'completed' | 'failed';
  duration?: number;
  error?: string;
  metrics?: Record<string, number>;
}

export interface QualityGateEvent {
  gate_id: string;
  project: string;
  score: number;
  threshold: number;
  passed: boolean;
  criteria: Array<{
    name: string;
    value: number;
    threshold: number;
    passed: boolean;
  }>;
}

export interface KnowledgeEvent {
  document_id: string;
  type: 'creation' | 'update' | 'deletion';
  content_type: string;
  tags: string[];
  metadata: Record<string, any>;
}

export interface CustomerEvent {
  customer_id: string;
  event_type: string;
  data: Record<string, any>;
  timestamp: Date;
  source: string;
}

export interface ResourceEvent {
  resource_id: string;
  type: 'compute' | 'storage' | 'network' | 'ai_inference';
  action: 'requested' | 'allocated' | 'released' | 'scaled';
  amount: number;
  unit: string;
  requester: LifecycleId;
}

// Event Builder
export class EventBuilder {
  private event: Partial<MosaicEvent> = {};

  static create(type: EventType): EventBuilder {
    return new EventBuilder().type(type);
  }

  type(type: EventType): EventBuilder {
    this.event.type = type;
    return this;
  }

  source(source: LifecycleId): EventBuilder {
    this.event.source = source;
    return this;
  }

  target(target: LifecycleId | LifecycleId[]): EventBuilder {
    this.event.target = target;
    return this;
  }

  payload(payload: Record<string, any>): EventBuilder {
    this.event.payload = payload;
    return this;
  }

  priority(priority: Priority): EventBuilder {
    if (!this.event.metadata) {
      this.event.metadata = {} as EventMetadata;
    }
    this.event.metadata.priority = priority;
    return this;
  }

  correlationId(id: string): EventBuilder {
    if (!this.event.metadata) {
      this.event.metadata = {} as EventMetadata;
    }
    this.event.metadata.correlation_id = id;
    return this;
  }

  ttl(seconds: number): EventBuilder {
    if (!this.event.metadata) {
      this.event.metadata = {} as EventMetadata;
    }
    this.event.metadata.ttl = seconds;
    return this;
  }

  build(): MosaicEvent {
    if (!this.event.type || !this.event.source) {
      throw new Error('Event must have type and source');
    }

    return {
      id: generateEventId(),
      type: this.event.type,
      source: this.event.source,
      target: this.event.target,
      timestamp: new Date(),
      payload: this.event.payload || {},
      metadata: {
        version: '1.0.0',
        schema_version: '1.0.0',
        priority: 'medium',
        ...this.event.metadata
      }
    };
  }
}

// Event Bus Interface
export interface EventBus {
  publish(event: MosaicEvent): Promise<void>;
  subscribe(pattern: string, handler: EventHandler): Promise<void>;
  unsubscribe(pattern: string, handler: EventHandler): Promise<void>;
  request(event: MosaicEvent, timeout?: number): Promise<MosaicEvent>;
  health(): Promise<{ status: string; metrics: Record<string, number> }>;
}

export type EventHandler = (event: MosaicEvent) => Promise<void> | void;

// Event Patterns for Subscription
export const EventPatterns = {
  // Lifecycle-specific patterns
  APEX_ALL: 'apex.*',
  APEX_DEPLOYMENTS: 'apex.deployment.*',
  PRISM_ALL: 'prism.*',
  AURORA_ALL: 'aurora.*',
  PULSE_ALL: 'pulse.*',
  
  // Cross-lifecycle patterns
  ALL_DEPLOYMENTS: '*.deployment.*',
  ALL_ERRORS: '*.error.*',
  ALL_HEALTH: '*.health.*',
  ALL_WORKFLOWS: 'workflow.*',
  
  // System patterns
  SYSTEM_ALL: '*',
  LIFECYCLE_EVENTS: 'lifecycle.*',
  RESOURCE_EVENTS: 'resource.*'
} as const;

// Event Utilities
export function generateEventId(): string {
  return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function isEventExpired(event: MosaicEvent): boolean {
  if (!event.metadata?.ttl) return false;
  
  const expiryTime = new Date(event.timestamp.getTime() + (event.metadata.ttl * 1000));
  return new Date() > expiryTime;
}

export function getEventAge(event: MosaicEvent): number {
  return Date.now() - event.timestamp.getTime();
}

export function matchesPattern(eventType: string, pattern: string): boolean {
  const regex = new RegExp(pattern.replace(/\*/g, '.*'));
  return regex.test(eventType);
}

// Event Validation
export function validateEvent(event: MosaicEvent): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!event.id) errors.push('Event ID is required');
  if (!event.type) errors.push('Event type is required');
  if (!event.source) errors.push('Event source is required');
  if (!event.timestamp) errors.push('Event timestamp is required');
  if (!event.payload) errors.push('Event payload is required');

  if (event.metadata) {
    if (!event.metadata.version) errors.push('Metadata version is required');
    if (!event.metadata.schema_version) errors.push('Metadata schema version is required');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Common Event Factories
export const EventFactories = {
  deployment: {
    started: (deploymentId: string, environment: string, version: string) =>
      EventBuilder.create('apex.deployment.started')
        .source('apex-lc')
        .payload({ deployment_id: deploymentId, environment, version, status: 'started' })
        .priority('high')
        .build(),

    completed: (deploymentId: string, duration: number, metrics: Record<string, number>) =>
      EventBuilder.create('apex.deployment.completed')
        .source('apex-lc')
        .payload({ deployment_id: deploymentId, status: 'completed', duration, metrics })
        .priority('medium')
        .build(),

    failed: (deploymentId: string, error: string) =>
      EventBuilder.create('apex.deployment.failed')
        .source('apex-lc')
        .payload({ deployment_id: deploymentId, status: 'failed', error })
        .priority('critical')
        .build()
  },

  knowledge: {
    captured: (documentId: string, contentType: string, tags: string[]) =>
      EventBuilder.create('prism.knowledge.captured')
        .source('prism-lc')
        .payload({ document_id: documentId, type: 'creation', content_type: contentType, tags })
        .priority('low')
        .build(),

    insightDiscovered: (insight: string, confidence: number, sources: string[]) =>
      EventBuilder.create('prism.insight.discovered')
        .source('prism-lc')
        .payload({ insight, confidence, sources })
        .priority('medium')
        .build()
  },

  customer: {
    onboarded: (customerId: string, plan: string, source: string) =>
      EventBuilder.create('aurora.customer.onboarded')
        .source('aurora-lc')
        .payload({ customer_id: customerId, plan, source, timestamp: new Date() })
        .priority('medium')
        .build(),

    feedbackReceived: (customerId: string, rating: number, feedback: string) =>
      EventBuilder.create('aurora.feedback.received')
        .source('aurora-lc')
        .payload({ customer_id: customerId, rating, feedback, timestamp: new Date() })
        .priority('medium')
        .build()
  },

  system: {
    healthCheck: (lifecycle: LifecycleId, status: string, metrics: Record<string, number>) =>
      EventBuilder.create('pulse.health.check.completed')
        .source('pulse-lc')
        .payload({ lifecycle, status, metrics })
        .priority('low')
        .build(),

    alertTriggered: (alertId: string, severity: string, description: string) =>
      EventBuilder.create('pulse.alert.triggered')
        .source('pulse-lc')
        .payload({ alert_id: alertId, severity, description })
        .priority(severity as Priority)
        .build()
  }
};

// Mock EventBus implementation for development
export class MockEventBus implements EventBus {
  private listeners = new Map<MosaicEventType, Array<(event: MosaicEvent) => Promise<void>>>();

  async publish(event: MosaicEvent): Promise<void> {
    console.log(`📤 Publishing event: ${event.type} from ${event.source}`);

    // Simulate async processing
    await new Promise(resolve => setTimeout(resolve, 10));

    // Notify listeners
    const eventListeners = this.listeners.get(event.type as MosaicEventType) || [];
    for (const listener of eventListeners) {
      try {
        await listener(event);
      } catch (error) {
        console.error(`Error in event listener for ${event.type}:`, error);
      }
    }
  }

  async subscribe(
    eventTypes: MosaicEventType[],
    handler: (event: MosaicEvent) => Promise<void>
  ): Promise<void> {
    for (const eventType of eventTypes) {
      if (!this.listeners.has(eventType)) {
        this.listeners.set(eventType, []);
      }
      this.listeners.get(eventType)!.push(handler);
    }
    console.log(`📥 Subscribed to events: ${eventTypes.join(', ')}`);
  }
}

// Production EventBus factory
export function createEventBus(useKafka: boolean = false): EventBus {
  if (useKafka && process.env.NODE_ENV !== 'test') {
    // Import and return Kafka EventBus
    try {
      const { KafkaEventBus } = require('../infrastructure/event-bus/event-bus');
      return new KafkaEventBus();
    } catch (error) {
      console.warn('Failed to load Kafka EventBus, falling back to MockEventBus:', error);
      return new MockEventBus();
    }
  }

  return new MockEventBus();
}
