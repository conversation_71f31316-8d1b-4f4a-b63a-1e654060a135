/**
 * MOSAIC Shared Types and Interfaces
 * Core type definitions for cross-lifecycle communication
 */

// Core MOSAIC Types
export type LifecycleId = 
  | 'apex-lc' | 'prism-lc' | 'aurora-lc' | 'nexus-lc' | 'flux-lc' | 'spark-lc'
  | 'shield-lc' | 'quantum-lc' | 'echo-lc' | 'pulse-lc' | 'flow-lc';

export type LifecycleStatus = 'active' | 'planned' | 'inactive' | 'deprecated';
export type LifecyclePhase = 1 | 2 | 3 | 4;
export type Priority = 'low' | 'medium' | 'high' | 'critical';
export type Environment = 'development' | 'staging' | 'production';

// Lifecycle Configuration
export interface LifecycleConfig {
  id: LifecycleId;
  name: string;
  full_name: string;
  domain: string;
  status: LifecycleStatus;
  priority: number;
  phase: LifecyclePhase;
  description?: string;
  dependencies?: LifecycleId[];
  integrations?: LifecycleId[];
}

// Event System Types
export interface MosaicEvent {
  id: string;
  type: string;
  source: LifecycleId;
  target?: LifecycleId | LifecycleId[];
  timestamp: Date;
  payload: Record<string, any>;
  metadata?: EventMetadata;
}

export interface EventMetadata {
  correlation_id?: string;
  causation_id?: string;
  version: string;
  schema_version: string;
  priority: Priority;
  ttl?: number;
  retry_count?: number;
}

export type MosaicEventType =
  // APEX-LC Events
  | 'apex.feature.created'
  | 'apex.feature.deployed'
  | 'apex.feature.tested'
  // AURORA-LC Events
  | 'aurora.customer.health_updated'
  | 'aurora.customer.journey_milestone'
  | 'aurora.feedback.received'
  // PRISM-LC Events
  | 'prism.knowledge.captured'
  | 'prism.insight.generated'
  | 'prism.pattern.recognized'
  // PULSE-LC Events
  | 'pulse.system.health_check'
  | 'pulse.resource.allocated'
  | 'pulse.optimization.completed'
  // System Events
  | 'system.lifecycle.started'
  | 'system.lifecycle.stopped'
  | 'system.error.occurred'
  // Feature Flag Events
  | 'feature.flag.toggled'
  | 'feature.experiment.started'
  | 'feature.feedback.collected';

export interface EventBus {
  publish(event: MosaicEvent): Promise<void>;
  subscribe(
    eventTypes: MosaicEventType[],
    handler: (event: MosaicEvent) => Promise<void>,
    groupId?: string
  ): Promise<void>;
}

// Context Store Types
export interface ContextEntry {
  key: string;
  value: any;
  lifecycle: LifecycleId;
  timestamp: Date;
  ttl?: number;
  version: number;
  metadata?: Record<string, any>;
}

export interface SharedContext {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  update<T>(key: string, updater: (current: T | null) => T): Promise<void>;
  delete(key: string): Promise<void>;
  subscribe(pattern: string, callback: (entry: ContextEntry) => void): void;
  unsubscribe(pattern: string): void;
}

// Coordination Types
export interface CoordinationRequest {
  id: string;
  source: LifecycleId;
  target: LifecycleId;
  type: 'data.query' | 'resource.request' | 'workflow.trigger' | 'status.check';
  payload: Record<string, any>;
  timeout?: number;
  priority?: Priority;
}

export interface CoordinationResponse {
  request_id: string;
  status: 'success' | 'error' | 'timeout';
  data?: any;
  error?: string;
  metadata?: Record<string, any>;
}

// Resource Management Types
export interface ResourceRequirement {
  type: 'compute' | 'storage' | 'network' | 'ai_inference' | 'database';
  amount: number;
  unit: string;
  duration?: number;
  priority: Priority;
  constraints?: Record<string, any>;
}

export interface ResourceAllocation {
  id: string;
  lifecycle: LifecycleId;
  requirements: ResourceRequirement[];
  allocated: boolean;
  start_time?: Date;
  end_time?: Date;
  actual_usage?: Record<string, number>;
}

// Workflow Types
export interface WorkflowStep {
  id: string;
  name: string;
  lifecycle: LifecycleId;
  dependencies: string[];
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  duration?: number;
  error?: string;
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  triggers: WorkflowTrigger[];
  metadata?: Record<string, any>;
}

export interface WorkflowTrigger {
  type: 'event' | 'schedule' | 'manual' | 'condition';
  config: Record<string, any>;
  enabled: boolean;
}

// Metrics and Monitoring Types
export interface LifecycleMetrics {
  lifecycle: LifecycleId;
  timestamp: Date;
  performance: {
    response_time: number;
    throughput: number;
    error_rate: number;
    availability: number;
  };
  resources: {
    cpu_usage: number;
    memory_usage: number;
    storage_usage: number;
    network_usage: number;
  };
  business: {
    tasks_completed: number;
    success_rate: number;
    user_satisfaction: number;
    cost_efficiency: number;
  };
}

export interface SystemHealth {
  overall_status: 'healthy' | 'degraded' | 'critical';
  lifecycles: Record<LifecycleId, LifecycleHealth>;
  infrastructure: InfrastructureHealth;
  last_updated: Date;
}

export interface LifecycleHealth {
  status: 'healthy' | 'degraded' | 'critical' | 'offline';
  metrics: LifecycleMetrics;
  alerts: Alert[];
  dependencies_status: Record<LifecycleId, 'healthy' | 'degraded' | 'critical'>;
}

export interface InfrastructureHealth {
  event_bus: ComponentHealth;
  context_store: ComponentHealth;
  monitoring: ComponentHealth;
  security: ComponentHealth;
}

export interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'critical' | 'offline';
  metrics: Record<string, number>;
  last_check: Date;
}

export interface Alert {
  id: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  description: string;
  lifecycle: LifecycleId;
  timestamp: Date;
  resolved: boolean;
  resolution_time?: Date;
  metadata?: Record<string, any>;
}

// AI Integration Types
export interface AIPersona {
  id: string;
  name: string;
  role: string;
  lifecycle: LifecycleId;
  capabilities: string[];
  model: string;
  context_window: number;
  temperature: number;
  system_prompt: string;
  tools: string[];
}

export interface AITask {
  id: string;
  persona: string;
  type: 'analysis' | 'generation' | 'optimization' | 'decision' | 'automation';
  input: Record<string, any>;
  output?: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed';
  started_at?: Date;
  completed_at?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

// Integration Patterns
export interface IntegrationPattern {
  name: string;
  type: 'event-driven' | 'request-response' | 'pub-sub' | 'workflow' | 'data-sync';
  source: LifecycleId;
  target: LifecycleId;
  configuration: Record<string, any>;
  enabled: boolean;
  metrics?: IntegrationMetrics;
}

export interface IntegrationMetrics {
  message_count: number;
  success_rate: number;
  average_latency: number;
  error_count: number;
  last_success: Date;
  last_error?: Date;
}

// Quality and Compliance Types
export interface QualityGate {
  id: string;
  name: string;
  lifecycle: LifecycleId;
  criteria: QualityCriteria[];
  threshold: number;
  enabled: boolean;
}

export interface QualityCriteria {
  metric: string;
  operator: '>' | '<' | '>=' | '<=' | '==' | '!=';
  value: number;
  weight: number;
}

export interface ComplianceRule {
  id: string;
  name: string;
  description: string;
  category: 'security' | 'privacy' | 'performance' | 'accessibility' | 'business';
  severity: 'low' | 'medium' | 'high' | 'critical';
  automated: boolean;
  check_frequency: string;
  last_check?: Date;
  status: 'compliant' | 'non-compliant' | 'unknown';
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Export all types for easy importing
export * from './events';
export * from './workflows';
export * from './metrics';
