{"name": "@alias/mosaic-cli", "version": "1.0.0", "description": "MOSAIC Lifecycle Management CLI", "main": "dist/mosaic.js", "bin": {"mosaic": "dist/mosaic.js"}, "scripts": {"build": "tsc", "dev": "tsx watch mosaic.ts", "start": "node dist/mosaic.js", "test": "vitest", "lint": "eslint . --ext .ts", "format": "prettier --write .", "prepare": "npm run build"}, "keywords": ["mosaic", "lifecycle", "orchestration", "ai", "automation", "cli"], "author": "ALIAS Team", "license": "MIT", "dependencies": {"commander": "^11.1.0", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "js-yaml": "^4.1.0", "axios": "^1.6.2", "ws": "^8.14.2", "dotenv": "^16.3.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@types/inquirer": "^9.0.7", "@types/js-yaml": "^4.0.9", "@types/ws": "^8.5.10", "typescript": "^5.3.2", "tsx": "^4.6.0", "vitest": "^1.0.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/alias-org/mosaic.git"}, "bugs": {"url": "https://github.com/alias-org/mosaic/issues"}, "homepage": "https://github.com/alias-org/mosaic#readme"}