#!/usr/bin/env node

/**
 * ALIAS CLI - AI-Powered Project Scaffolding and Management
 * 
 * Main CLI tool for creating and managing ALIAS/MOSAIC projects
 * with DotAI integration and full AI assistance.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import inquirer from 'inquirer';
import { execSync } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import { resolve, join } from 'path';

const program = new Command();

// CLI Version and Info
program
  .name('alias')
  .description('ALIAS CLI - AI-powered project scaffolding with MOSAIC and DotAI')
  .version('1.0.0');

// Create command for new projects
program
  .command('create <project-name>')
  .description('Create a new ALIAS project with AI integration')
  .option('-t, --type <type>', 'Project type', 'web-application')
  .option('-T, --template <template>', 'Template to use', 'mosaic-dotai')
  .option('-m, --model <model>', 'AI model', 'claude-3-5-sonnet')
  .option('--skip-git', 'Skip git initialization')
  .option('--skip-install', 'Skip npm install')
  .option('-y, --yes', 'Skip prompts and use defaults')
  .action(async (projectName, options) => {
    await createProject(projectName, options);
  });

// Add DotAI to existing project
program
  .command('add <feature>')
  .description('Add features to existing ALIAS project')
  .option('-p, --path <path>', 'Project path', '.')
  .action(async (feature, options) => {
    await addFeature(feature, options);
  });

// Initialize existing project
program
  .command('init')
  .description('Initialize ALIAS features in existing project')
  .option('-f, --force', 'Force initialization even if files exist')
  .action(async (options) => {
    await initProject(options);
  });

// Agent management commands
const agentCmd = program
  .command('agent')
  .description('Manage MOSAIC agents');

agentCmd
  .command('list')
  .description('List available agents')
  .action(listAgents);

agentCmd
  .command('add <agent-name>')
  .description('Add an agent to current project')
  .action(addAgent);

agentCmd
  .command('status')
  .description('Show agent status')
  .action(showAgentStatus);

// Lifecycle management
const lifecycleCmd = program
  .command('lifecycle')
  .description('Manage project lifecycle');

lifecycleCmd
  .command('status')
  .description('Show current lifecycle stage')
  .action(showLifecycleStatus);

lifecycleCmd
  .command('advance')
  .description('Advance to next lifecycle stage')
  .action(advanceLifecycle);

// AI assistance commands
const aiCmd = program
  .command('ai')
  .description('AI assistance commands');

aiCmd
  .command('chat')
  .description('Start AI chat session')
  .action(startAIChat);

aiCmd
  .command('review')
  .description('AI code review')
  .action(aiCodeReview);

// Template management
const templateCmd = program
  .command('template')
  .description('Manage project templates');

templateCmd
  .command('list')
  .description('List available templates')
  .action(listTemplates);

templateCmd
  .command('create <name>')
  .description('Create new template')
  .action(createTemplate);

// Show CLI banner
function showBanner() {
  console.log(
    chalk.cyan(
      figlet.textSync('ALIAS', {
        font: 'Big',
        horizontalLayout: 'default',
        verticalLayout: 'default'
      })
    )
  );
  console.log(chalk.blue('AI-Powered Development Platform\n'));
}

// Create new project
async function createProject(projectName: string, options: any) {
  showBanner();
  
  console.log(chalk.yellow(`🚀 Creating ALIAS project: ${projectName}\n`));
  
  // Interactive prompts if not using --yes
  let config = options;
  if (!options.yes) {
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'type',
        message: 'What type of project?',
        choices: [
          { name: 'Web Application (Next.js)', value: 'web-application' },
          { name: 'API Service (Hono)', value: 'api-service' },
          { name: 'CLI Tool', value: 'cli-tool' },
          { name: 'Library/Package', value: 'library' },
          { name: 'Mobile App (React Native)', value: 'mobile-app' },
          { name: 'AI Agent', value: 'agent' }
        ],
        default: options.type || 'web-application'
      },
      {
        type: 'list',
        name: 'model',
        message: 'Which AI model?',
        choices: [
          { name: 'Claude 3.5 Sonnet (Recommended)', value: 'claude-3-5-sonnet' },
          { name: 'Claude 3 Opus', value: 'claude-3-opus' },
          { name: 'GPT-4 Turbo', value: 'gpt-4-turbo' }
        ],
        default: options.model || 'claude-3-5-sonnet'
      },
      {
        type: 'confirm',
        name: 'git',
        message: 'Initialize git repository?',
        default: !options.skipGit
      },
      {
        type: 'confirm',
        name: 'install',
        message: 'Install dependencies?',
        default: !options.skipInstall
      }
    ]);
    
    config = { ...options, ...answers };
  }
  
  // Run scaffolding script
  try {
    const scriptPath = resolve(__dirname, '../scripts/scaffold-alias-project.sh');
    const args = [
      scriptPath,
      '--type', config.type,
      '--model', config.model,
      config.git === false ? '--skip-git' : '',
      config.install === false ? '--skip-install' : '',
      projectName
    ].filter(Boolean);
    
    console.log(chalk.blue('Running project scaffolding...\n'));
    execSync(args.join(' '), { stdio: 'inherit' });
    
    console.log(chalk.green('\n✅ Project created successfully!'));
    console.log(chalk.cyan('\nNext steps:'));
    console.log(chalk.white(`  cd ${projectName}`));
    console.log(chalk.white('  cursor . # or code .'));
    console.log(chalk.white('  npm run dev'));
    console.log(chalk.yellow('\n🤖 Try: claude create feature "Your first feature"'));
    
  } catch (error) {
    console.error(chalk.red('❌ Failed to create project:'), error);
    process.exit(1);
  }
}

// Add feature to existing project
async function addFeature(feature: string, options: any) {
  const features = {
    'dotai': 'DotAI task management and AI assistance',
    'home-assistant': 'Home Assistant integration for work-life synthesis',
    'agents': 'MOSAIC agent system',
    'lifecycle': 'Full 11-stage lifecycle management',
    'ci-cd': 'GitLab CI/CD pipeline',
    'docs': 'AI-powered documentation system'
  };
  
  if (!features[feature as keyof typeof features]) {
    console.error(chalk.red(`❌ Unknown feature: ${feature}`));
    console.log(chalk.yellow('Available features:'));
    Object.entries(features).forEach(([key, desc]) => {
      console.log(chalk.white(`  ${key}: ${desc}`));
    });
    return;
  }
  
  console.log(chalk.blue(`🔧 Adding ${feature} to project...`));
  
  try {
    switch (feature) {
      case 'dotai':
        await addDotAI(options.path);
        break;
      case 'home-assistant':
        await addHomeAssistant(options.path);
        break;
      case 'agents':
        await addMOSAICAgents(options.path);
        break;
      default:
        console.log(chalk.yellow(`Feature ${feature} installation not yet implemented`));
    }
  } catch (error) {
    console.error(chalk.red(`❌ Failed to add ${feature}:`), error);
  }
}

// Initialize existing project
async function initProject(options: any) {
  console.log(chalk.blue('🔄 Initializing ALIAS features...'));
  
  const projectPath = process.cwd();
  const packageJsonPath = join(projectPath, 'package.json');
  
  if (!existsSync(packageJsonPath)) {
    console.error(chalk.red('❌ No package.json found. Are you in a Node.js project?'));
    return;
  }
  
  // Create necessary directories
  const dirs = ['.claude', '.dotai', '.mosaic', 'docs'];
  dirs.forEach(dir => {
    const dirPath = join(projectPath, dir);
    if (!existsSync(dirPath)) {
      mkdirSync(dirPath, { recursive: true });
      console.log(chalk.green(`✅ Created ${dir}/`));
    }
  });
  
  console.log(chalk.green('✅ ALIAS initialization complete'));
}

// Agent management functions
async function listAgents() {
  const agents = [
    { name: 'flow_guardian', domain: 'ESD', description: 'Protects developer flow state' },
    { name: 'code_reviewer', domain: 'SAD', description: 'Automated code review' },
    { name: 'test_generator', domain: 'SAD', description: 'AI-powered test generation' },
    { name: 'doc_curator', domain: 'KAD', description: 'Documentation management' },
    { name: 'wellness_advocate', domain: 'UPD', description: 'Developer wellness monitoring' }
  ];
  
  console.log(chalk.blue('\n🤖 Available MOSAIC Agents:\n'));
  agents.forEach(agent => {
    console.log(`${chalk.cyan(agent.name)} (${chalk.magenta(agent.domain)})`);
    console.log(`  ${chalk.gray(agent.description)}\n`);
  });
}

async function addAgent(agentName: string) {
  console.log(chalk.blue(`Adding agent: ${agentName}`));
  // Implementation would add agent configuration
}

async function showAgentStatus() {
  console.log(chalk.blue('🔍 Checking agent status...'));
  // Implementation would show current agent states
}

// Lifecycle management functions
async function showLifecycleStatus() {
  console.log(chalk.blue('📊 Current Lifecycle Status:'));
  // Implementation would read current stage from .mosaic/lifecycles/
}

async function advanceLifecycle() {
  console.log(chalk.blue('⬆️ Advancing lifecycle stage...'));
  // Implementation would move to next stage
}

// AI assistance functions
async function startAIChat() {
  console.log(chalk.magenta('🤖 Starting AI chat session...'));
  console.log(chalk.yellow('Type "exit" to end the session\n'));
  
  // Interactive AI chat loop
  while (true) {
    const { question } = await inquirer.prompt([
      {
        type: 'input',
        name: 'question',
        message: 'You:'
      }
    ]);
    
    if (question.toLowerCase() === 'exit') {
      console.log(chalk.blue('👋 Goodbye!'));
      break;
    }
    
    console.log(chalk.magenta('AI:'), 'Processing your request...');
    // Implementation would call Claude API
  }
}

async function aiCodeReview() {
  console.log(chalk.blue('🔍 Running AI code review...'));
  // Implementation would analyze git diff and provide review
}

// Template management functions
async function listTemplates() {
  const templates = [
    { name: 'mosaic-dotai', description: 'Full MOSAIC with DotAI integration' },
    { name: 'minimal', description: 'Basic ALIAS project structure' },
    { name: 'enterprise', description: 'Enterprise-grade with governance' }
  ];
  
  console.log(chalk.blue('\n📋 Available Templates:\n'));
  templates.forEach(template => {
    console.log(`${chalk.cyan(template.name)}`);
    console.log(`  ${chalk.gray(template.description)}\n`);
  });
}

async function createTemplate(name: string) {
  console.log(chalk.blue(`Creating template: ${name}`));
  // Implementation would create new template
}

// Helper functions for adding features
async function addDotAI(projectPath: string) {
  console.log(chalk.green('✅ DotAI integration added'));
}

async function addHomeAssistant(projectPath: string) {
  console.log(chalk.green('✅ Home Assistant integration added'));
}

async function addMOSAICAgents(projectPath: string) {
  console.log(chalk.green('✅ MOSAIC agents added'));
}

// Run the CLI
program.parse();

export { program };