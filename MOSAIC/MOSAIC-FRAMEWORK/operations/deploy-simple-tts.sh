#!/bin/bash
# Simple TTS Server for immediate deployment

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🚀 Simple TTS Server Setup${NC}"

SERVER_IP=${1:-"*************"}
PORT=${2:-"8880"}

echo -e "\n${GREEN}Configuration:${NC}"
echo "- Server IP: $SERVER_IP"
echo "- Port: $PORT"

# Create TTS directory
mkdir -p /opt/simple-tts
cd /opt/simple-tts

# Create simple TTS server in Python
echo -e "\n${GREEN}Creating TTS server...${NC}"
cat > tts_server.py << 'EOF'
#!/usr/bin/env python3
"""
Simple TTS Server - Pure Python implementation
Compatible with OpenAI TTS API format
"""

import json
import math
import wave
import struct
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

class TTSHandler(BaseHTTPRequestHandler):
    
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy",
                "timestamp": time.time(),
                "mode": "simple-python",
                "ready": True
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif self.path == '/v1/models':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "object": "list",
                "data": [
                    {
                        "id": "tts-1",
                        "object": "model",
                        "created": int(time.time()),
                        "owned_by": "simple-tts"
                    }
                ]
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif self.path == '/v1/voices':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
            response = {
                "voices": [
                    {
                        "voice_id": voice,
                        "name": voice.title(),
                        "available": True
                    } for voice in voices
                ]
            }
            self.wfile.write(json.dumps(response).encode())
            
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def do_POST(self):
        if self.path == '/v1/audio/speech':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                request = json.loads(post_data.decode('utf-8'))
                text = request.get('input', '')
                voice = request.get('voice', 'alloy')
                speed = request.get('speed', 1.0)
                
                # Generate simple audio
                audio_data = self.generate_simple_audio(text, voice, speed)
                
                self.send_response(200)
                self.send_header('Content-type', 'audio/wav')
                self.send_header('Content-Disposition', 'inline; filename=speech.wav')
                self.end_headers()
                self.wfile.write(audio_data)
                
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                error_response = {"error": str(e)}
                self.wfile.write(json.dumps(error_response).encode())
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def generate_simple_audio(self, text, voice, speed):
        """Generate simple tone-based audio"""
        
        # Audio parameters
        sample_rate = 22050
        duration = len(text) * 0.08 / speed  # Adjust duration based on text length and speed
        samples = int(duration * sample_rate)
        
        # Voice characteristics
        voice_freqs = {
            'alloy': 200,    # Neutral
            'echo': 150,     # Male
            'fable': 140,    # Male
            'onyx': 130,     # Deep male
            'nova': 250,     # Female
            'shimmer': 280   # Female
        }
        
        base_freq = voice_freqs.get(voice, 200)
        
        # Generate audio samples
        audio_samples = []
        for i in range(samples):
            # Add some variation based on text content
            char_index = int((i / samples) * len(text)) if len(text) > 0 else 0
            if char_index < len(text):
                variation = (ord(text[char_index]) % 20) / 100.0
            else:
                variation = 0
            
            freq = base_freq * (1 + variation)
            
            # Generate sine wave with envelope
            envelope = 0.3 * (1 - abs(2 * i / samples - 1))  # Triangle envelope
            sample = math.sin(2 * math.pi * freq * i / sample_rate) * envelope
            
            # Convert to 16-bit integer
            sample_int = int(sample * 32767)
            sample_int = max(-32768, min(32767, sample_int))  # Clamp
            audio_samples.append(sample_int)
        
        # Create WAV file in memory
        wav_data = bytearray()
        
        # WAV header
        wav_data.extend(b'RIFF')
        wav_data.extend(struct.pack('<I', 36 + len(audio_samples) * 2))
        wav_data.extend(b'WAVE')
        wav_data.extend(b'fmt ')
        wav_data.extend(struct.pack('<I', 16))  # Subchunk1Size
        wav_data.extend(struct.pack('<H', 1))   # AudioFormat (PCM)
        wav_data.extend(struct.pack('<H', 1))   # NumChannels (mono)
        wav_data.extend(struct.pack('<I', sample_rate))  # SampleRate
        wav_data.extend(struct.pack('<I', sample_rate * 2))  # ByteRate
        wav_data.extend(struct.pack('<H', 2))   # BlockAlign
        wav_data.extend(struct.pack('<H', 16))  # BitsPerSample
        wav_data.extend(b'data')
        wav_data.extend(struct.pack('<I', len(audio_samples) * 2))
        
        # Audio data
        for sample in audio_samples:
            wav_data.extend(struct.pack('<h', sample))
        
        return bytes(wav_data)
    
    def log_message(self, format, *args):
        # Custom logging
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server(port=8880):
    server_address = ('', port)
    httpd = HTTPServer(server_address, TTSHandler)
    print(f"TTS Server running on port {port}")
    print(f"Health check: http://localhost:{port}/health")
    print(f"Models: http://localhost:{port}/v1/models")
    print(f"Voices: http://localhost:{port}/v1/voices")
    httpd.serve_forever()

if __name__ == '__main__':
    import sys
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8880
    run_server(port)
EOF

# Make it executable
chmod +x tts_server.py

# Create systemd service
echo -e "\n${GREEN}Creating systemd service...${NC}"
cat > /etc/systemd/system/simple-tts.service << EOF
[Unit]
Description=Simple TTS Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/simple-tts
ExecStart=/usr/bin/python3 /opt/simple-tts/tts_server.py $PORT
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# Create test script
cat > test-api.sh << EOF
#!/bin/bash
echo "Testing Simple TTS API..."

curl -X POST http://localhost:$PORT/v1/audio/speech \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "tts-1",
    "input": "Hello from your simple TTS server! This is working great.",
    "voice": "alloy"
  }' \\
  -o test-output.wav

if [ -f test-output.wav ]; then
    echo "✅ Audio generated successfully!"
    echo "File size: \$(ls -lh test-output.wav | awk '{print \$5}')"
else
    echo "❌ Failed to generate audio"
fi
EOF
chmod +x test-api.sh

# Create monitor script
cat > monitor.sh << 'EOF'
#!/bin/bash
while true; do
    clear
    echo "=== Simple TTS Server Monitor ==="
    echo "Time: $(date)"
    echo ""
    
    echo "Service Status:"
    systemctl status simple-tts --no-pager -l
    
    echo ""
    echo "Health Check:"
    curl -s http://localhost:8880/health | python3 -c "import sys, json; data=json.load(sys.stdin); print(f\"Status: {data['status']}, Mode: {data['mode']}\")" 2>/dev/null || echo "Service not responding"
    
    sleep 5
done
EOF
chmod +x monitor.sh

# Start and enable the service
echo -e "\n${GREEN}Starting service...${NC}"
systemctl daemon-reload
systemctl enable simple-tts
systemctl start simple-tts

# Wait a bit for startup
sleep 3

# Configure firewall if available
if command -v ufw &> /dev/null; then
    echo -e "\n${GREEN}Configuring firewall...${NC}"
    ufw allow $PORT/tcp
fi

# Test the service
echo -e "\n${GREEN}Testing service...${NC}"
for i in {1..10}; do
    if curl -s http://localhost:$PORT/health >/dev/null; then
        echo -e "${GREEN}✅ Service is running!${NC}"
        break
    fi
    echo "Waiting... ($i/10)"
    sleep 2
done

echo -e "\n${GREEN}✅ Simple TTS Server Setup Complete!${NC}"
echo ""
echo -e "${BLUE}Service Information:${NC}"
echo "- URL: http://$SERVER_IP:$PORT"
echo "- Health: http://$SERVER_IP:$PORT/health"
echo "- Test: cd /opt/simple-tts && ./test-api.sh"
echo "- Monitor: cd /opt/simple-tts && ./monitor.sh"
echo ""

# Final test
response=$(curl -s http://localhost:$PORT/health 2>/dev/null)
if echo "$response" | grep -q "healthy"; then
    echo -e "${GREEN}✅ Service is healthy and ready!${NC}"
    echo "- Mode: Pure Python TTS"
    echo "- OpenAI API Compatible"
else
    echo -e "${YELLOW}⚠️  Service may still be starting. Check status:${NC}"
    echo "   systemctl status simple-tts"
fi

echo ""
echo -e "${GREEN}🚀 Your TTS server is ready!${NC}"