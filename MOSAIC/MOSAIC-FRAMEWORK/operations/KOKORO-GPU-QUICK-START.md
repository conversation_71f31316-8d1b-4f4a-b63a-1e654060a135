# Kokoro GPU Server - Quick Start Guide

## 🚀 One-Command Deployment

### On your server with RTX 4090:

```bash
# 1. Download and run deployment script
curl -L https://gitlab.com/aliaslabs/mosaic-framework/-/raw/main/operations/deploy-kokoro-gpu.sh -o deploy-kokoro-gpu.sh
sudo bash deploy-kokoro-gpu.sh
```

**The script will:**
- ✅ Install NVIDIA drivers (if needed)
- ✅ Install Docker with GPU support
- ✅ Download and configure Kokoro TTS
- ✅ Build optimized Docker container
- ✅ Start GPU-accelerated TTS service
- ✅ Configure firewall and systemd service
- ✅ Run health checks

## 📊 Expected Timeline

| Step | Duration | Description |
|------|----------|-------------|
| Driver Install | 5-10 min | Only if drivers missing |
| Docker Setup | 2-3 min | Container runtime |
| Model Download | 10-15 min | Kokoro voice models |
| Build Container | 5-8 min | GPU-optimized image |
| **Total** | **20-35 min** | **Complete setup** |

## 🧪 Quick Test

After deployment, test your server:

```bash
# On the server
/opt/kokoro/test-api.sh

# Check health
curl http://your-server-ip:8880/health

# Generate speech
curl -X POST http://your-server-ip:8880/v1/audio/speech \
  -H "Content-Type: application/json" \
  -d '{
    "model": "tts-1",
    "input": "Hello from RTX 4090!",
    "voice": "af_sky"
  }' \
  -o test.mp3
```

## 📱 Client Configuration

### Update your local voice-mode settings:

```bash
# Set environment variables
export VOICEMODE_TTS_BASE_URL="http://your-server-ip:8880/v1"
export VOICEMODE_PREFER_LOCAL_TTS=false

# Or use voice-mode config
```

### Test from Claude Code:

```bash
# Use the voice status tool to verify connection
/voice-mode:voice-status

# Test TTS with GPU acceleration
/voice-mode:converse "Testing GPU accelerated speech!"
```

## 🔧 Management Commands

```bash
# Service management
sudo systemctl start kokoro-tts     # Start service
sudo systemctl stop kokoro-tts      # Stop service  
sudo systemctl status kokoro-tts    # Check status

# Monitoring
cd /opt/kokoro && ./monitor.sh      # GPU and service monitor
docker logs kokoro-tts-gpu          # View logs
nvidia-smi -l 1                     # GPU utilization

# Performance test
cd /opt/kokoro && ./test-api.sh     # API functionality test
```

## ⚡ Performance Expectations

**RTX 4090 Performance:**
- **50 characters**: ~0.1 seconds (50x real-time)
- **200 characters**: ~0.3 seconds (30x real-time)
- **1000 characters**: ~0.8 seconds (20x real-time)
- **Concurrent requests**: 4-8 optimal

**Memory Usage:**
- Base GPU memory: ~2-3 GB
- Per voice model: ~500MB-1GB
- Total with 8 voices: ~6-8 GB (well within 24GB RTX 4090)

## 🔍 Troubleshooting

### Common Issues:

**1. GPU Not Detected**
```bash
# Check NVIDIA driver
nvidia-smi

# If not working, reboot after driver install:
sudo reboot
# Then run deployment script again
```

**2. Docker GPU Access**
```bash
# Test GPU in Docker
docker run --rm --gpus all nvidia/cuda:12.2-base nvidia-smi

# If fails, restart Docker
sudo systemctl restart docker
```

**3. Service Won't Start**
```bash
# Check logs
docker logs kokoro-tts-gpu

# Common fixes
sudo systemctl restart kokoro-tts
docker-compose down && docker-compose up -d
```

**4. High Memory Usage**
```bash
# Monitor GPU memory
nvidia-smi -l 1

# If OOM, reduce concurrent requests or restart service
sudo systemctl restart kokoro-tts
```

### Performance Optimization:

**For maximum speed:**
```bash
# Edit /opt/kokoro/docker-compose.yml
environment:
  - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256  # Reduce for less memory
  - OMP_NUM_THREADS=16                             # Increase for more CPU cores
```

## 🌐 Production Setup

### SSL/HTTPS (Recommended):

```bash
# Install nginx and certbot
sudo apt install nginx certbot python3-certbot-nginx

# Configure reverse proxy
sudo nano /etc/nginx/sites-available/kokoro
# Add SSL certificate
sudo certbot --nginx -d kokoro.yourdomain.com
```

### Firewall Security:
```bash
# Allow only specific IPs
sudo ufw delete allow 8880
sudo ufw allow from YOUR_CLIENT_IP to any port 8880
```

## 📈 Scaling Options

### Multiple GPUs:
```yaml
# docker-compose.yml for 2 GPUs
services:
  kokoro-gpu0:
    environment:
      - CUDA_VISIBLE_DEVICES=0
    ports:
      - "8880:8880"
  
  kokoro-gpu1:
    environment:
      - CUDA_VISIBLE_DEVICES=1  
    ports:
      - "8881:8880"
```

### Load Balancing:
```bash
# nginx upstream configuration
upstream kokoro_backend {
    server localhost:8880;
    server localhost:8881;
}
```

## 📞 Support

If you encounter issues:

1. **Check logs**: `docker logs kokoro-tts-gpu`
2. **Monitor GPU**: `cd /opt/kokoro && ./monitor.sh`
3. **Test health**: `curl http://localhost:8880/health`
4. **Restart service**: `sudo systemctl restart kokoro-tts`

---

Your RTX 4090 will provide massive TTS performance improvements! 🚀