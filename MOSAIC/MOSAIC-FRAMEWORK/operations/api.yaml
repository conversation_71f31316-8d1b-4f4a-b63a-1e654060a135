# ALIAS MOSAIC - API Specifications
# REST and GraphQL API definitions for MOSAIC

api:
  version: "1.0.0"
  base_url: "/api/v1/mosaic"
  
  authentication:
    type: "Bearer"
    header: "Authorization"
    format: "Bearer {token}"
  
  rate_limits:
    default:
      requests_per_minute: 600
      requests_per_hour: 10000
    
    ai_operations:
      requests_per_minute: 60
      tokens_per_minute: 100000

  # See full endpoint definitions in this file...
