#!/bin/bash
# Configure local voice-mode client to use GPU TTS server

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🔧 Configuring Voice-Mode for GPU TTS Server${NC}"

GPU_SERVER_IP=${1:-"*************"}
GPU_SERVER_PORT=${2:-"8880"}
GPU_SERVER_URL="http://${GPU_SERVER_IP}:${GPU_SERVER_PORT}"

echo -e "\n${GREEN}Configuration:${NC}"
echo "- GPU Server: $GPU_SERVER_URL"

# Test GPU server connectivity
echo -e "\n${GREEN}1. Testing GPU server connectivity...${NC}"
if curl -s "$GPU_SERVER_URL/health" >/dev/null; then
    echo -e "${GREEN}✅ GPU server is reachable${NC}"
    SERVER_MODE=$(curl -s "$GPU_SERVER_URL/health" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('mode', 'unknown'))" 2>/dev/null || echo "unknown")
    echo "   Server mode: $SERVER_MODE"
else
    echo -e "${YELLOW}⚠️  GPU server not reachable at $GPU_SERVER_URL${NC}"
    echo "   Make sure the server is running and accessible"
    exit 1
fi

# Stop local Kokoro if running
echo -e "\n${GREEN}2. Stopping local Kokoro...${NC}"
pkill -f kokoro-start 2>/dev/null || echo "   No local Kokoro process found"

# Wait a moment for port to free up
sleep 2

# Set environment variables for current session
echo -e "\n${GREEN}3. Setting environment variables...${NC}"
export VOICEMODE_TTS_BASE_URL="$GPU_SERVER_URL/v1"
export VOICEMODE_PREFER_LOCAL_TTS=false
export KOKORO_BASE_URL="$GPU_SERVER_URL/v1"

echo "   VOICEMODE_TTS_BASE_URL=$VOICEMODE_TTS_BASE_URL"
echo "   VOICEMODE_PREFER_LOCAL_TTS=$VOICEMODE_PREFER_LOCAL_TTS"

# Create persistent configuration script
echo -e "\n${GREEN}4. Creating persistent configuration...${NC}"
cat > ~/.voice-mode-gpu-config << EOF
# Voice-Mode GPU Server Configuration
export VOICEMODE_TTS_BASE_URL="$GPU_SERVER_URL/v1"
export VOICEMODE_PREFER_LOCAL_TTS=false
export KOKORO_BASE_URL="$GPU_SERVER_URL/v1"

# Add to your shell profile (.bashrc, .zshrc, etc.):
# source ~/.voice-mode-gpu-config
EOF

echo "   Configuration saved to ~/.voice-mode-gpu-config"

# Test TTS generation directly
echo -e "\n${GREEN}5. Testing TTS generation...${NC}"
TEST_TEXT="Hello from the GPU accelerated TTS server!"
curl -X POST "$GPU_SERVER_URL/v1/audio/speech" \
  -H "Content-Type: application/json" \
  -d "{
    \"model\": \"tts-1\",
    \"input\": \"$TEST_TEXT\",
    \"voice\": \"alloy\"
  }" \
  -o test-gpu-audio.wav

if [ -f test-gpu-audio.wav ]; then
    FILE_SIZE=$(ls -lh test-gpu-audio.wav | awk '{print $5}')
    echo -e "${GREEN}✅ TTS generation successful!${NC}"
    echo "   Audio file: test-gpu-audio.wav ($FILE_SIZE)"
    
    # Try to play the audio (macOS)
    if command -v afplay >/dev/null; then
        echo "   Playing test audio..."
        afplay test-gpu-audio.wav &
    fi
else
    echo -e "${YELLOW}⚠️  TTS generation failed${NC}"
fi

echo -e "\n${GREEN}✅ GPU Client Configuration Complete!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Restart your voice-mode session or reload configuration"
echo "2. Source the configuration: source ~/.voice-mode-gpu-config"
echo "3. Test with voice commands"
echo ""
echo -e "${BLUE}Manual Test Commands:${NC}"
echo "curl -s $GPU_SERVER_URL/health"
echo "curl -s $GPU_SERVER_URL/v1/voices"
echo ""
echo -e "${GREEN}🚀 Your client is now configured for GPU-accelerated TTS!${NC}"