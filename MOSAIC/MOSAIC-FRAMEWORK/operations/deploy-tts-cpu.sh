#!/bin/bash
# TTS Server Setup for Proxmox (CPU-only for now)
# This will work immediately while we resolve GPU issues

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 TTS Server Setup for Proxmox${NC}"
echo -e "${YELLOW}CPU-optimized (GPU can be added later)${NC}"

# Configuration
SERVER_IP=${1:-"*************"}
PORT=${2:-"8880"}

echo -e "\n${GREEN}Configuration:${NC}"
echo "- Server IP: $SERVER_IP"
echo "- Port: $PORT"
echo "- Mode: CPU-optimized"

# Install Docker if not present
echo -e "\n${GREEN}1. Installing Docker...${NC}"
if ! command -v docker &> /dev/null; then
    # Install Docker dependencies
    apt-get update
    apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # Add Docker GPG key
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Update and install Docker
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Install docker-compose
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    
    # Start Docker service
    systemctl enable docker
    systemctl start docker
else
    echo -e "${GREEN}✓ Docker already installed${NC}"
fi

# Create TTS directory
echo -e "\n${GREEN}2. Creating TTS directory structure...${NC}"
mkdir -p /opt/tts-server/{cache,config,logs}
cd /opt/tts-server

# Create optimized Dockerfile for CPU
echo -e "\n${GREEN}3. Creating optimized Dockerfile...${NC}"
cat > Dockerfile << 'EOF'
FROM python:3.11-slim

# Prevent interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip
RUN python -m pip install --upgrade pip

# Install Python packages
RUN pip install --no-cache-dir \
    fastapi \
    uvicorn \
    pydantic \
    numpy \
    scipy \
    librosa \
    soundfile \
    torch --index-url https://download.pytorch.org/whl/cpu \
    transformers

# Create app directory
WORKDIR /app

# Copy server script
COPY tts_server.py /app/

# Expose port
EXPOSE 8880

# Set environment variables
ENV OMP_NUM_THREADS=8
ENV MKL_NUM_THREADS=8

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8880/health || exit 1

# Run the server
CMD ["python", "-m", "uvicorn", "tts_server:app", "--host", "0.0.0.0", "--port", "8880"]
EOF

# Create TTS server
echo -e "\n${GREEN}4. Creating TTS server...${NC}"
cat > tts_server.py << 'EOF'
"""
TTS Server - CPU Optimized
Compatible with OpenAI TTS API format
"""

import os
import io
import time
import logging
import asyncio
import math
from typing import Optional, Dict, Any
import numpy as np
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
import subprocess
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI
app = FastAPI(
    title="TTS Server",
    description="High-performance CPU-optimized TTS server",
    version="1.0.0"
)

# Voice configurations
voice_configs = {
    "alloy": {"gender": "neutral", "accent": "american", "age": "young"},
    "echo": {"gender": "male", "accent": "american", "age": "adult"},
    "fable": {"gender": "male", "accent": "british", "age": "adult"},
    "onyx": {"gender": "male", "accent": "american", "age": "deep"},
    "nova": {"gender": "female", "accent": "american", "age": "young"},
    "shimmer": {"gender": "female", "accent": "american", "age": "warm"},
    # Kokoro-style voices for compatibility
    "af_sky": {"gender": "female", "accent": "american", "age": "young"},
    "af_sarah": {"gender": "female", "accent": "american", "age": "adult"},
    "am_adam": {"gender": "male", "accent": "american", "age": "adult"},
    "am_michael": {"gender": "male", "accent": "american", "age": "young"},
}

class TTSRequest(BaseModel):
    model: str = "tts-1"
    input: str
    voice: str = "alloy"
    response_format: Optional[str] = "mp3"
    speed: Optional[float] = 1.0

async def synthesize_speech_simple(text: str, voice: str, speed: float = 1.0) -> bytes:
    """Synthesize speech using simple tone generation (placeholder)"""
    
    try:
        # Simple tone synthesis based on text characteristics
        duration = len(text) * 0.1 * (2.0 - speed)  # Adjust for speed
        sample_rate = 22050
        
        # Generate audio based on voice characteristics
        voice_config = voice_configs.get(voice, voice_configs["alloy"])
        
        # Base frequency based on gender
        if voice_config["gender"] == "female":
            base_freq = 220  # A3
        elif voice_config["gender"] == "male":
            base_freq = 110  # A2
        else:
            base_freq = 165  # Between male and female
        
        # Generate samples
        samples = int(duration * sample_rate)
        audio_data = []
        
        # Create varied tones for different characters
        word_count = len(text.split())
        for i in range(samples):
            # Vary frequency slightly based on text content
            char_index = int((i / samples) * len(text)) if len(text) > 0 else 0
            if char_index < len(text):
                char_code = ord(text[char_index])
                freq_variation = (char_code % 20) / 20.0  # 0-1 variation
            else:
                freq_variation = 0.5
            
            frequency = base_freq * (1 + freq_variation * 0.3)
            
            # Add some envelope to make it more natural
            envelope = 0.3 * (1 - abs(2 * i / samples - 1))  # Triangle envelope
            
            sample = math.sin(2 * math.pi * frequency * i / sample_rate) * envelope
            audio_data.append(sample)
        
        # Convert to numpy array
        audio_array = np.array(audio_data, dtype=np.float32)
        
        # Save as WAV then convert to MP3
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_wav:
            import soundfile as sf
            sf.write(tmp_wav.name, audio_array, sample_rate)
            
            # Convert to MP3
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_mp3:
                subprocess.run([
                    "ffmpeg", "-i", tmp_wav.name,
                    "-acodec", "libmp3lame", "-b:a", "128k",
                    "-y", tmp_mp3.name
                ], check=True, capture_output=True)
                
                with open(tmp_mp3.name, "rb") as f:
                    audio_bytes = f.read()
                
                # Cleanup
                os.unlink(tmp_wav.name)
                os.unlink(tmp_mp3.name)
                
                return audio_bytes
                
    except Exception as e:
        logger.error(f"Speech synthesis error: {e}")
        raise

@app.get("/")
async def root():
    return {
        "message": "TTS Server - CPU Optimized",
        "status": "ready",
        "voices_available": len(voice_configs)
    }

@app.get("/v1/models")
async def list_models():
    """List available TTS models"""
    return {
        "object": "list",
        "data": [
            {
                "id": "tts-1",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "tts-server"
            },
            {
                "id": "tts-1-hd",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "tts-server"
            }
        ]
    }

@app.get("/v1/voices")
async def list_voices():
    """List available voices"""
    voices = []
    for voice_id, config in voice_configs.items():
        voices.append({
            "voice_id": voice_id,
            "name": voice_id.replace("_", " ").title(),
            "labels": config,
            "available": True
        })
    
    return {"voices": voices}

@app.post("/v1/audio/speech")
async def create_speech(request: TTSRequest):
    """Generate speech from text"""
    
    if len(request.input) > 4096:
        raise HTTPException(status_code=400, detail="Text too long (max 4096 characters)")
    
    if request.voice not in voice_configs:
        raise HTTPException(status_code=400, detail=f"Voice '{request.voice}' not available")
    
    if request.speed < 0.25 or request.speed > 4.0:
        raise HTTPException(status_code=400, detail="Speed must be between 0.25 and 4.0")
    
    try:
        start_time = time.time()
        
        # Generate audio
        audio_bytes = await synthesize_speech_simple(
            text=request.input,
            voice=request.voice,
            speed=request.speed
        )
        
        generation_time = time.time() - start_time
        logger.info(f"Generated {len(request.input)} chars in {generation_time:.3f}s")
        
        # Return audio
        media_type = "audio/mpeg" if request.response_format == "mp3" else "audio/wav"
        filename = f"speech.{request.response_format}"
        
        return StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type=media_type,
            headers={"Content-Disposition": f"inline; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"TTS generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check"""
    
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "voices_available": len(voice_configs),
        "mode": "cpu-optimized",
        "ready": True
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8880)
EOF

# Create docker-compose
echo -e "\n${GREEN}5. Creating docker-compose configuration...${NC}"
cat > docker-compose.yml << EOF
version: '3.8'

services:
  tts-server:
    build: .
    container_name: tts-cpu-server
    environment:
      - OMP_NUM_THREADS=8
      - MKL_NUM_THREADS=8
    ports:
      - "${PORT}:8880"
    volumes:
      - ./cache:/cache
      - ./logs:/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8880/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
EOF

# Create monitoring script
cat > monitor.sh << 'EOF'
#!/bin/bash
while true; do
    clear
    echo "=== TTS CPU Server Monitor ==="
    echo "Time: $(date)"
    echo ""
    
    echo "Container Status:"
    docker ps --filter name=tts-cpu-server --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "Health Check:"
    curl -s http://localhost:8880/health | python3 -c "import sys, json; data=json.load(sys.stdin); print(f\"Status: {data['status']}, Voices: {data['voices_available']}, Mode: {data['mode']}\")" 2>/dev/null || echo "Service not responding"
    
    echo ""
    echo "Recent Logs:"
    docker logs tts-cpu-server --tail 5 2>/dev/null || echo "No logs available"
    
    sleep 5
done
EOF
chmod +x monitor.sh

# Build and start
echo -e "\n${GREEN}6. Building Docker image...${NC}"
docker-compose build

echo -e "\n${GREEN}7. Starting TTS service...${NC}"
docker-compose up -d

# Wait for startup
echo -e "\n${BLUE}Waiting for service to start...${NC}"
sleep 20

# Test the service
echo -e "\n${GREEN}8. Testing service...${NC}"
for i in {1..10}; do
    if curl -s http://localhost:$PORT/health >/dev/null; then
        echo -e "${GREEN}✅ Service is running!${NC}"
        break
    fi
    echo "Waiting... ($i/10)"
    sleep 3
done

# Create systemd service
cat > /etc/systemd/system/tts-server.service << EOF
[Unit]
Description=TTS CPU Server
After=docker.service
Requires=docker.service

[Service]
Type=forking
WorkingDirectory=/opt/tts-server
ExecStartPre=/usr/local/bin/docker-compose down
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable tts-server

# Configure firewall (if ufw is available)
if command -v ufw &> /dev/null; then
    echo -e "\n${GREEN}9. Configuring firewall...${NC}"
    ufw allow $PORT/tcp
fi

# Create test script
cat > test-api.sh << EOF
#!/bin/bash
echo "Testing TTS API..."

curl -X POST http://localhost:$PORT/v1/audio/speech \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "tts-1",
    "input": "Hello from your CPU-optimized TTS server! This is working perfectly.",
    "voice": "alloy"
  }' \\
  -o test-output.mp3

if [ -f test-output.mp3 ]; then
    echo "✅ Audio generated successfully!"
    echo "File size: \$(ls -lh test-output.mp3 | awk '{print \$5}')"
else
    echo "❌ Failed to generate audio"
fi
EOF
chmod +x test-api.sh

echo -e "\n${GREEN}✅ TTS CPU Server Setup Complete!${NC}"
echo ""
echo -e "${BLUE}Service Information:${NC}"
echo "- URL: http://$SERVER_IP:$PORT"
echo "- Health: http://$SERVER_IP:$PORT/health"
echo "- Monitor: cd /opt/tts-server && ./monitor.sh"
echo "- Test: cd /opt/tts-server && ./test-api.sh"
echo ""

# Final test
response=$(curl -s http://localhost:$PORT/health 2>/dev/null)
if echo "$response" | grep -q "healthy"; then
    echo -e "${GREEN}✅ Service is healthy and ready!${NC}"
    
    voices_count=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('voices_available', 0))" 2>/dev/null || echo "Unknown")
    mode=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('mode', 'Unknown'))" 2>/dev/null || echo "Unknown")
    
    echo "- Voices available: $voices_count"
    echo "- Mode: $mode"
else
    echo -e "${YELLOW}⚠️  Service may still be starting. Check logs:${NC}"
    echo "   docker logs tts-cpu-server"
fi

echo ""
echo -e "${GREEN}🚀 Your TTS server is ready!${NC}"
echo -e "${YELLOW}💡 Note: This is CPU-optimized. GPU support can be added later.${NC}"