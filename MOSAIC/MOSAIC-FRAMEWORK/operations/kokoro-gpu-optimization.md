# Kokoro GPU Optimization Guide

## RTX 4090 Performance Tuning

### GPU Memory Optimization

```bash
# Set optimal memory allocation
export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:512,expandable_segments:True"
export CUDA_LAUNCH_BLOCKING=0  # Async execution
export CUDA_CACHE_DISABLE=0    # Enable caching
```

### Docker Runtime Settings

```yaml
# docker-compose.yml optimizations
services:
  kokoro-tts:
    environment:
      # GPU Memory Management
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512,expandable_segments:True
      - CUDA_VISIBLE_DEVICES=0
      - TORCH_CUDA_ARCH_LIST=8.9  # RTX 4090 architecture
      
      # Performance Tuning
      - OMP_NUM_THREADS=8          # CPU threads for data loading
      - MKL_NUM_THREADS=8          # Intel MKL optimization
      - NUMBA_CACHE_DIR=/cache     # JIT compilation cache
      
      # Memory Optimization
      - MALLOC_ARENA_MAX=4         # Reduce memory fragmentation
    
    deploy:
      resources:
        limits:
          memory: 32G              # Adjust based on available RAM
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
              device_ids: ['0']
```

### Performance Benchmarks

**Expected Performance on RTX 4090:**

| Text Length | Generation Time | Real-time Factor |
|-------------|----------------|------------------|
| 50 chars    | ~0.1s          | 50x faster      |
| 200 chars   | ~0.3s          | 30x faster      |
| 1000 chars  | ~0.8s          | 20x faster      |

**Concurrent Requests:**
- 1 request: ~0.3s per request
- 4 concurrent: ~0.4s per request (optimal)
- 8 concurrent: ~0.6s per request
- 16+ concurrent: Memory limited

### Voice Quality Settings

```python
# High-quality voices for RTX 4090
RECOMMENDED_VOICES = {
    "female": ["af_sky", "af_sarah", "bf_emma", "bf_isabella"],
    "male": ["am_adam", "am_michael", "bm_george", "bm_lewis"]
}

# Quality settings
GENERATION_CONFIG = {
    "temperature": 0.7,        # Balance quality vs speed
    "top_k": 50,              # Vocabulary filtering
    "top_p": 0.8,             # Nucleus sampling
    "repetition_penalty": 1.1, # Avoid repetition
    "length_penalty": 1.0,     # Length control
}
```

### Monitoring and Scaling

```bash
# GPU utilization monitoring
nvidia-smi dmon -s pucvmet -d 1

# Memory usage tracking
watch -n 1 'nvidia-smi --query-gpu=memory.used,memory.total --format=csv,nounits,noheader'

# Container metrics
docker stats kokoro-tts-gpu

# API performance monitoring
curl -s http://localhost:8880/health | jq '.gpu_memory_allocated'
```

### Load Balancing (Multiple GPUs)

```yaml
# Multi-GPU setup
services:
  kokoro-tts-gpu0:
    environment:
      - CUDA_VISIBLE_DEVICES=0
    ports:
      - "8880:8880"
  
  kokoro-tts-gpu1:
    environment:
      - CUDA_VISIBLE_DEVICES=1
    ports:
      - "8881:8880"

  nginx-lb:
    image: nginx:alpine
    ports:
      - "8880:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
```

### Troubleshooting

**Common Issues:**

1. **Out of Memory (OOM)**
   ```bash
   # Check memory usage
   nvidia-smi
   
   # Reduce batch size in server code
   # Add memory cleanup in generation loop
   torch.cuda.empty_cache()
   ```

2. **Slow First Request**
   ```python
   # Warm up the model during startup
   def warmup_model():
       for voice in voices.values():
           with torch.no_grad():
               _ = voice.generate("warmup", max_length=10)
   ```

3. **High Latency**
   ```bash
   # Check GPU utilization
   nvidia-smi -l 1
   
   # Monitor API response times
   curl -w "@curl-format.txt" http://localhost:8880/health
   ```

### Security Considerations

```bash
# Firewall configuration
ufw allow from YOUR_CLIENT_IP to any port 8880
ufw deny 8880

# SSL/TLS termination
# Use nginx with Let's Encrypt for production
certbot --nginx -d kokoro.yourdomain.com
```

### Backup and Recovery

```bash
# Backup models and configuration
tar -czf kokoro-backup-$(date +%Y%m%d).tar.gz /opt/kokoro/

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backup/kokoro"
DATE=$(date +%Y%m%d_%H%M%S)

# Stop service
systemctl stop kokoro-tts

# Create backup
tar -czf "$BACKUP_DIR/kokoro_$DATE.tar.gz" /opt/kokoro/

# Restart service
systemctl start kokoro-tts

# Cleanup old backups (keep 7 days)
find "$BACKUP_DIR" -name "kokoro_*.tar.gz" -mtime +7 -delete
```

## Client Configuration Updates

Update your voice-mode configuration to use the GPU server:

```bash
# Environment variables
export VOICEMODE_TTS_ENDPOINT="http://your-server:8880/v1"
export VOICEMODE_TTS_PROVIDER="kokoro"
export VOICEMODE_PREFER_LOCAL_TTS=false

# Or use the voice-mode settings
```

This setup will give you significant performance improvements with your RTX 4090!