#!/bin/bash
# Deploy Kokoro GPU server - Single command deployment
# Run this script on your server with RTX 4090

set -e

echo "🚀 Deploying Kokoro GPU Server"
echo "================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    echo "❌ This script must be run as root (use sudo)"
    echo "Run: sudo $0"
    exit 1
fi

# Get server configuration
read -p "Enter your server's external IP address: " SERVER_IP
read -p "Enter port for Kokoro service [8880]: " PORT
PORT=${PORT:-8880}

echo ""
echo "📋 Configuration:"
echo "- Server IP: $SERVER_IP"
echo "- Port: $PORT"
echo "- GPU: Detecting..."

# Check for NVIDIA GPU
if command -v nvidia-smi &> /dev/null; then
    GPU_INFO=$(nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits | head -1)
    echo "- Detected: $GPU_INFO"
else
    echo "⚠️  NVIDIA GPU not detected. Installing drivers..."
fi

echo ""
read -p "Continue with installation? [y/N]: " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "Installation cancelled."
    exit 0
fi

echo ""
echo "🔧 Starting installation..."

# 1. Update system
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# 2. Install NVIDIA drivers if needed
if ! command -v nvidia-smi &> /dev/null; then
    echo "🎮 Installing NVIDIA drivers..."
    
    # Detect and install appropriate driver
    ubuntu-drivers autoinstall
    
    echo "⚠️  NVIDIA driver installed. System reboot required."
    echo "After reboot, run this script again to continue."
    
    # Create a restart script
    cat > /root/continue-kokoro-install.sh << EOF
#!/bin/bash
cd $(pwd)
sudo $0
EOF
    chmod +x /root/continue-kokoro-install.sh
    
    echo "Run after reboot: sudo /root/continue-kokoro-install.sh"
    exit 0
fi

# 3. Install Docker
if ! command -v docker &> /dev/null; then
    echo "🐳 Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    rm get-docker.sh
    
    # Install docker-compose
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# 4. Configure Docker for GPU
echo "🎮 Configuring Docker GPU support..."
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/libnvidia-container/gpgkey | apt-key add -
curl -s -L https://nvidia.github.io/libnvidia-container/$distribution/libnvidia-container.list | tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

apt-get update
apt-get install -y nvidia-container-toolkit
nvidia-ctk runtime configure --runtime=docker
systemctl restart docker

# 5. Create Kokoro directory
echo "📁 Creating Kokoro directory structure..."
mkdir -p /opt/kokoro/{models,cache,config,logs}
cd /opt/kokoro

# 6. Download and setup Kokoro
echo "📥 Setting up Kokoro TTS..."

# Create optimized Dockerfile
cat > Dockerfile << 'EOF'
FROM nvidia/cuda:12.2-runtime-ubuntu22.04

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3-pip \
    git \
    ffmpeg \
    libsndfile1 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.11 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# Install PyTorch with CUDA support
RUN pip3 install --no-cache-dir torch==2.1.0+cu121 torchaudio==2.1.0+cu121 --index-url https://download.pytorch.org/whl/cu121

# Install Kokoro TTS (using a working version)
RUN pip3 install --no-cache-dir \
    git+https://github.com/resemble-ai/Kokoro.git \
    fastapi \
    uvicorn \
    pydantic \
    numpy \
    scipy \
    librosa \
    soundfile

# Create app directory
WORKDIR /app

# Copy server script
COPY kokoro_server.py /app/

# Create models directory
RUN mkdir -p /app/models

# Expose port
EXPOSE 8880

# Set environment variables for optimal GPU usage
ENV CUDA_VISIBLE_DEVICES=0
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
ENV TORCH_CUDA_ARCH_LIST="8.9"
ENV OMP_NUM_THREADS=8

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8880/health || exit 1

# Run the server
CMD ["python", "-m", "uvicorn", "kokoro_server:app", "--host", "0.0.0.0", "--port", "8880"]
EOF

# 7. Create the optimized server
cat > kokoro_server.py << 'EOF'
"""
Kokoro TTS GPU Server - Production Ready
Optimized for RTX 4090 with automatic model management
"""

import os
import io
import time
import logging
import asyncio
from typing import Optional, Dict, Any
import torch
import numpy as np
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
import subprocess
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# GPU optimization
if torch.cuda.is_available():
    torch.backends.cudnn.benchmark = True
    torch.backends.cuda.matmul.allow_tf32 = True
    device = torch.device("cuda")
    logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
else:
    device = torch.device("cpu")
    logger.warning("GPU not available, using CPU")

# Initialize FastAPI
app = FastAPI(
    title="Kokoro TTS GPU Server",
    description="High-performance TTS with RTX 4090 acceleration",
    version="1.0.0"
)

# Global model cache
models_cache = {}
voice_configs = {
    "af_sky": {"gender": "female", "accent": "american", "age": "young"},
    "af_sarah": {"gender": "female", "accent": "american", "age": "adult"},
    "am_adam": {"gender": "male", "accent": "american", "age": "adult"},
    "am_michael": {"gender": "male", "accent": "american", "age": "young"},
    "bf_emma": {"gender": "female", "accent": "british", "age": "young"},
    "bf_isabella": {"gender": "female", "accent": "british", "age": "adult"},
    "bm_george": {"gender": "male", "accent": "british", "age": "adult"},
    "bm_lewis": {"gender": "male", "accent": "british", "age": "young"},
}

# Request models
class TTSRequest(BaseModel):
    model: str = "tts-1"
    input: str
    voice: str = "af_sky"
    response_format: Optional[str] = "mp3"
    speed: Optional[float] = 1.0

# Load Kokoro models on startup
@app.on_event("startup")
async def load_models():
    """Load and cache Kokoro models"""
    try:
        # Import here to handle potential installation issues
        import kokoro_tts as kokoro
        
        logger.info("Loading Kokoro models...")
        for voice_name in voice_configs.keys():
            try:
                logger.info(f"Loading voice: {voice_name}")
                # Load voice model
                voice_model = kokoro.load_voice(voice_name)
                if torch.cuda.is_available():
                    voice_model = voice_model.to(device)
                    voice_model.eval()
                
                models_cache[voice_name] = voice_model
                logger.info(f"✓ Voice {voice_name} loaded successfully")
                
            except Exception as e:
                logger.error(f"Failed to load voice {voice_name}: {e}")
        
        logger.info(f"Loaded {len(models_cache)} voice models")
        
        # Warmup with a short generation
        if models_cache:
            warmup_voice = list(models_cache.keys())[0]
            await generate_speech_async("Warmup", warmup_voice)
            logger.info("Model warmup completed")
            
    except ImportError:
        logger.error("Kokoro TTS not properly installed. Some features may not work.")
    except Exception as e:
        logger.error(f"Error during model loading: {e}")

async def generate_speech_async(text: str, voice: str, speed: float = 1.0) -> bytes:
    """Generate speech asynchronously with GPU acceleration"""
    
    if voice not in models_cache:
        raise ValueError(f"Voice '{voice}' not available")
    
    try:
        # Import kokoro inside function to handle missing imports
        import kokoro_tts as kokoro
        
        voice_model = models_cache[voice]
        
        # Generate audio with mixed precision for faster inference
        with torch.cuda.amp.autocast(enabled=torch.cuda.is_available()):
            with torch.no_grad():
                # Generate audio
                audio_array = kokoro.generate(
                    text=text,
                    voice=voice_model,
                    speed=speed,
                    device=device
                )
        
        # Convert to bytes using temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            # Save as WAV first
            import soundfile as sf
            sf.write(tmp_file.name, audio_array, 22050)
            
            # Convert to MP3 if needed
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as mp3_file:
                subprocess.run([
                    "ffmpeg", "-i", tmp_file.name,
                    "-acodec", "libmp3lame", "-b:a", "128k",
                    "-y", mp3_file.name
                ], check=True, capture_output=True)
                
                with open(mp3_file.name, "rb") as f:
                    audio_bytes = f.read()
                
                # Cleanup
                os.unlink(tmp_file.name)
                os.unlink(mp3_file.name)
                
                return audio_bytes
                
    except Exception as e:
        logger.error(f"Speech generation error: {e}")
        raise

@app.get("/")
async def root():
    return {
        "message": "Kokoro TTS GPU Server",
        "gpu": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "Not available",
        "models_loaded": len(models_cache)
    }

@app.get("/v1/models")
async def list_models():
    """List available TTS models"""
    return {
        "object": "list",
        "data": [
            {
                "id": "tts-1",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "kokoro"
            }
        ]
    }

@app.get("/v1/voices")
async def list_voices():
    """List available voices with details"""
    voices = []
    for voice_id, config in voice_configs.items():
        voices.append({
            "voice_id": voice_id,
            "name": voice_id.replace("_", " ").title(),
            "labels": config,
            "available": voice_id in models_cache
        })
    
    return {"voices": voices}

@app.post("/v1/audio/speech")
async def create_speech(request: TTSRequest):
    """Generate speech from text"""
    
    if len(request.input) > 5000:
        raise HTTPException(status_code=400, detail="Text too long (max 5000 characters)")
    
    if request.voice not in voice_configs:
        raise HTTPException(status_code=400, detail=f"Voice '{request.voice}' not available")
    
    if not models_cache:
        raise HTTPException(status_code=503, detail="TTS models not loaded")
    
    try:
        start_time = time.time()
        
        # Generate audio
        audio_bytes = await generate_speech_async(
            text=request.input,
            voice=request.voice,
            speed=request.speed
        )
        
        generation_time = time.time() - start_time
        logger.info(f"Generated {len(request.input)} chars in {generation_time:.3f}s")
        
        # Return audio
        media_type = "audio/mpeg" if request.response_format == "mp3" else "audio/wav"
        filename = f"speech.{request.response_format}"
        
        return StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type=media_type,
            headers={"Content-Disposition": f"inline; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"TTS generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    
    gpu_info = {}
    if torch.cuda.is_available():
        gpu_info = {
            "gpu_available": True,
            "gpu_name": torch.cuda.get_device_name(0),
            "gpu_memory_allocated": f"{torch.cuda.memory_allocated(0) / 1e9:.2f} GB",
            "gpu_memory_total": f"{torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB",
            "gpu_utilization": f"{torch.cuda.utilization(0)}%"
        }
    else:
        gpu_info = {"gpu_available": False}
    
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "models_loaded": len(models_cache),
        "available_voices": list(models_cache.keys()),
        "device": str(device),
        **gpu_info
    }

# Cleanup on shutdown
@app.on_event("shutdown")
async def cleanup():
    """Clean up GPU memory on shutdown"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    logger.info("Server shutdown completed")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8880)
EOF

# 8. Create docker-compose configuration
cat > docker-compose.yml << EOF
version: '3.8'

services:
  kokoro-tts:
    build: .
    container_name: kokoro-tts-gpu
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    ports:
      - "${PORT}:8880"
    volumes:
      - ./cache:/cache
      - ./logs:/logs
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8880/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
EOF

# 9. Create monitoring script
cat > monitor.sh << 'EOF'
#!/bin/bash

while true; do
    clear
    echo "=== Kokoro GPU Server Monitor ==="
    echo "Time: $(date)"
    echo ""
    
    # GPU status
    echo "GPU Status:"
    nvidia-smi --query-gpu=name,temperature.gpu,utilization.gpu,memory.used,memory.total --format=csv,noheader
    
    echo ""
    echo "Container Status:"
    docker ps --filter name=kokoro-tts-gpu --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "Health Check:"
    curl -s http://localhost:8880/health | python3 -c "import sys, json; data=json.load(sys.stdin); print(f\"Status: {data['status']}, Models: {data['models_loaded']}, GPU: {data.get('gpu_name', 'N/A')}\")" 2>/dev/null || echo "Service not responding"
    
    echo ""
    echo "Recent Logs:"
    docker logs kokoro-tts-gpu --tail 5 2>/dev/null || echo "No logs available"
    
    sleep 5
done
EOF
chmod +x monitor.sh

# 10. Build and start
echo "🏗️  Building Docker image..."
docker-compose build

echo "🚀 Starting Kokoro TTS service..."
docker-compose up -d

# 11. Wait for startup
echo "⏳ Waiting for service to start..."
sleep 30

# Test the service
echo "🧪 Testing service..."
for i in {1..10}; do
    if curl -s http://localhost:$PORT/health >/dev/null; then
        echo "✅ Service is running!"
        break
    fi
    echo "Waiting... ($i/10)"
    sleep 5
done

# 12. Create systemd service
cat > /etc/systemd/system/kokoro-tts.service << EOF
[Unit]
Description=Kokoro TTS GPU Server
After=docker.service
Requires=docker.service

[Service]
Type=forking
WorkingDirectory=/opt/kokoro
ExecStartPre=/usr/local/bin/docker-compose down
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable kokoro-tts

# 13. Configure firewall
echo "🔒 Configuring firewall..."
ufw allow $PORT/tcp
ufw --force enable

# 14. Final status check
echo ""
echo "🎉 Kokoro GPU Server Deployment Complete!"
echo "========================================"
echo ""

# Display service info
echo "📊 Service Information:"
echo "- URL: http://$SERVER_IP:$PORT"
echo "- Health: http://$SERVER_IP:$PORT/health"
echo "- GPU Monitor: cd /opt/kokoro && ./monitor.sh"
echo ""

# Test the deployment
echo "🧪 Final Test:"
response=$(curl -s http://localhost:$PORT/health)
if echo "$response" | grep -q "healthy"; then
    echo "✅ Service is healthy and ready!"
    
    # Show GPU info
    gpu_name=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('gpu_name', 'Unknown'))" 2>/dev/null)
    models_count=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('models_loaded', 0))" 2>/dev/null)
    
    echo "- GPU: $gpu_name"
    echo "- Models loaded: $models_count"
else
    echo "⚠️  Service may still be starting. Check logs:"
    echo "   docker logs kokoro-tts-gpu"
fi

echo ""
echo "📋 Management Commands:"
echo "- Start: systemctl start kokoro-tts"
echo "- Stop: systemctl stop kokoro-tts"
echo "- Status: systemctl status kokoro-tts"
echo "- Logs: docker logs kokoro-tts-gpu"
echo "- Monitor: cd /opt/kokoro && ./monitor.sh"
echo ""

# Create quick test script
cat > /opt/kokoro/test-api.sh << EOF
#!/bin/bash
echo "Testing Kokoro TTS API..."

curl -X POST http://localhost:$PORT/v1/audio/speech \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "tts-1",
    "input": "Hello from your GPU accelerated Kokoro server! The RTX 4090 is working perfectly.",
    "voice": "af_sky"
  }' \\
  -o test-output.mp3

if [ -f test-output.mp3 ]; then
    echo "✅ Audio generated successfully!"
    echo "File size: \$(ls -lh test-output.mp3 | awk '{print \$5}')"
else
    echo "❌ Failed to generate audio"
fi
EOF
chmod +x /opt/kokoro/test-api.sh

echo "🧪 Run API test: /opt/kokoro/test-api.sh"
echo ""
echo "🚀 Your Kokoro GPU server is ready!"