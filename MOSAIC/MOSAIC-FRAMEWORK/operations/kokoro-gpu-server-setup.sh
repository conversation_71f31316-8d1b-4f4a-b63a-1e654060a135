#!/bin/bash
# Kokoro TTS GPU Server Setup with RTX 4090
# This script sets up Kokoro TTS with GPU acceleration on a server

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 Kokoro TTS GPU Server Setup${NC}"
echo -e "${YELLOW}Optimized for NVIDIA RTX 4090${NC}"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root${NC}" 
   exit 1
fi

# System requirements check
echo -e "\n${GREEN}1. Checking system requirements...${NC}"

# Check for NVIDIA GPU
if ! command -v nvidia-smi &> /dev/null; then
    echo -e "${RED}NVIDIA drivers not found. Installing...${NC}"
    
    # Add NVIDIA repository
    distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
    curl -s -L https://nvidia.github.io/libnvidia-container/gpgkey | apt-key add -
    curl -s -L https://nvidia.github.io/libnvidia-container/$distribution/libnvidia-container.list | tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
    
    apt-get update
    apt-get install -y nvidia-driver-535 nvidia-container-toolkit
fi

# Verify GPU
echo -e "${BLUE}Detected GPU:${NC}"
nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    echo -e "\n${GREEN}2. Installing Docker...${NC}"
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    rm get-docker.sh
else
    echo -e "\n${GREEN}2. Docker already installed${NC}"
fi

# Configure Docker for GPU support
echo -e "\n${GREEN}3. Configuring Docker for GPU support...${NC}"
nvidia-ctk runtime configure --runtime=docker
systemctl restart docker

# Create directory structure
echo -e "\n${GREEN}4. Creating directory structure...${NC}"
mkdir -p /opt/kokoro/{models,cache,config,logs}
mkdir -p /etc/systemd/system

# Download Kokoro models
echo -e "\n${GREEN}5. Downloading Kokoro models...${NC}"
cd /opt/kokoro/models

# Download voice models (using HuggingFace)
echo "Downloading Kokoro voice models..."
cat > download_models.py << 'EOF'
import os
from huggingface_hub import snapshot_download

# Kokoro models repository
models = [
    "hexgrad/Kokoro-82M",  # Base model
]

for model in models:
    print(f"Downloading {model}...")
    snapshot_download(
        repo_id=model,
        local_dir=f"/opt/kokoro/models/{model.split('/')[-1]}",
        local_dir_use_symlinks=False
    )

print("Models downloaded successfully!")
EOF

pip3 install huggingface_hub
python3 download_models.py
rm download_models.py

# Create Dockerfile for GPU-accelerated Kokoro
echo -e "\n${GREEN}6. Creating GPU-optimized Dockerfile...${NC}"
cat > /opt/kokoro/Dockerfile << 'EOF'
FROM nvidia/cuda:12.2.0-runtime-ubuntu22.04

# Install Python and dependencies
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3-pip \
    git \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.11 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# Install Kokoro and dependencies
RUN pip3 install --no-cache-dir \
    kokoro-tts \
    torch==2.1.0+cu121 \
    torchaudio==2.1.0+cu121 \
    --index-url https://download.pytorch.org/whl/cu121

# Install API server dependencies
RUN pip3 install --no-cache-dir \
    fastapi \
    uvicorn \
    pydantic \
    numpy \
    scipy

# Copy models
COPY models /models

# Create API server
COPY kokoro_server.py /app/kokoro_server.py

WORKDIR /app

# Expose port
EXPOSE 8880

# Set environment variables for GPU optimization
ENV CUDA_VISIBLE_DEVICES=0
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
ENV TORCH_CUDA_ARCH_LIST="8.9"  # RTX 4090 architecture

# Run the server
CMD ["python", "-m", "uvicorn", "kokoro_server:app", "--host", "0.0.0.0", "--port", "8880", "--workers", "1"]
EOF

# Create the Kokoro API server
echo -e "\n${GREEN}7. Creating Kokoro API server...${NC}"
cat > /opt/kokoro/kokoro_server.py << 'EOF'
"""
Kokoro TTS Server with GPU Acceleration
Optimized for NVIDIA RTX 4090
"""

import os
import io
import time
import logging
import torch
import numpy as np
from typing import Optional, List
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
import kokoro

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# GPU optimization settings
torch.backends.cudnn.benchmark = True
torch.backends.cuda.matmul.allow_tf32 = True

# Initialize FastAPI
app = FastAPI(title="Kokoro TTS Server", version="1.0.0")

# Check GPU availability
if torch.cuda.is_available():
    device = torch.device("cuda")
    logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
    logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
else:
    device = torch.device("cpu")
    logger.warning("GPU not available, using CPU")

# Load Kokoro models
logger.info("Loading Kokoro models...")
models = {}
voices = {}

# Initialize models with GPU support
try:
    # Load voice models
    for voice_name in ["af_sky", "af_sarah", "am_adam", "am_michael", 
                       "bf_emma", "bf_isabella", "bm_george", "bm_lewis"]:
        logger.info(f"Loading voice: {voice_name}")
        voice_model = kokoro.load_voice(voice_name)
        if torch.cuda.is_available():
            voice_model = voice_model.to(device)
        voices[voice_name] = voice_model
    
    logger.info("All models loaded successfully")
except Exception as e:
    logger.error(f"Error loading models: {e}")

# Request models
class TTSRequest(BaseModel):
    model: str = "tts-1"
    input: str
    voice: str = "af_sky"
    response_format: Optional[str] = "mp3"
    speed: Optional[float] = 1.0

class ModelsResponse(BaseModel):
    object: str = "list"
    data: List[dict]

class VoicesResponse(BaseModel):
    voices: List[dict]

@app.get("/")
async def root():
    return {"message": "Kokoro TTS Server with GPU Acceleration"}

@app.get("/v1/models")
async def list_models():
    """List available TTS models"""
    return ModelsResponse(
        data=[
            {
                "id": "tts-1",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "kokoro"
            }
        ]
    )

@app.get("/v1/voices")
async def list_voices():
    """List available voices"""
    voice_list = []
    for voice_name in voices.keys():
        voice_list.append({
            "voice_id": voice_name,
            "name": voice_name.replace("_", " ").title(),
            "labels": {
                "accent": voice_name[:2],
                "gender": "female" if voice_name[1] == "f" else "male",
                "age": "young"
            },
            "preview_url": None
        })
    
    return {"voices": voice_list}

@app.post("/v1/audio/speech")
async def create_speech(request: TTSRequest):
    """Generate speech from text using GPU acceleration"""
    try:
        # Validate voice
        if request.voice not in voices:
            raise HTTPException(status_code=400, detail=f"Voice '{request.voice}' not found")
        
        # Log request
        logger.info(f"TTS request: {len(request.input)} chars, voice: {request.voice}")
        
        # Generate audio with GPU
        start_time = time.time()
        
        with torch.cuda.amp.autocast():  # Mixed precision for faster inference
            voice_model = voices[request.voice]
            audio_data = kokoro.generate(
                text=request.input,
                voice=voice_model,
                speed=request.speed,
                device=device
            )
        
        generation_time = time.time() - start_time
        logger.info(f"Generated audio in {generation_time:.2f}s")
        
        # Convert to requested format
        if request.response_format == "mp3":
            # Convert to MP3
            import subprocess
            import tempfile
            
            with tempfile.NamedTemporaryFile(suffix=".wav") as tmp_wav:
                kokoro.save_audio(audio_data, tmp_wav.name)
                
                with tempfile.NamedTemporaryFile(suffix=".mp3") as tmp_mp3:
                    subprocess.run([
                        "ffmpeg", "-i", tmp_wav.name,
                        "-acodec", "libmp3lame",
                        "-b:a", "128k",
                        tmp_mp3.name, "-y"
                    ], check=True)
                    
                    with open(tmp_mp3.name, "rb") as f:
                        mp3_data = f.read()
            
            return StreamingResponse(
                io.BytesIO(mp3_data),
                media_type="audio/mpeg",
                headers={"Content-Disposition": "inline; filename=speech.mp3"}
            )
        
        else:
            # Return WAV
            wav_buffer = io.BytesIO()
            kokoro.save_audio(audio_data, wav_buffer)
            wav_buffer.seek(0)
            
            return StreamingResponse(
                wav_buffer,
                media_type="audio/wav",
                headers={"Content-Disposition": "inline; filename=speech.wav"}
            )
    
    except Exception as e:
        logger.error(f"TTS error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint with GPU status"""
    gpu_status = {}
    
    if torch.cuda.is_available():
        gpu_status = {
            "gpu_available": True,
            "gpu_name": torch.cuda.get_device_name(0),
            "gpu_memory_allocated": f"{torch.cuda.memory_allocated(0) / 1e9:.2f} GB",
            "gpu_memory_cached": f"{torch.cuda.memory_reserved(0) / 1e9:.2f} GB",
            "gpu_memory_total": f"{torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB"
        }
    else:
        gpu_status = {"gpu_available": False}
    
    return {
        "status": "healthy",
        "models_loaded": len(voices),
        "device": str(device),
        **gpu_status
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8880, workers=1)
EOF

# Create docker-compose.yml
echo -e "\n${GREEN}8. Creating docker-compose configuration...${NC}"
cat > /opt/kokoro/docker-compose.yml << 'EOF'
version: '3.8'

services:
  kokoro-tts:
    build: .
    container_name: kokoro-tts-gpu
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    ports:
      - "8880:8880"
    volumes:
      - ./models:/models:ro
      - ./cache:/cache
      - ./logs:/logs
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8880/health"]
      interval: 30s
      timeout: 10s
      retries: 3
EOF

# Create systemd service
echo -e "\n${GREEN}9. Creating systemd service...${NC}"
cat > /etc/systemd/system/kokoro-tts.service << 'EOF'
[Unit]
Description=Kokoro TTS Server with GPU
After=docker.service
Requires=docker.service

[Service]
Type=forking
WorkingDirectory=/opt/kokoro
ExecStartPre=/usr/bin/docker-compose down
ExecStart=/usr/bin/docker-compose up -d
ExecStop=/usr/bin/docker-compose down
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Create nginx configuration for reverse proxy
echo -e "\n${GREEN}10. Creating nginx configuration...${NC}"
cat > /opt/kokoro/nginx.conf << 'EOF'
server {
    listen 80;
    server_name kokoro.yourdomain.com;  # Change this
    
    location / {
        proxy_pass http://localhost:8880;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings for long audio generation
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:8880/health;
        access_log off;
    }
}
EOF

# Create monitoring script
echo -e "\n${GREEN}11. Creating GPU monitoring script...${NC}"
cat > /opt/kokoro/monitor_gpu.sh << 'EOF'
#!/bin/bash
# Monitor GPU usage and performance

while true; do
    clear
    echo "=== Kokoro TTS GPU Monitor ==="
    echo "Time: $(date)"
    echo ""
    
    # GPU Status
    nvidia-smi --query-gpu=name,temperature.gpu,utilization.gpu,utilization.memory,memory.used,memory.total --format=csv,noheader,nounits
    
    echo ""
    echo "=== Docker Container Status ==="
    docker ps --filter name=kokoro-tts-gpu --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "=== Recent Logs ==="
    docker logs kokoro-tts-gpu --tail 10 2>&1
    
    sleep 5
done
EOF

chmod +x /opt/kokoro/monitor_gpu.sh

# Build and start the service
echo -e "\n${GREEN}12. Building Docker image...${NC}"
cd /opt/kokoro
docker-compose build

echo -e "\n${GREEN}13. Starting Kokoro TTS service...${NC}"
systemctl daemon-reload
systemctl enable kokoro-tts
systemctl start kokoro-tts

# Wait for service to start
echo -e "\n${BLUE}Waiting for service to start...${NC}"
sleep 10

# Test the service
echo -e "\n${GREEN}14. Testing the service...${NC}"
curl -s http://localhost:8880/health | python3 -m json.tool

echo -e "\n${GREEN}✅ Kokoro TTS GPU Server Setup Complete!${NC}"
echo ""
echo -e "${BLUE}Service Information:${NC}"
echo "- API Endpoint: http://localhost:8880"
echo "- Health Check: http://localhost:8880/health"
echo "- GPU Monitor: /opt/kokoro/monitor_gpu.sh"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Configure your firewall to allow port 8880"
echo "2. Set up SSL/TLS with Let's Encrypt"
echo "3. Update nginx configuration with your domain"
echo "4. Configure your client to use the new endpoint"
echo ""
echo -e "${GREEN}GPU Optimization Tips:${NC}"
echo "- The RTX 4090 can handle multiple concurrent requests"
echo "- Mixed precision (FP16) is enabled for faster inference"
echo "- Monitor GPU memory usage with: nvidia-smi -l 1"
echo "- Adjust batch size in kokoro_server.py for your workload"