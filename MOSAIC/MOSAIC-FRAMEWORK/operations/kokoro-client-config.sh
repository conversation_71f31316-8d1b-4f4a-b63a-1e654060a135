#!/bin/bash
# Configure local system to use remote Kokoro GPU server

# Configuration
KOKORO_SERVER_URL="http://your-server-ip:8880/v1"  # Change this to your server IP
LOCAL_PROXY_PORT=8881

echo "🔧 Configuring Kokoro client to use GPU server"

# 1. Update environment variables
cat >> ~/.zshrc << EOF

# Kokoro GPU Server Configuration
export KOKORO_TTS_ENDPOINT="${KOKORO_SERVER_URL}"
export VOICEMODE_TTS_BASE_URL="${KOKORO_SERVER_URL}"
export VOICEMODE_PREFER_LOCAL_TTS=false
EOF

# 2. Create local proxy service (optional - for compatibility)
cat > ~/kokoro-proxy.py << 'EOF'
#!/usr/bin/env python3
"""
Local proxy for Kokoro GPU server
Forwards requests from local port to remote GPU server
"""

import os
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
import httpx
import uvicorn

app = FastAPI()

# Get server URL from environment
KOKORO_SERVER = os.getenv("KOKORO_TTS_ENDPOINT", "http://localhost:8880/v1")
client = httpx.AsyncClient(timeout=60.0)

@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def proxy(request: Request, path: str):
    """Forward all requests to the GPU server"""
    
    # Build target URL
    url = f"{KOKORO_SERVER}/{path}"
    
    # Forward the request
    response = await client.request(
        method=request.method,
        url=url,
        headers=dict(request.headers),
        content=await request.body(),
        params=dict(request.query_params)
    )
    
    # Return the response
    return StreamingResponse(
        response.iter_bytes(),
        status_code=response.status_code,
        headers=dict(response.headers),
        media_type=response.headers.get("content-type")
    )

if __name__ == "__main__":
    print(f"Starting Kokoro proxy on port {os.getenv('LOCAL_PROXY_PORT', 8881)}")
    print(f"Forwarding to: {KOKORO_SERVER}")
    uvicorn.run(app, host="127.0.0.1", port=int(os.getenv("LOCAL_PROXY_PORT", 8881)))
EOF

chmod +x ~/kokoro-proxy.py

# 3. Create benchmark script
cat > ~/benchmark-kokoro-gpu.py << 'EOF'
#!/usr/bin/env python3
"""
Benchmark Kokoro GPU server performance
"""

import time
import requests
import statistics
import concurrent.futures
from typing import List

# Test configuration
SERVER_URL = os.getenv("KOKORO_TTS_ENDPOINT", "http://localhost:8880/v1")
TEST_TEXTS = [
    "Hello, this is a test of the GPU accelerated text to speech system.",
    "The quick brown fox jumps over the lazy dog.",
    "GPU acceleration makes text to speech much faster and more efficient.",
    "This is a longer text to test the performance with more content. " * 5
]

def benchmark_single_request(text: str, voice: str = "af_sky") -> float:
    """Benchmark a single TTS request"""
    start_time = time.time()
    
    response = requests.post(
        f"{SERVER_URL}/audio/speech",
        json={
            "model": "tts-1",
            "input": text,
            "voice": voice,
            "response_format": "mp3"
        },
        timeout=30
    )
    
    if response.status_code != 200:
        raise Exception(f"Request failed: {response.status_code}")
    
    return time.time() - start_time

def run_benchmark():
    """Run comprehensive benchmark"""
    print("🚀 Kokoro GPU Server Benchmark")
    print(f"Server: {SERVER_URL}")
    print("-" * 50)
    
    # Check server health
    health = requests.get(f"{SERVER_URL.replace('/v1', '')}/health").json()
    print(f"GPU: {health.get('gpu_name', 'Not available')}")
    print(f"GPU Memory: {health.get('gpu_memory_total', 'Unknown')}")
    print("-" * 50)
    
    # Single request benchmarks
    print("\n📊 Single Request Performance:")
    for text in TEST_TEXTS:
        times = []
        for _ in range(3):
            duration = benchmark_single_request(text)
            times.append(duration)
        
        avg_time = statistics.mean(times)
        print(f"Text length {len(text)} chars: {avg_time:.3f}s (avg of 3 runs)")
    
    # Concurrent request benchmark
    print("\n📊 Concurrent Request Performance:")
    concurrent_counts = [1, 2, 4, 8]
    
    for count in concurrent_counts:
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=count) as executor:
            futures = []
            for i in range(count):
                text = TEST_TEXTS[i % len(TEST_TEXTS)]
                futures.append(executor.submit(benchmark_single_request, text))
            
            concurrent.futures.wait(futures)
        
        total_time = time.time() - start_time
        print(f"{count} concurrent requests: {total_time:.3f}s total")
    
    print("\n✅ Benchmark complete!")

if __name__ == "__main__":
    import os
    run_benchmark()
EOF

chmod +x ~/benchmark-kokoro-gpu.py

# 4. Create systemd service for local proxy (optional)
sudo tee /etc/systemd/system/kokoro-proxy.service << EOF
[Unit]
Description=Kokoro GPU Server Proxy
After=network.target

[Service]
Type=simple
User=$USER
Environment="KOKORO_TTS_ENDPOINT=${KOKORO_SERVER_URL}"
Environment="LOCAL_PROXY_PORT=${LOCAL_PROXY_PORT}"
ExecStart=/usr/bin/python3 $HOME/kokoro-proxy.py
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 5. Create connection test script
cat > ~/test-kokoro-gpu.sh << 'EOF'
#!/bin/bash

echo "🧪 Testing Kokoro GPU Server Connection"
echo ""

# Test health endpoint
echo "1. Testing health endpoint..."
curl -s "${KOKORO_TTS_ENDPOINT%/v1}/health" | python3 -m json.tool

echo ""
echo "2. Testing voice list..."
curl -s "${KOKORO_TTS_ENDPOINT}/voices" | python3 -m json.tool | head -20

echo ""
echo "3. Testing TTS generation..."
curl -X POST "${KOKORO_TTS_ENDPOINT}/audio/speech" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "tts-1",
    "input": "Hello from the GPU accelerated Kokoro server!",
    "voice": "af_sky"
  }' \
  -o test_output.mp3

if [ -f test_output.mp3 ]; then
    echo "✅ Audio file generated successfully!"
    echo "Size: $(ls -lh test_output.mp3 | awk '{print $5}')"
    
    # Play if on macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        afplay test_output.mp3
    fi
else
    echo "❌ Failed to generate audio"
fi

echo ""
echo "✅ Test complete!"
EOF

chmod +x ~/test-kokoro-gpu.sh

echo ""
echo "✅ Kokoro GPU client configuration complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update KOKORO_SERVER_URL in this script with your server's IP"
echo "2. Run: source ~/.zshrc"
echo "3. Test connection: ~/test-kokoro-gpu.sh"
echo "4. Run benchmark: ~/benchmark-kokoro-gpu.py"
echo ""
echo "🔧 Optional:"
echo "- Start local proxy: python3 ~/kokoro-proxy.py"
echo "- Enable proxy service: sudo systemctl enable --now kokoro-proxy"