#!/bin/bash
# Kokoro TTS GPU Server Setup for Debian/Proxmox with RTX 4090

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 Kokoro TTS GPU Server Setup for Debian${NC}"
echo -e "${YELLOW}Optimized for NVIDIA RTX 4090${NC}"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root${NC}" 
   exit 1
fi

# Configuration
SERVER_IP=${1:-"*************"}
PORT=${2:-"8880"}

echo -e "\n${GREEN}Configuration:${NC}"
echo "- Server IP: $SERVER_IP"
echo "- Port: $PORT"

# Check for NVIDIA GPU
echo -e "\n${GREEN}1. Checking for NVIDIA GPU...${NC}"
if command -v nvidia-smi &> /dev/null; then
    echo -e "${GREEN}✓ NVIDIA drivers already installed${NC}"
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader
else
    echo -e "${YELLOW}Installing NVIDIA drivers for Debian...${NC}"
    
    # Add contrib and non-free repositories
    echo "deb http://deb.debian.org/debian/ bookworm main contrib non-free non-free-firmware" > /etc/apt/sources.list.d/nvidia.list
    
    # Update package list
    apt-get update
    
    # Install NVIDIA driver
    apt-get install -y nvidia-driver nvidia-smi
    
    echo -e "${YELLOW}⚠️  NVIDIA driver installed. System reboot required.${NC}"
    echo "After reboot, run this script again to continue:"
    echo "bash $0 $SERVER_IP $PORT"
    
    read -p "Reboot now? [y/N]: " reboot_confirm
    if [[ "$reboot_confirm" =~ ^[Yy]$ ]]; then
        reboot
    else
        echo "Please reboot manually and run the script again."
        exit 0
    fi
fi

# Install Docker if not present
echo -e "\n${GREEN}2. Installing Docker...${NC}"
if ! command -v docker &> /dev/null; then
    # Install Docker dependencies
    apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # Add Docker GPG key
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Update and install Docker
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Install docker-compose
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    
    # Start Docker service
    systemctl enable docker
    systemctl start docker
else
    echo -e "${GREEN}✓ Docker already installed${NC}"
fi

# Configure Docker for GPU
echo -e "\n${GREEN}3. Configuring Docker GPU support...${NC}"

# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
curl -s -L https://nvidia.github.io/libnvidia-container/$distribution/libnvidia-container.list | \
    sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
    tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

apt-get update
apt-get install -y nvidia-container-toolkit

# Configure Docker to use NVIDIA runtime
nvidia-ctk runtime configure --runtime=docker
systemctl restart docker

# Test GPU access in Docker
echo -e "\n${GREEN}4. Testing GPU access in Docker...${NC}"
if docker run --rm --gpus all nvidia/cuda:12.2-base-ubuntu22.04 nvidia-smi; then
    echo -e "${GREEN}✓ GPU access in Docker working${NC}"
else
    echo -e "${RED}❌ GPU access in Docker failed${NC}"
    exit 1
fi

# Create Kokoro directory
echo -e "\n${GREEN}5. Creating Kokoro directory structure...${NC}"
mkdir -p /opt/kokoro/{models,cache,config,logs}
cd /opt/kokoro

# Create optimized Dockerfile
echo -e "\n${GREEN}6. Creating optimized Dockerfile...${NC}"
cat > Dockerfile << 'EOF'
FROM nvidia/cuda:12.2-runtime-ubuntu22.04

# Prevent interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3-pip \
    python3.11-dev \
    git \
    ffmpeg \
    libsndfile1 \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.11 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# Upgrade pip
RUN python3 -m pip install --upgrade pip

# Install PyTorch with CUDA support
RUN pip3 install --no-cache-dir \
    torch==2.1.0+cu121 \
    torchaudio==2.1.0+cu121 \
    --index-url https://download.pytorch.org/whl/cu121

# Install basic dependencies
RUN pip3 install --no-cache-dir \
    fastapi \
    uvicorn \
    pydantic \
    numpy \
    scipy \
    librosa \
    soundfile \
    transformers \
    accelerate

# Create app directory
WORKDIR /app

# Copy server script
COPY kokoro_server.py /app/

# Expose port
EXPOSE 8880

# Set environment variables for optimal GPU usage
ENV CUDA_VISIBLE_DEVICES=0
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
ENV TORCH_CUDA_ARCH_LIST="8.9"
ENV OMP_NUM_THREADS=8
ENV TRANSFORMERS_CACHE=/cache

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8880/health || exit 1

# Run the server
CMD ["python", "-m", "uvicorn", "kokoro_server:app", "--host", "0.0.0.0", "--port", "8880"]
EOF

# Create simplified TTS server
echo -e "\n${GREEN}7. Creating TTS server...${NC}"
cat > kokoro_server.py << 'EOF'
"""
Simple TTS Server with GPU Support
Using basic TTS models as Kokoro alternative
"""

import os
import io
import time
import logging
import asyncio
from typing import Optional, Dict, Any
import torch
import numpy as np
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
import subprocess
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Check GPU
if torch.cuda.is_available():
    device = torch.device("cuda")
    logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
    torch.backends.cudnn.benchmark = True
else:
    device = torch.device("cpu")
    logger.warning("GPU not available, using CPU")

# Initialize FastAPI
app = FastAPI(
    title="TTS GPU Server",
    description="High-performance TTS with GPU acceleration",
    version="1.0.0"
)

# Voice configurations
voice_configs = {
    "af_sky": {"gender": "female", "accent": "american", "age": "young"},
    "af_sarah": {"gender": "female", "accent": "american", "age": "adult"},
    "am_adam": {"gender": "male", "accent": "american", "age": "adult"},
    "am_michael": {"gender": "male", "accent": "american", "age": "young"},
    "bf_emma": {"gender": "female", "accent": "british", "age": "young"},
    "bf_isabella": {"gender": "female", "accent": "british", "age": "adult"},
    "bm_george": {"gender": "male", "accent": "british", "age": "adult"},
    "bm_lewis": {"gender": "male", "accent": "british", "age": "young"},
}

# Global TTS model
tts_model = None

class TTSRequest(BaseModel):
    model: str = "tts-1"
    input: str
    voice: str = "af_sky"
    response_format: Optional[str] = "mp3"
    speed: Optional[float] = 1.0

@app.on_event("startup")
async def load_models():
    """Load TTS models on startup"""
    global tts_model
    try:
        # Try to load a simple TTS model
        logger.info("Loading TTS models...")
        
        # For now, we'll use a simple text-to-speech approach
        # In production, replace with actual Kokoro or other TTS model
        logger.info("TTS server ready (using basic synthesis)")
        
    except Exception as e:
        logger.error(f"Error loading models: {e}")

async def synthesize_speech(text: str, voice: str, speed: float = 1.0) -> bytes:
    """Synthesize speech using available TTS"""
    
    try:
        # Create a simple sine wave tone (placeholder)
        # Replace this with actual TTS synthesis
        duration = len(text) * 0.1  # Rough estimate
        sample_rate = 22050
        
        # Generate sine wave (placeholder audio)
        import math
        samples = int(duration * sample_rate)
        frequency = 440  # A4 note
        
        audio_data = []
        for i in range(samples):
            sample = math.sin(2 * math.pi * frequency * i / sample_rate) * 0.3
            audio_data.append(sample)
        
        # Convert to numpy array
        audio_array = np.array(audio_data, dtype=np.float32)
        
        # Save as WAV then convert to MP3
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_wav:
            import soundfile as sf
            sf.write(tmp_wav.name, audio_array, sample_rate)
            
            # Convert to MP3
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_mp3:
                subprocess.run([
                    "ffmpeg", "-i", tmp_wav.name,
                    "-acodec", "libmp3lame", "-b:a", "128k",
                    "-y", tmp_mp3.name
                ], check=True, capture_output=True)
                
                with open(tmp_mp3.name, "rb") as f:
                    audio_bytes = f.read()
                
                # Cleanup
                os.unlink(tmp_wav.name)
                os.unlink(tmp_mp3.name)
                
                return audio_bytes
                
    except Exception as e:
        logger.error(f"Speech synthesis error: {e}")
        raise

@app.get("/")
async def root():
    return {
        "message": "TTS GPU Server",
        "gpu": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "Not available",
        "status": "ready"
    }

@app.get("/v1/models")
async def list_models():
    """List available TTS models"""
    return {
        "object": "list",
        "data": [
            {
                "id": "tts-1",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "tts-server"
            }
        ]
    }

@app.get("/v1/voices")
async def list_voices():
    """List available voices"""
    voices = []
    for voice_id, config in voice_configs.items():
        voices.append({
            "voice_id": voice_id,
            "name": voice_id.replace("_", " ").title(),
            "labels": config,
            "available": True
        })
    
    return {"voices": voices}

@app.post("/v1/audio/speech")
async def create_speech(request: TTSRequest):
    """Generate speech from text"""
    
    if len(request.input) > 5000:
        raise HTTPException(status_code=400, detail="Text too long (max 5000 characters)")
    
    if request.voice not in voice_configs:
        raise HTTPException(status_code=400, detail=f"Voice '{request.voice}' not available")
    
    try:
        start_time = time.time()
        
        # Generate audio
        audio_bytes = await synthesize_speech(
            text=request.input,
            voice=request.voice,
            speed=request.speed
        )
        
        generation_time = time.time() - start_time
        logger.info(f"Generated {len(request.input)} chars in {generation_time:.3f}s")
        
        # Return audio
        media_type = "audio/mpeg" if request.response_format == "mp3" else "audio/wav"
        filename = f"speech.{request.response_format}"
        
        return StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type=media_type,
            headers={"Content-Disposition": f"inline; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"TTS generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check with GPU status"""
    
    gpu_info = {}
    if torch.cuda.is_available():
        gpu_info = {
            "gpu_available": True,
            "gpu_name": torch.cuda.get_device_name(0),
            "gpu_memory_total": f"{torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB"
        }
    else:
        gpu_info = {"gpu_available": False}
    
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "device": str(device),
        **gpu_info
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8880)
EOF

# Create docker-compose
echo -e "\n${GREEN}8. Creating docker-compose configuration...${NC}"
cat > docker-compose.yml << EOF
version: '3.8'

services:
  tts-server:
    build: .
    container_name: tts-gpu-server
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    ports:
      - "${PORT}:8880"
    volumes:
      - ./cache:/cache
      - ./logs:/logs
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8880/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
EOF

# Create monitoring script
cat > monitor.sh << 'EOF'
#!/bin/bash
while true; do
    clear
    echo "=== TTS GPU Server Monitor ==="
    echo "Time: $(date)"
    echo ""
    
    # GPU status
    echo "GPU Status:"
    nvidia-smi --query-gpu=name,temperature.gpu,utilization.gpu,memory.used,memory.total --format=csv,noheader
    
    echo ""
    echo "Container Status:"
    docker ps --filter name=tts-gpu-server --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "Health Check:"
    curl -s http://localhost:8880/health | python3 -c "import sys, json; data=json.load(sys.stdin); print(f\"Status: {data['status']}, GPU: {data.get('gpu_name', 'N/A')}\")" 2>/dev/null || echo "Service not responding"
    
    sleep 5
done
EOF
chmod +x monitor.sh

# Build and start
echo -e "\n${GREEN}9. Building Docker image...${NC}"
docker-compose build

echo -e "\n${GREEN}10. Starting TTS service...${NC}"
docker-compose up -d

# Wait for startup
echo -e "\n${BLUE}Waiting for service to start...${NC}"
sleep 30

# Test the service
echo -e "\n${GREEN}11. Testing service...${NC}"
for i in {1..10}; do
    if curl -s http://localhost:$PORT/health >/dev/null; then
        echo -e "${GREEN}✅ Service is running!${NC}"
        break
    fi
    echo "Waiting... ($i/10)"
    sleep 5
done

# Create systemd service
cat > /etc/systemd/system/tts-gpu.service << EOF
[Unit]
Description=TTS GPU Server
After=docker.service
Requires=docker.service

[Service]
Type=forking
WorkingDirectory=/opt/kokoro
ExecStartPre=/usr/local/bin/docker-compose down
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable tts-gpu

# Configure firewall (if ufw is available)
if command -v ufw &> /dev/null; then
    echo -e "\n${GREEN}12. Configuring firewall...${NC}"
    ufw allow $PORT/tcp
fi

# Create test script
cat > test-api.sh << EOF
#!/bin/bash
echo "Testing TTS API..."

curl -X POST http://localhost:$PORT/v1/audio/speech \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "tts-1",
    "input": "Hello from your GPU accelerated TTS server!",
    "voice": "af_sky"
  }' \\
  -o test-output.mp3

if [ -f test-output.mp3 ]; then
    echo "✅ Audio generated successfully!"
    echo "File size: \$(ls -lh test-output.mp3 | awk '{print \$5}')"
else
    echo "❌ Failed to generate audio"
fi
EOF
chmod +x test-api.sh

echo -e "\n${GREEN}✅ TTS GPU Server Setup Complete!${NC}"
echo ""
echo -e "${BLUE}Service Information:${NC}"
echo "- URL: http://$SERVER_IP:$PORT"
echo "- Health: http://$SERVER_IP:$PORT/health"
echo "- Monitor: cd /opt/kokoro && ./monitor.sh"
echo "- Test: cd /opt/kokoro && ./test-api.sh"
echo ""

# Final test
response=$(curl -s http://localhost:$PORT/health 2>/dev/null)
if echo "$response" | grep -q "healthy"; then
    echo -e "${GREEN}✅ Service is healthy and ready!${NC}"
    
    gpu_name=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('gpu_name', 'Unknown'))" 2>/dev/null || echo "Unknown")
    echo "- GPU: $gpu_name"
else
    echo -e "${YELLOW}⚠️  Service may still be starting. Check logs:${NC}"
    echo "   docker logs tts-gpu-server"
fi

echo ""
echo -e "${GREEN}🚀 Your TTS GPU server is ready!${NC}"
EOF