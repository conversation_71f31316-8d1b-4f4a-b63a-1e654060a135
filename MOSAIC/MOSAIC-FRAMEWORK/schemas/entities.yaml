# ALIAS MOSAIC - Entity Model & Relationships
# Defines the core entities and their relationships across all lifecycles

entities:
  # Universal Entity Properties (inherited by all)
  base_entity:
    properties:
      id:
        type: "uuid"
        description: "Unique identifier"
        example: "550e8400-e29b-41d4-a716-************"
      
      type:
        type: "enum"
        values: ["project", "task", "person", "document", "insight", "event", "asset"]
        description: "Entity classification"
      
      lifecycle_origin:
        type: "string"
        description: "Which lifecycle created this entity"
        example: "apex-lc"
      
      metadata:
        created_at: "timestamp"
        updated_at: "timestamp"
        created_by: "persona_id"
        updated_by: "persona_id"
        version: "integer"
        tags: "array<string>"
      
      visibility:
        scope: ["private", "team", "organization", "public"]
        permissions: "rbac_rules"
      
      relationships:
        type: "array<relationship>"
        description: "Connections to other entities"
      
      embeddings:
        vector: "float_array[3072]"
        model: "string"
        generated_at: "timestamp"

  # See specific entity types in this file...
