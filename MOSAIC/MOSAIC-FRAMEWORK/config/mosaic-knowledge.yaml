# ALIAS MOSAIC - Knowledge Representation v1.0
# This YAML defines the complete structure and relationships of the ALIAS MOSAIC system

mosaic:
  name: "ALIAS MOSAIC"
  version: "1.0.0"
  description: "Modular Organizational System for AI-Integrated Cycles"
  tagline: "Focus on living. MOSAIC handles the rest."
  owner: "ALIAS"
  status: "active_development"
  visibility: "proprietary"
  
  # Core philosophical principles
  principles:
    - id: "ai-first"
      description: "Every process guided by intelligent AI personas"
      implementation: "AI personas in each lifecycle making autonomous decisions"
    
    - id: "local-first"
      description: "Data lives on device, syncs when connected"
      implementation: "SQLite + Zero for offline, Convex for sync"
    
    - id: "realtime-first"
      description: "All changes propagate instantly across devices"
      implementation: "Convex real-time subscriptions"
    
    - id: "privacy-first"
      description: "Your data, your control, your infrastructure"
      implementation: "End-to-end encryption, local processing"
    
    - id: "lifestyle-integrated"
      description: "Work and life flow together naturally"
      implementation: "FLOW-LC orchestrating contextual intelligence"

  # The 11 Lifecycles summary
  lifecycles_count: 11
  personas_count: 42
  
  # See detailed lifecycle definitions in ../lifecycles/
  # See entity model in ../schemas/entities.yaml
  # See operational config in ../operations/
