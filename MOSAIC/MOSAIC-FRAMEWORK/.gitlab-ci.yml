stages:
  - validate
  - test
  - deploy

variables:
  YAML_FILES: "**/*.yaml **/*.yml"

validate:yaml:
  stage: validate
  image: python:3.11
  before_script:
    - pip install yamllint pyyaml
  script:
    - yamllint -c .yamllint .
    - python scripts/validate_structure.py
  only:
    changes:
      - "**/*.yaml"
      - "**/*.yml"

test:references:
  stage: test
  image: python:3.11
  script:
    - python scripts/check_references.py
  only:
    changes:
      - "**/*.yaml"
      - "**/*.yml"

deploy:documentation:
  stage: deploy
  image: node:20
  script:
    - echo "Deploy documentation site"
  only:
    - main
  when: manual
