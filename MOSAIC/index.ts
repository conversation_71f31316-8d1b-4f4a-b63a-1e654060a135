/**
 * MOSAIC - Meta-Orchestration System for AI-Integrated Collaboration
 * Main orchestrator that coordinates all 11 lifecycles
 */

import { EventEmitter } from 'events';
import { readFileSync } from 'fs';
import { join } from 'path';
import yaml from 'js-yaml';

// Import lifecycle implementations
import ApexLifecycle from './apex-lc';
import PrismLifecycle from './prism-lc';
import PulseLifecycle from './pulse-lc';
import AuroraLifecycle from './aurora-lc';

// Import shared types and utilities
import { MosaicEvent, LifecycleId, SharedContext, LifecycleConfig, EventBus } from './shared/types';
import { EventBuilder, createEventBus } from './shared/events';
import { getMosaicInfrastructure } from './infrastructure';

// MOSAIC Configuration Interface
interface MosaicConfig {
  name: string;
  version: string;
  description: string;
  lifecycles: LifecycleConfig[];
  infrastructure: any;
  integration: any;
  development: any;
  metrics: any;
  features: any;
}

// Mock implementations for infrastructure components
class MockEventBus extends EventEmitter implements EventBus {
  private subscriptions: Map<string, Function[]> = new Map();

  async publish(event: MosaicEvent): Promise<void> {
    console.log(`📡 Event Published: ${event.type} from ${event.source}`);
    
    // Find matching subscriptions
    for (const [pattern, handlers] of this.subscriptions) {
      if (this.matchesPattern(event.type, pattern)) {
        for (const handler of handlers) {
          try {
            await handler(event);
          } catch (error) {
            console.error(`Error handling event ${event.type}:`, error);
          }
        }
      }
    }
  }

  async subscribe(pattern: string, handler: Function): Promise<void> {
    if (!this.subscriptions.has(pattern)) {
      this.subscriptions.set(pattern, []);
    }
    this.subscriptions.get(pattern)!.push(handler);
  }

  async unsubscribe(pattern: string, handler: Function): Promise<void> {
    const handlers = this.subscriptions.get(pattern);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  async request(event: MosaicEvent, timeout: number = 5000): Promise<MosaicEvent> {
    // Mock request-response pattern
    await this.publish(event);
    
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, timeout);
      
      // Mock response after 100ms
      setTimeout(() => {
        clearTimeout(timer);
        resolve({
          ...event,
          id: `response_${event.id}`,
          type: `${event.type}.response`,
          source: event.target as LifecycleId || 'unknown',
          target: event.source,
          payload: { status: 'success', original_request: event.id }
        });
      }, 100);
    });
  }

  async health(): Promise<{ status: string; metrics: Record<string, number> }> {
    return {
      status: 'healthy',
      metrics: {
        events_published: 100,
        events_processed: 95,
        subscriptions: this.subscriptions.size,
        error_rate: 0.01
      }
    };
  }

  private matchesPattern(eventType: string, pattern: string): boolean {
    if (pattern === '*') return true;
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return regex.test(eventType);
  }
}

class MockSharedContext implements SharedContext {
  private store: Map<string, any> = new Map();
  private subscribers: Map<string, Function[]> = new Map();

  async get<T>(key: string): Promise<T | null> {
    return this.store.get(key) || null;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    this.store.set(key, value);
    
    // Notify subscribers
    const pattern = this.findMatchingPattern(key);
    if (pattern) {
      const handlers = this.subscribers.get(pattern) || [];
      const entry = {
        key,
        value,
        lifecycle: 'unknown' as LifecycleId,
        timestamp: new Date(),
        version: 1,
        ttl
      };
      
      handlers.forEach(handler => {
        try {
          handler(entry);
        } catch (error) {
          console.error(`Error in context subscriber:`, error);
        }
      });
    }
  }

  async update<T>(key: string, updater: (current: T | null) => T): Promise<void> {
    const current = await this.get<T>(key);
    const updated = updater(current);
    await this.set(key, updated);
  }

  async delete(key: string): Promise<void> {
    this.store.delete(key);
  }

  subscribe(pattern: string, callback: Function): void {
    if (!this.subscribers.has(pattern)) {
      this.subscribers.set(pattern, []);
    }
    this.subscribers.get(pattern)!.push(callback);
  }

  unsubscribe(pattern: string): void {
    this.subscribers.delete(pattern);
  }

  private findMatchingPattern(key: string): string | null {
    for (const pattern of this.subscribers.keys()) {
      if (this.matchesPattern(key, pattern)) {
        return pattern;
      }
    }
    return null;
  }

  private matchesPattern(key: string, pattern: string): boolean {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return regex.test(key);
  }
}

// Main MOSAIC Orchestrator
export class MosaicOrchestrator extends EventEmitter {
  private config: MosaicConfig;
  private eventBus: EventBus;
  private context: SharedContext;
  private lifecycles: Map<LifecycleId, any> = new Map();
  private isRunning: boolean = false;
  private startTime: Date;

  constructor(configPath?: string, useKafka: boolean = false) {
    super();
    this.config = this.loadConfiguration(configPath);
    this.eventBus = createEventBus(useKafka);
    this.context = new MockSharedContext();
    this.startTime = new Date();

    this.setupGlobalEventHandlers();
  }

  // Load MOSAIC Configuration
  private loadConfiguration(configPath?: string): MosaicConfig {
    const defaultConfigPath = join(__dirname, 'config', 'mosaic.yaml');
    const path = configPath || defaultConfigPath;
    
    try {
      const configContent = readFileSync(path, 'utf8');
      return yaml.load(configContent) as MosaicConfig;
    } catch (error) {
      console.error(`Failed to load MOSAIC configuration from ${path}:`, error);
      throw error;
    }
  }

  // Setup Global Event Handlers
  private setupGlobalEventHandlers(): void {
    this.eventBus.subscribe('*', this.logEvent.bind(this));
    this.eventBus.subscribe('*.error.*', this.handleError.bind(this));
    this.eventBus.subscribe('mosaic.*', this.handleMosaicEvent.bind(this));
  }

  // Initialize and Start MOSAIC
  async start(): Promise<void> {
    if (this.isRunning) {
      console.warn('MOSAIC is already running');
      return;
    }

    console.log(`🚀 Starting MOSAIC v${this.config.version}`);
    console.log(`📋 Description: ${this.config.description}`);

    try {
      // Initialize infrastructure
      await this.initializeInfrastructure();

      // Initialize Phase 1 lifecycles
      await this.initializePhase1Lifecycles();

      // Start system monitoring
      await this.startSystemMonitoring();

      // Mark as running
      this.isRunning = true;

      // Publish startup event
      const startupEvent = EventBuilder.create('mosaic.system.started')
        .source('pulse-lc')
        .payload({
          version: this.config.version,
          started_at: this.startTime,
          active_lifecycles: Array.from(this.lifecycles.keys()),
          configuration: {
            total_lifecycles: this.config.lifecycles.length,
            active_phase: 1
          }
        })
        .priority('high')
        .build();

      await this.eventBus.publish(startupEvent);

      console.log('✅ MOSAIC started successfully');
      console.log(`📊 Active Lifecycles: ${Array.from(this.lifecycles.keys()).join(', ')}`);

      this.emit('started');

    } catch (error) {
      console.error('❌ Failed to start MOSAIC:', error);
      throw error;
    }
  }

  // Stop MOSAIC
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.warn('MOSAIC is not running');
      return;
    }

    console.log('🛑 Stopping MOSAIC...');

    try {
      // Publish shutdown event
      const shutdownEvent = EventBuilder.create('mosaic.system.stopping')
        .source('pulse-lc')
        .payload({
          stopped_at: new Date(),
          uptime: Date.now() - this.startTime.getTime(),
          active_lifecycles: Array.from(this.lifecycles.keys())
        })
        .priority('high')
        .build();

      await this.eventBus.publish(shutdownEvent);

      // Stop all lifecycles
      for (const [id, lifecycle] of this.lifecycles) {
        console.log(`  Stopping ${id}...`);
        if (lifecycle.stop) {
          await lifecycle.stop();
        }
      }

      // Clear lifecycles
      this.lifecycles.clear();
      this.isRunning = false;

      console.log('✅ MOSAIC stopped successfully');
      this.emit('stopped');

    } catch (error) {
      console.error('❌ Error stopping MOSAIC:', error);
      throw error;
    }
  }

  // Get System Status
  async getStatus(): Promise<any> {
    const uptime = this.isRunning ? Date.now() - this.startTime.getTime() : 0;
    
    return {
      running: this.isRunning,
      version: this.config.version,
      uptime: uptime,
      started_at: this.startTime,
      active_lifecycles: Array.from(this.lifecycles.keys()),
      total_lifecycles: this.config.lifecycles.length,
      event_bus_health: await this.eventBus.health(),
      phase: 1 // Currently in Phase 1
    };
  }

  // Process Feature Request (Main Entry Point)
  async processFeatureRequest(request: any): Promise<string> {
    if (!this.isRunning) {
      throw new Error('MOSAIC is not running');
    }

    const apexLifecycle = this.lifecycles.get('apex-lc');
    if (!apexLifecycle) {
      throw new Error('APEX-LC is not available');
    }

    console.log(`🎯 Processing feature request: ${request.title}`);
    
    try {
      const result = await apexLifecycle.processFeatureRequest(request);
      console.log(`✅ Feature request completed: ${result}`);
      return result;
    } catch (error) {
      console.error(`❌ Feature request failed:`, error);
      throw error;
    }
  }

  // Search Knowledge Base
  async searchKnowledge(query: any): Promise<any> {
    const prismLifecycle = this.lifecycles.get('prism-lc');
    if (!prismLifecycle) {
      throw new Error('PRISM-LC is not available');
    }

    return await prismLifecycle.search(query);
  }

  // Get System Health
  async getSystemHealth(): Promise<any> {
    const pulseLifecycle = this.lifecycles.get('pulse-lc');
    if (!pulseLifecycle) {
      throw new Error('PULSE-LC is not available');
    }

    return await pulseLifecycle.assessSystemHealth();
  }

  // Private Methods
  private async initializeInfrastructure(): Promise<void> {
    console.log('🔧 Initializing infrastructure...');

    try {
      // Initialize MOSAIC infrastructure if using Kafka
      if (this.eventBus.constructor.name === 'KafkaEventBus') {
        const infrastructure = getMosaicInfrastructure();
        await infrastructure.initialize();
        console.log('  ✅ MOSAIC Infrastructure (Kafka) initialized');
      } else {
        console.log('  ✅ Event Bus (Mock) initialized');
      }

      // Initialize context store (already done in constructor)
      console.log('  ✅ Context Store initialized');

      // Mock other infrastructure components
      console.log('  ✅ Monitoring initialized');
      console.log('  ✅ Security initialized');
    } catch (error) {
      console.error('❌ Failed to initialize infrastructure:', error);
      throw error;
    }
  }

  private async initializePhase1Lifecycles(): Promise<void> {
    console.log('🔄 Initializing Phase 1 lifecycles...');

    const phase1Lifecycles = this.config.lifecycles.filter(lc => lc.phase === 1 && lc.status === 'active');

    for (const lifecycleConfig of phase1Lifecycles) {
      await this.initializeLifecycle(lifecycleConfig);
    }
  }

  private async initializeLifecycle(config: LifecycleConfig): Promise<void> {
    console.log(`  🚀 Initializing ${config.name} (${config.id})...`);

    let lifecycle: any;

    switch (config.id) {
      case 'apex-lc':
        lifecycle = new ApexLifecycle(this.eventBus, this.context);
        break;
      case 'prism-lc':
        lifecycle = new PrismLifecycle(this.eventBus, this.context);
        break;
      case 'pulse-lc':
        lifecycle = new PulseLifecycle(this.eventBus, this.context);
        break;
      case 'aurora-lc':
        lifecycle = new AuroraLifecycle(this.eventBus, this.context);
        break;
      default:
        console.log(`    ⚠️  ${config.id} implementation not yet available`);
        return;
    }

    this.lifecycles.set(config.id, lifecycle);
    console.log(`    ✅ ${config.name} initialized`);
  }

  private async startSystemMonitoring(): Promise<void> {
    console.log('📊 Starting system monitoring...');
    
    // Start periodic health checks
    setInterval(async () => {
      try {
        const status = await this.getStatus();
        await this.context.set('mosaic.system_status', status);
      } catch (error) {
        console.error('Error updating system status:', error);
      }
    }, 30000); // Every 30 seconds

    console.log('  ✅ System monitoring started');
  }

  private async logEvent(event: MosaicEvent): Promise<void> {
    // Log all events for debugging (in production, this would be more selective)
    if (process.env.NODE_ENV === 'development') {
      console.log(`📝 Event: ${event.type} | ${event.source} → ${event.target || 'broadcast'}`);
    }
  }

  private async handleError(event: MosaicEvent): Promise<void> {
    console.error(`🚨 Error Event: ${event.type}`, event.payload);
    
    // In production, this would trigger alerting and recovery procedures
    this.emit('error', event);
  }

  private async handleMosaicEvent(event: MosaicEvent): Promise<void> {
    // Handle MOSAIC-specific events
    switch (event.type) {
      case 'mosaic.system.shutdown':
        await this.stop();
        break;
      case 'mosaic.lifecycle.restart':
        await this.restartLifecycle(event.payload.lifecycle);
        break;
      default:
        // Unknown MOSAIC event
        break;
    }
  }

  private async restartLifecycle(lifecycleId: LifecycleId): Promise<void> {
    console.log(`🔄 Restarting ${lifecycleId}...`);
    
    const lifecycle = this.lifecycles.get(lifecycleId);
    if (lifecycle && lifecycle.stop) {
      await lifecycle.stop();
    }
    
    const config = this.config.lifecycles.find(lc => lc.id === lifecycleId);
    if (config) {
      await this.initializeLifecycle(config);
    }
  }
}

// Export default instance
export default MosaicOrchestrator;
