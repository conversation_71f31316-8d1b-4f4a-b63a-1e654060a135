import { describe, it, expect } from 'vitest';

describe('Basic Test Suite', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should handle async operations', async () => {
    const result = await Promise.resolve('test');
    expect(result).toBe('test');
  });

  it('should validate environment setup', () => {
    expect(process.env.NODE_ENV).toBe('test');
    expect(process.env.MOSAIC_ENV).toBe('test');
  });
});
