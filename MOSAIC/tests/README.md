# MOSAIC Integration Testing Suite

## Overview

This comprehensive testing suite validates the integration and coordination patterns across all MOSAIC lifecycles, infrastructure components, and CLI tools.

## Test Structure

```
tests/
├── integration/                    # Integration tests
│   ├── cross-lifecycle-communication.test.ts
│   ├── infrastructure-integration.test.ts
│   └── cli-integration.test.ts
├── unit/                          # Unit tests
│   └── basic.test.ts
├── setup.ts                       # Global test setup
├── vitest.config.ts              # Test configuration
├── package.json                   # Test dependencies
├── .env.test                      # Test environment variables
└── run-integration-tests.sh       # Test runner script

```

## Test Categories

### 1. Cross-Lifecycle Communication Tests
- **APEX-LC to PRISM-LC**: Development insights sharing and knowledge requests
- **APEX-LC to AURORA-LC**: Feature deployment notifications and customer feedback
- **PULSE-LC Meta-Orchestration**: Resource allocation and conflict resolution
- **Event-Driven Coordination**: Event cascading and ordering consistency
- **Error Handling**: Lifecycle failures and system stability

### 2. Infrastructure Integration Tests
- **Event Bus**: Schema validation, dead letter queue handling
- **Context Store**: Data persistence, expiration, querying
- **Schema Registry**: Schema registration, validation, evolution
- **Dead Letter Queue**: Message retry, failure handling
- **End-to-End Flow**: Complete event lifecycle with all components

### 3. CLI Integration Tests
- **Basic Commands**: Help, version, configuration validation
- **Status Management**: System status, lifecycle-specific status
- **Configuration**: Get/set values, backup/restore, validation
- **Health Checks**: Basic and deep health checks, automatic fixes
- **Event Monitoring**: Statistics, filtering, pattern matching
- **Deployment**: Planning, validation, dependency resolution
- **Lifecycle Management**: Start/stop operations, validation
- **Error Handling**: Invalid configurations, command options
- **Output Formatting**: JSON, table, no-color options

## Running Tests

### Prerequisites
- Node.js 18+ installed
- Docker (optional, for infrastructure tests)
- MOSAIC system components built

### Quick Start
```bash
# Install dependencies
npm install

# Run all tests
npm run test

# Run specific test suites
npm run test unit/
npm run test integration/

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

### Using the Test Runner Script
```bash
# Make executable
chmod +x run-integration-tests.sh

# Run all tests
./run-integration-tests.sh all

# Run specific test categories
./run-integration-tests.sh cross-lifecycle
./run-integration-tests.sh infrastructure
./run-integration-tests.sh cli

# Show help
./run-integration-tests.sh help
```

## Test Environment

### Environment Variables
- `NODE_ENV=test` - Test environment
- `MOSAIC_ENV=test` - MOSAIC test mode
- `MOSAIC_USE_MOCKS=true` - Use mock services
- `MOSAIC_TIMEOUT=5000` - Test timeout (ms)
- `MOSAIC_RETRY_ATTEMPTS=2` - Retry attempts

### Mock Services
When Docker is not available, tests automatically use mock implementations:
- Mock Event Bus (in-memory)
- Mock Context Store (in-memory)
- Mock Schema Registry
- Mock Infrastructure Services

### Real Infrastructure
When Docker is available, tests can use real services:
- Apache Kafka for event bus
- Redis for context store
- PostgreSQL for persistence
- Schema Registry for validation

## Test Results

### Current Status
✅ **Basic Test Framework**: Fully functional with Vitest
✅ **Environment Setup**: Test configuration and mocks working
✅ **CLI Testing**: Command validation and integration tests
✅ **Unit Testing**: Basic functionality validated

### Integration Test Coverage
- **Cross-Lifecycle Communication**: 8 test scenarios
- **Infrastructure Integration**: 12 test scenarios  
- **CLI Integration**: 15 test scenarios
- **Total Test Cases**: 35+ comprehensive integration tests

## Continuous Integration

### Test Automation
The test suite is designed for CI/CD integration:
- Automated dependency installation
- Environment detection (Docker availability)
- Service health checking
- Comprehensive reporting
- Cleanup and teardown

### Performance Metrics
- Test execution time: < 30 seconds
- Coverage reporting: HTML and JSON formats
- Memory usage monitoring
- Resource cleanup validation

## Troubleshooting

### Common Issues
1. **Docker not available**: Tests automatically fall back to mocks
2. **Port conflicts**: Tests use configurable ports with defaults
3. **Timeout issues**: Configurable timeouts for different environments
4. **Memory leaks**: Automatic cleanup in test teardown

### Debug Mode
```bash
# Enable verbose logging
DEBUG=mosaic:* npm run test

# Run single test file
npm run test integration/cli-integration.test.ts

# Skip setup/teardown
SKIP_SETUP=true npm run test
```

## Contributing

### Adding New Tests
1. Create test files in appropriate directories
2. Follow existing naming conventions
3. Use proper setup/teardown patterns
4. Include both positive and negative test cases
5. Add documentation for complex test scenarios

### Test Guidelines
- Use descriptive test names
- Group related tests in describe blocks
- Mock external dependencies appropriately
- Clean up resources in afterEach/afterAll
- Validate both success and error conditions

## Future Enhancements

### Planned Improvements
- [ ] Performance benchmarking tests
- [ ] Load testing for high-volume scenarios
- [ ] Security testing for authentication/authorization
- [ ] Cross-platform compatibility tests
- [ ] Visual regression testing for UI components
- [ ] API contract testing with OpenAPI specs

### Integration Targets
- [ ] GitHub Actions CI/CD pipeline
- [ ] SonarQube code quality analysis
- [ ] Allure test reporting
- [ ] Slack/Discord notifications
- [ ] Performance monitoring integration
