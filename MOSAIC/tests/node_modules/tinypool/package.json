{"name": "tinypool", "version": "0.8.4", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "type": "module", "main": "./dist/esm/index.js", "module": "./dist/esm/index.js", "types": "./dist/index.d.ts", "files": ["dist/**"], "packageManager": "pnpm@8.4.0", "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinypool.git"}, "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "homepage": "https://github.com/tinylibs/tinypool#readme", "engines": {"node": ">=14.0.0"}}