# Research Assistant Agent
**Advanced research and information synthesis for MOSAIC**

![Research Assistant Preview](preview.png)

## 🎯 Overview

The Research Assistant is a sophisticated knowledge agent that performs multi-source research, fact-checking, and information synthesis. It's designed to handle complex research queries with academic-level accuracy and comprehensive citation management.

## ✨ Features

- **Multi-Source Integration**: Aggregates information from multiple sources
- **Fact Verification**: Cross-references claims for accuracy
- **Citation Management**: Generates proper citations in multiple formats
- **Bias Detection**: Identifies and mitigates source bias
- **Summarization**: Configurable summary lengths and styles
- **Knowledge Graphs**: Builds conceptual relationships

## 🚀 Installation

```bash
# Using MOSAIC CLI
mosaic agent add research-assistant

# With custom configuration
mosaic agent add research-assistant --config research.json

# Manual installation
curl -O https://registry.mosaic.alias.dev/agents/research-assistant/agent.af
./scripts/agent-import.sh agent.af
```

## 📋 Configuration

### Basic Configuration
```json
{
  "searchDepth": 3,
  "citationStyle": "APA",
  "factCheckingLevel": "moderate",
  "summarizationLength": "medium"
}
```

### Advanced Configuration
```json
{
  "searchDepth": 5,
  "citationStyle": "APA",
  "factCheckingLevel": "strict",
  "summarizationLength": "detailed",
  "sources": {
    "academic": true,
    "news": true,
    "social": false,
    "trusted": ["nature.com", "sciencedirect.com"]
  },
  "biasDetection": {
    "enabled": true,
    "threshold": 0.7
  },
  "output": {
    "format": "markdown",
    "includeCitations": true,
    "includeConfidence": true
  }
}
```

## 🔧 Usage

### Basic Research Query
```typescript
const agent = await loadAgent('research-assistant');

const result = await agent.research({
  query: "Latest developments in quantum computing",
  depth: 2,
  sources: ['academic', 'news']
});

console.log(result.summary);
console.log(result.citations);
```

### Multi-Source Synthesis
```typescript
const synthesis = await agent.synthesize({
  topic: "Renewable energy storage solutions",
  perspectives: ['technological', 'economic', 'environmental'],
  compareAndContrast: true
});
```

### Academic Literature Review
```typescript
const review = await agent.literatureReview({
  topic: "Machine learning in healthcare diagnostics",
  yearRange: [2020, 2024],
  includeMetaAnalysis: true,
  citationStyle: "APA"
});
```

## 🤖 Quantum Commands

The Research Assistant supports the following quantum commands:

- `!RESEARCH <query>` - Perform research on a topic
- `!SYNTHESIZE <sources>` - Synthesize information from multiple sources
- `!FACT_CHECK <claim>` - Verify a specific claim
- `!CITE <content>` - Generate citations for content
- `!SUMMARIZE <text>` - Create summary of provided text
- `!BIAS_CHECK <source>` - Analyze source for bias

## 📊 Performance

### Benchmarks
- **Simple Query**: 0.8s average response time
- **Complex Research**: 3.5s average response time
- **Multi-Source Synthesis**: 5.2s average response time
- **Success Rate**: 94%
- **Accuracy Rate**: 89%

### Resource Usage
- **Memory**: ~500MB typical, 2GB max
- **CPU**: Low to moderate usage
- **API Calls**: 2-10 per query depending on depth

## 🔄 Integration

### With Document Analyzer
```typescript
// Chain with document analyzer for PDF research
const doc = await documentAnalyzer.analyze('research-paper.pdf');
const insights = await researchAssistant.synthesize({
  content: doc.extracted,
  context: doc.metadata,
  additionalResearch: true
});
```

### With Knowledge Curator
```typescript
// Build knowledge base from research
const research = await researchAssistant.research({
  query: "AI safety frameworks",
  depth: 5
});

await knowledgeCurator.ingest({
  content: research,
  category: "ai-safety",
  autoTag: true
});
```

## 🛠️ Customization

### Custom Sources
```typescript
agent.addSource({
  name: 'internal-docs',
  type: 'database',
  endpoint: 'https://internal.company.com/api',
  credentials: process.env.INTERNAL_API_KEY
});
```

### Custom Fact Checkers
```typescript
agent.addFactChecker({
  name: 'company-facts',
  check: async (claim) => {
    // Custom fact-checking logic
    return { verified: true, confidence: 0.95 };
  }
});
```

## 📈 Best Practices

1. **Source Configuration**: Configure trusted sources for your domain
2. **Caching**: Enable caching for repeated queries
3. **Batch Processing**: Use batch mode for multiple related queries
4. **Citation Management**: Always verify citations for academic use
5. **Bias Mitigation**: Enable bias detection for controversial topics

## 🚨 Limitations

- Maximum 50 sources per query
- API rate limits apply to external sources
- Some academic databases require additional authentication
- Real-time data may have 15-minute delay

## 🔐 Security

- All external API calls use secure connections
- Credentials are encrypted and stored securely
- No user queries are logged or stored
- Compliant with data protection regulations

## 🤝 Contributing

We welcome improvements to the Research Assistant! See our [Contributing Guide](../../CONTRIBUTING.md) for details.

### Areas for Contribution
- Additional citation formats
- New fact-checking sources
- Language support expansion
- Performance optimizations
- Integration examples

## 📞 Support

- **Documentation**: [Full Docs](https://docs.mosaic.alias.dev/agents/research-assistant)
- **Issues**: [GitLab Issues](https://gitlab.alias.dev/mosaic/agents/issues)
- **Community**: [Discord](https://discord.gg/mosaic-agents)

---

**Version**: 1.2.0  
**Domain**: KAD (Knowledge Architecture)  
**License**: MIT