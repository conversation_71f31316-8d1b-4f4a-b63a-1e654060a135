{"id": "research-assistant", "name": "Research Assistant", "description": "Advanced research and information synthesis agent with multi-source integration, fact-checking, and comprehensive citation management", "version": "1.2.0", "author": {"name": "ALIAS Team", "email": "<EMAIL>", "url": "https://alias.dev"}, "license": "MIT", "category": "knowledge", "tags": ["research", "analysis", "synthesis", "knowledge", "fact-checking", "citations", "multi-source"], "domain": {"primary": "KAD", "secondary": ["IKD", "MLD"], "capabilities": ["Multi-source research aggregation", "Fact verification and cross-referencing", "Citation generation (APA, MLA, Chicago)", "Summarization with configurable depth", "Bias detection and mitigation", "Knowledge graph construction"]}, "lifecycle": {"recommended": 3, "compatible": [2, 3, 4, 5], "stages": {"2": "Basic research tasks", "3": "Architecture and integration planning", "4": "Development and customization", "5": "Testing and validation"}}, "dependencies": {"required": {"models": ["gpt-4", "claude-3"], "services": ["vector-db"], "memory": "2GB"}, "optional": {"services": ["search-api", "academic-db", "news-api"], "agents": ["document-analyzer", "fact-checker"]}}, "configuration": {"searchDepth": {"type": "number", "default": 3, "min": 1, "max": 10, "description": "Number of search iterations"}, "citationStyle": {"type": "enum", "default": "APA", "options": ["APA", "MLA", "Chicago", "IEEE"], "description": "Citation format style"}, "factCheckingLevel": {"type": "enum", "default": "moderate", "options": ["basic", "moderate", "strict"], "description": "Fact verification stringency"}, "summarizationLength": {"type": "enum", "default": "medium", "options": ["brief", "medium", "detailed"], "description": "Summary output length"}}, "performance": {"metrics": {"avgResponseTime": "2.3s", "p95ResponseTime": "4.1s", "successRate": "94%", "accuracyRate": "89%"}, "benchmarks": {"simpleQuery": "0.8s", "complexResearch": "3.5s", "multiSourceSynthesis": "5.2s"}, "limits": {"maxSourcesPerQuery": 50, "maxConcurrentRequests": 10, "maxMemoryUsage": "2GB"}}, "pricing": {"model": "free", "premium": false, "apiCosts": {"estimated": "$0.02-0.10 per query", "factors": ["query complexity", "source count", "output length"]}}, "examples": [{"title": "Basic Research Query", "input": "What are the latest developments in quantum computing?", "outputPreview": "Recent quantum computing breakthroughs include..."}, {"title": "Multi-Source Synthesis", "input": "Compare different approaches to sustainable energy storage", "outputPreview": "Comparative analysis of energy storage technologies..."}, {"title": "Academic Research", "input": "Literature review on machine learning in healthcare", "outputPreview": "Systematic review of ML applications in healthcare..."}], "changelog": {"1.2.0": {"date": "2024-12-15", "changes": ["Added real-time fact-checking", "Improved citation accuracy", "Enhanced bias detection"]}, "1.1.0": {"date": "2024-11-20", "changes": ["Multi-language support", "Academic database integration", "Performance optimizations"]}, "1.0.0": {"date": "2024-10-01", "changes": ["Initial release", "Core research capabilities", "Basic citation support"]}}, "support": {"documentation": "https://docs.mosaic.alias.dev/agents/research-assistant", "issues": "https://gitlab.alias.dev/mosaic/agents/issues", "community": "https://community.alias.dev/agents/research-assistant"}, "installation": {"size": "4.2MB", "setupTime": "~2 minutes", "requirements": {"node": ">=20.0.0", "mosaic": ">=1.0.0"}}}