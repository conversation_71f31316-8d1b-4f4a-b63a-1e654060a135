# MOSAIC Agent Registry
**A curated collection of production-ready AI agents for the ALIAS MOSAIC platform**

## 🎯 Overview

The MOSAIC Agent Registry provides a shadcn-style component library for AI agents. Each agent is a carefully crafted, production-ready component that can be imported directly into your MOSAIC project with a single command.

## 🚀 Quick Start

```bash
# Install the MOSAIC CLI (if not already installed)
npm install -g @alias/mosaic-cli

# Browse available agents
mosaic agent browse

# Add an agent to your project
mosaic agent add research-assistant

# Add with specific configuration
mosaic agent add code-reviewer --domain SAD --lifecycle 4
```

## 📦 Featured Agents

### Knowledge & Research
- **research-assistant** - Advanced research and information synthesis
- **document-analyzer** - Document parsing and analysis
- **knowledge-curator** - Knowledge base management and curation

### Development & Engineering
- **code-reviewer** - Automated code review and suggestions
- **test-generator** - Intelligent test case generation
- **api-designer** - RESTful and GraphQL API design assistant

### Business & Operations
- **lead-qualifier** - Sales lead qualification and scoring
- **support-agent** - Customer support automation
- **analytics-reporter** - Business intelligence and reporting

### Integration & Automation
- **workflow-orchestrator** - Multi-step process automation
- **data-migrator** - Intelligent data migration and transformation
- **integration-bridge** - Third-party service integration

## 🏗️ Registry Structure

```
registry/
├── agents/
│   ├── research-assistant/
│   │   ├── agent.af              # Letta agent file
│   │   ├── metadata.json         # Registry metadata
│   │   ├── README.md            # Agent documentation
│   │   ├── preview.png          # Visual preview
│   │   └── examples/            # Usage examples
│   ├── code-reviewer/
│   └── ...
├── categories.json              # Agent categorization
├── registry.json               # Master registry index
└── schema/                     # Registry schemas
```

## 🔄 How It Works

### 1. Agent Discovery
```typescript
// Browse agents by category
const agents = await registry.browse({
  category: 'development',
  domain: 'SAD',
  sort: 'popular'
});

// Search for specific capabilities
const results = await registry.search('code review typescript');
```

### 2. Agent Installation
```typescript
// Install agent with automatic configuration
await registry.install('research-assistant', {
  projectPath: './my-project',
  configure: true,
  runSetup: true
});
```

### 3. Agent Customization
```typescript
// Agents are fully customizable after installation
const agent = await loadAgent('research-assistant');
agent.customize({
  prompts: { /* custom prompts */ },
  tools: { /* additional tools */ },
  memory: { /* memory configuration */ }
});
```

## 📋 Agent Metadata Schema

```json
{
  "id": "research-assistant",
  "name": "Research Assistant",
  "description": "Advanced research and information synthesis agent",
  "version": "1.2.0",
  "author": "ALIAS Team",
  "license": "MIT",
  "category": "knowledge",
  "tags": ["research", "analysis", "synthesis", "knowledge"],
  "domain": {
    "primary": "KAD",
    "secondary": ["IKD", "MLD"]
  },
  "lifecycle": {
    "recommended": 3,
    "compatible": [2, 3, 4, 5]
  },
  "dependencies": {
    "models": ["gpt-4", "claude-3"],
    "services": ["vector-db", "search-api"],
    "agents": ["document-analyzer"]
  },
  "performance": {
    "avgResponseTime": "2.3s",
    "successRate": "94%",
    "userRating": 4.8
  },
  "pricing": {
    "model": "free",
    "premium": false
  }
}
```

## 🎨 Creating Registry Agents

### Agent Submission Process

1. **Develop Your Agent**
   ```bash
   # Use the agent template
   mosaic agent create my-agent --template registry
   ```

2. **Test and Validate**
   ```bash
   # Run registry validation
   mosaic registry validate ./my-agent
   ```

3. **Submit for Review**
   ```bash
   # Submit to registry
   mosaic registry submit ./my-agent
   ```

### Quality Standards

#### Required
- ✅ Complete .af file with all state components
- ✅ Comprehensive documentation
- ✅ Working examples
- ✅ Test coverage >80%
- ✅ Performance benchmarks
- ✅ Security review passed

#### Recommended
- 📸 Visual preview/demo
- 🎥 Video walkthrough
- 📊 Performance metrics
- 🔧 Configuration templates
- 🌐 Multi-language support

## 🛠️ CLI Commands

### Browse and Search
```bash
# List all agents
mosaic agent list

# Browse by category
mosaic agent browse --category development

# Search agents
mosaic agent search "code review"

# Get agent details
mosaic agent info research-assistant
```

### Installation
```bash
# Basic installation
mosaic agent add research-assistant

# With options
mosaic agent add code-reviewer \
  --path ./agents \
  --domain SAD \
  --no-install

# Batch installation
mosaic agent add research-assistant code-reviewer test-generator
```

### Management
```bash
# Update agent
mosaic agent update research-assistant

# Remove agent
mosaic agent remove research-assistant

# List installed agents
mosaic agent installed

# Check for updates
mosaic agent outdated
```

## 🔌 API Access

### REST API
```bash
# Get registry index
GET https://registry.mosaic.alias.dev/api/v1/agents

# Get agent details
GET https://registry.mosaic.alias.dev/api/v1/agents/research-assistant

# Download agent file
GET https://registry.mosaic.alias.dev/api/v1/agents/research-assistant/download
```

### TypeScript SDK
```typescript
import { MosaicRegistry } from '@alias/mosaic-registry';

const registry = new MosaicRegistry();

// Browse agents
const agents = await registry.browse({
  category: 'development',
  limit: 10
});

// Install agent
await registry.install('code-reviewer', {
  projectPath: '.',
  configure: true
});
```

## 📊 Registry Statistics

### Current Status
- **Total Agents**: 47
- **Categories**: 8
- **Total Downloads**: 12,453
- **Active Contributors**: 23
- **Average Rating**: 4.6/5

### Popular Agents
1. **research-assistant** - 3,241 downloads
2. **code-reviewer** - 2,856 downloads
3. **test-generator** - 2,102 downloads
4. **support-agent** - 1,543 downloads
5. **workflow-orchestrator** - 1,298 downloads

## 🤝 Contributing

We welcome contributions! See our [Contributing Guide](CONTRIBUTING.md) for details.

### Ways to Contribute
- 🤖 Submit new agents
- 📝 Improve documentation
- 🐛 Report issues
- ⭐ Star and share
- 💡 Suggest features

## 🔐 Security

All agents undergo security review before inclusion:
- Static analysis for vulnerabilities
- Dependency scanning
- Prompt injection testing
- Data handling validation
- API key management review

## 📄 License

The MOSAIC Agent Registry is open source under the MIT license. Individual agents may have their own licenses specified in their metadata.

---

**Status**: 🚀 Live  
**Version**: 1.0.0  
**API**: https://registry.mosaic.alias.dev  
**Questions?** <EMAIL>