{"version": "1.0.0", "updated": "2024-12-30T00:00:00Z", "agents": [{"id": "research-assistant", "name": "Research Assistant", "description": "Advanced research agent with multi-source synthesis and fact-checking", "category": "knowledge", "domain": "KAD", "version": "1.2.0", "downloads": 3241, "rating": 4.8, "featured": true}, {"id": "code-reviewer", "name": "Code Reviewer", "description": "Intelligent code review with security scanning and best practices", "category": "development", "domain": "SAD", "version": "2.0.1", "downloads": 2856, "rating": 4.9, "featured": true}, {"id": "test-generator", "name": "Test Generator", "description": "Automated test case generation with coverage optimization", "category": "development", "domain": "SAD", "version": "1.5.0", "downloads": 2102, "rating": 4.7, "featured": false}, {"id": "lead-qualifier", "name": "Lead Qualifier", "description": "B2B lead qualification with predictive scoring", "category": "business", "domain": "MCD", "version": "1.1.0", "downloads": 1789, "rating": 4.6, "featured": true}, {"id": "support-agent", "name": "Support Agent", "description": "Customer support automation with sentiment analysis", "category": "business", "domain": "KAD", "version": "1.8.2", "downloads": 1543, "rating": 4.5, "featured": false}, {"id": "workflow-orchestrator", "name": "Workflow Orchestrator", "description": "Multi-step process automation with conditional logic", "category": "automation", "domain": "TKD", "version": "2.1.0", "downloads": 1298, "rating": 4.9, "featured": true}, {"id": "document-analyzer", "name": "Document Analyzer", "description": "Intelligent document parsing with entity extraction", "category": "knowledge", "domain": "IKD", "version": "1.3.0", "downloads": 1156, "rating": 4.7, "featured": false}, {"id": "api-designer", "name": "API Designer", "description": "REST and GraphQL API design with schema generation", "category": "development", "domain": "SAD", "version": "1.0.5", "downloads": 987, "rating": 4.8, "featured": false}, {"id": "data-migrator", "name": "Data Migrator", "description": "Intelligent data transformation and migration", "category": "automation", "domain": "UPD", "version": "1.4.0", "downloads": 876, "rating": 4.6, "featured": false}, {"id": "analytics-reporter", "name": "Analytics Reporter", "description": "Business intelligence with natural language insights", "category": "business", "domain": "ESD", "version": "1.2.3", "downloads": 743, "rating": 4.5, "featured": false}], "categories": {"knowledge": {"name": "Knowledge & Research", "description": "Agents for research, analysis, and knowledge management", "icon": "🔍"}, "development": {"name": "Development & Engineering", "description": "Code review, testing, and development automation", "icon": "💻"}, "business": {"name": "Business & Operations", "description": "Sales, support, and business process automation", "icon": "💼"}, "automation": {"name": "Integration & Automation", "description": "Workflow orchestration and system integration", "icon": "🔄"}, "creative": {"name": "Creative & Content", "description": "Content generation and creative assistance", "icon": "🎨"}, "data": {"name": "Data & Analytics", "description": "Data processing, analysis, and insights", "icon": "📊"}, "security": {"name": "Security & Compliance", "description": "Security scanning and compliance automation", "icon": "🔒"}, "communication": {"name": "Communication & Collaboration", "description": "Team communication and collaboration tools", "icon": "💬"}}, "stats": {"totalAgents": 47, "totalDownloads": 12453, "activeContributors": 23, "averageRating": 4.6, "lastUpdated": "2024-12-30T00:00:00Z"}}