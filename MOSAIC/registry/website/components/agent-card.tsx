/**
 * Agent Card Component
 * 
 * Displays agent information in a shadcn-style card format
 * for the MOSAIC Agent Registry website.
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Star, Download, Code, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AgentCardProps {
  agent: {
    id: string;
    name: string;
    description: string;
    category: string;
    domain: string;
    version: string;
    downloads: number;
    rating: number;
    featured?: boolean;
  };
  className?: string;
  onInstall?: () => void;
  onViewDetails?: () => void;
}

const domainColors: Record<string, string> = {
  SAD: 'bg-blue-500',
  KAD: 'bg-green-500',
  IKD: 'bg-purple-500',
  MLD: 'bg-yellow-500',
  TKD: 'bg-orange-500',
  MCD: 'bg-red-500',
  ESD: 'bg-pink-500',
  UPD: 'bg-indigo-500',
};

const categoryIcons: Record<string, string> = {
  knowledge: '🔍',
  development: '💻',
  business: '💼',
  automation: '🔄',
  creative: '🎨',
  data: '📊',
  security: '🔒',
  communication: '💬',
};

export function AgentCard({ agent, className, onInstall, onViewDetails }: AgentCardProps) {
  return (
    <Card className={cn('hover:shadow-lg transition-shadow', className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="flex items-center gap-2">
              {agent.featured && <Star className="w-4 h-4 text-yellow-500 fill-yellow-500" />}
              {agent.name}
            </CardTitle>
            <CardDescription className="line-clamp-2">
              {agent.description}
            </CardDescription>
          </div>
          <span className="text-2xl" role="img" aria-label={agent.category}>
            {categoryIcons[agent.category] || '📦'}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2 mb-4">
          <Badge variant="secondary">{agent.category}</Badge>
          <Badge 
            className={cn('text-white', domainColors[agent.domain] || 'bg-gray-500')}
          >
            {agent.domain}
          </Badge>
          <Badge variant="outline">v{agent.version}</Badge>
        </div>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Download className="w-3 h-3" />
            <span>{agent.downloads.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-3 h-3" />
            <span>{agent.rating.toFixed(1)}/5</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button
          variant="default"
          size="sm"
          className="flex-1"
          onClick={onInstall}
        >
          <Code className="w-4 h-4 mr-2" />
          Install
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onViewDetails}
        >
          Details
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </CardFooter>
    </Card>
  );
}

export function AgentCardSkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className="space-y-2">
          <div className="h-5 w-32 bg-muted animate-pulse rounded" />
          <div className="h-4 w-full bg-muted animate-pulse rounded" />
          <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 mb-4">
          <div className="h-5 w-20 bg-muted animate-pulse rounded-full" />
          <div className="h-5 w-16 bg-muted animate-pulse rounded-full" />
          <div className="h-5 w-12 bg-muted animate-pulse rounded-full" />
        </div>
        <div className="flex justify-between">
          <div className="h-4 w-16 bg-muted animate-pulse rounded" />
          <div className="h-4 w-12 bg-muted animate-pulse rounded" />
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <div className="h-9 flex-1 bg-muted animate-pulse rounded" />
        <div className="h-9 w-24 bg-muted animate-pulse rounded" />
      </CardFooter>
    </Card>
  );
}