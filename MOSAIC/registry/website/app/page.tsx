/**
 * MOSAIC Agent Registry Homepage
 * 
 * Main landing page for the agent registry with search,
 * filtering, and featured agents display.
 */

'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, TrendingUp, Star, Package } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AgentCard, AgentCardSkeleton } from '@/components/agent-card';
import { InstallDialog } from '@/components/install-dialog';
import { motion } from 'framer-motion';

export default function RegistryHome() {
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDomain, setSelectedDomain] = useState('all');
  const [sortBy, setSortBy] = useState('downloads');
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showInstallDialog, setShowInstallDialog] = useState(false);

  useEffect(() => {
    fetchAgents();
  }, [selectedCategory, selectedDomain, sortBy]);

  const fetchAgents = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedDomain !== 'all') params.append('domain', selectedDomain);
      params.append('sort', sortBy);
      
      const response = await fetch(`/api/v1/agents?${params}`);
      const data = await response.json();
      setAgents(data.agents);
    } catch (error) {
      console.error('Failed to fetch agents:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAgents = agents.filter(agent =>
    agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    agent.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const featuredAgents = agents.filter(agent => agent.featured);
  const popularAgents = [...agents].sort((a, b) => b.downloads - a.downloads).slice(0, 6);
  const recentAgents = [...agents].sort((a, b) => b.version.localeCompare(a.version)).slice(0, 6);

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden border-b">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10" />
        <div className="container relative py-24 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-5xl font-bold tracking-tight mb-4">
              MOSAIC Agent Registry
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Production-ready AI agents for your MOSAIC projects. 
              Copy, paste, and customize in seconds.
            </p>
            <div className="flex gap-4 justify-center mb-12">
              <Button size="lg" onClick={() => document.getElementById('browse')?.scrollIntoView()}>
                Browse Agents
              </Button>
              <Button size="lg" variant="outline">
                Submit Agent
              </Button>
            </div>
          </motion.div>

          {/* Stats */}
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">47</div>
              <div className="text-muted-foreground">Total Agents</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">12.4k</div>
              <div className="text-muted-foreground">Downloads</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">4.6</div>
              <div className="text-muted-foreground">Avg Rating</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Search and Filters */}
      <section id="browse" className="container py-8 border-b">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search agents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="knowledge">Knowledge</SelectItem>
              <SelectItem value="development">Development</SelectItem>
              <SelectItem value="business">Business</SelectItem>
              <SelectItem value="automation">Automation</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedDomain} onValueChange={setSelectedDomain}>
            <SelectTrigger className="w-full md:w-32">
              <SelectValue placeholder="Domain" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="SAD">SAD</SelectItem>
              <SelectItem value="KAD">KAD</SelectItem>
              <SelectItem value="IKD">IKD</SelectItem>
              <SelectItem value="MLD">MLD</SelectItem>
              <SelectItem value="TKD">TKD</SelectItem>
              <SelectItem value="MCD">MCD</SelectItem>
              <SelectItem value="ESD">ESD</SelectItem>
              <SelectItem value="UPD">UPD</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full md:w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="downloads">Most Popular</SelectItem>
              <SelectItem value="rating">Highest Rated</SelectItem>
              <SelectItem value="recent">Recently Updated</SelectItem>
              <SelectItem value="name">Alphabetical</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </section>

      {/* Main Content */}
      <section className="container py-12">
        <Tabs defaultValue="all" className="space-y-8">
          <TabsList>
            <TabsTrigger value="all" className="gap-2">
              <Package className="w-4 h-4" />
              All Agents
            </TabsTrigger>
            <TabsTrigger value="featured" className="gap-2">
              <Star className="w-4 h-4" />
              Featured
            </TabsTrigger>
            <TabsTrigger value="popular" className="gap-2">
              <TrendingUp className="w-4 h-4" />
              Popular
            </TabsTrigger>
            <TabsTrigger value="recent" className="gap-2">
              <Filter className="w-4 h-4" />
              Recent
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {loading ? (
                Array.from({ length: 6 }).map((_, i) => (
                  <AgentCardSkeleton key={i} />
                ))
              ) : (
                filteredAgents.map((agent) => (
                  <motion.div
                    key={agent.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <AgentCard
                      agent={agent}
                      onInstall={() => {
                        setSelectedAgent(agent);
                        setShowInstallDialog(true);
                      }}
                      onViewDetails={() => window.location.href = `/agents/${agent.id}`}
                    />
                  </motion.div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="featured" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredAgents.map((agent) => (
                <AgentCard
                  key={agent.id}
                  agent={agent}
                  onInstall={() => {
                    setSelectedAgent(agent);
                    setShowInstallDialog(true);
                  }}
                  onViewDetails={() => window.location.href = `/agents/${agent.id}`}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="popular" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {popularAgents.map((agent) => (
                <AgentCard
                  key={agent.id}
                  agent={agent}
                  onInstall={() => {
                    setSelectedAgent(agent);
                    setShowInstallDialog(true);
                  }}
                  onViewDetails={() => window.location.href = `/agents/${agent.id}`}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="recent" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentAgents.map((agent) => (
                <AgentCard
                  key={agent.id}
                  agent={agent}
                  onInstall={() => {
                    setSelectedAgent(agent);
                    setShowInstallDialog(true);
                  }}
                  onViewDetails={() => window.location.href = `/agents/${agent.id}`}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </section>

      {/* Install Dialog */}
      {selectedAgent && (
        <InstallDialog
          agent={selectedAgent}
          open={showInstallDialog}
          onOpenChange={setShowInstallDialog}
        />
      )}
    </div>
  );
}